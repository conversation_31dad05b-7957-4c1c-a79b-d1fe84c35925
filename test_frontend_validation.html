<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Signal Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-section { margin: 20px 0; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Frontend Signal Validation Tests</h1>
    <div id="test-results"></div>

    <script>
        // Mock dashboard object with validation functions
        const dashboard = {
            validateBiasData(bias) {
                if (!bias || typeof bias !== 'object') {
                    return { direction: 'NEUTRAL', strength: 0, factors: [], reason: 'No data available' };
                }
                
                return {
                    direction: bias.direction || 'NEUTRAL',
                    strength: typeof bias.strength === 'number' ? bias.strength : 0,
                    factors: Array.isArray(bias.factors) ? bias.factors : [],
                    reason: bias.reason || 'No analysis available'
                };
            },

            validateConfidenceData(confidence) {
                if (!confidence || typeof confidence !== 'object') {
                    return { level: 'LOW', score: 0, factors: [] };
                }
                
                return {
                    level: confidence.level || 'LOW',
                    score: typeof confidence.score === 'number' ? confidence.score : 0,
                    factors: Array.isArray(confidence.factors) ? confidence.factors : []
                };
            },

            validatePriceTargetsData(priceTargets) {
                if (!priceTargets || typeof priceTargets !== 'object') {
                    return { long_target: null, short_target: null, target_reasoning: [] };
                }
                
                return {
                    long_target: typeof priceTargets.long_target === 'number' ? priceTargets.long_target : null,
                    short_target: typeof priceTargets.short_target === 'number' ? priceTargets.short_target : null,
                    long_stop: typeof priceTargets.long_stop === 'number' ? priceTargets.long_stop : null,
                    short_stop: typeof priceTargets.short_stop === 'number' ? priceTargets.short_stop : null,
                    target_reasoning: Array.isArray(priceTargets.target_reasoning) ? priceTargets.target_reasoning : []
                };
            }
        };

        function runTest(testName, testFunction) {
            try {
                const result = testFunction();
                if (result) {
                    addTestResult(testName, 'PASS', 'Test completed successfully');
                    return true;
                } else {
                    addTestResult(testName, 'FAIL', 'Test returned false');
                    return false;
                }
            } catch (error) {
                addTestResult(testName, 'FAIL', `Test threw error: ${error.message}`);
                return false;
            }
        }

        function addTestResult(testName, status, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status.toLowerCase()}`;
            resultDiv.innerHTML = `<strong>${testName}</strong>: ${status} - ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function testBiasValidation() {
            // Test with null/undefined
            let result = dashboard.validateBiasData(null);
            if (result.direction !== 'NEUTRAL' || result.strength !== 0) return false;

            // Test with invalid object
            result = dashboard.validateBiasData({});
            if (result.direction !== 'NEUTRAL') return false;

            // Test with valid data
            result = dashboard.validateBiasData({
                direction: 'BULLISH',
                strength: 75,
                factors: ['factor1'],
                reason: 'test reason'
            });
            if (result.direction !== 'BULLISH' || result.strength !== 75) return false;

            // Test with partial data
            result = dashboard.validateBiasData({ direction: 'BEARISH' });
            if (result.direction !== 'BEARISH' || result.strength !== 0) return false;

            return true;
        }

        function testConfidenceValidation() {
            // Test with null
            let result = dashboard.validateConfidenceData(null);
            if (result.level !== 'LOW' || result.score !== 0) return false;

            // Test with valid data
            result = dashboard.validateConfidenceData({
                level: 'HIGH',
                score: 85,
                factors: ['factor1', 'factor2']
            });
            if (result.level !== 'HIGH' || result.score !== 85) return false;

            return true;
        }

        function testPriceTargetsValidation() {
            // Test with null
            let result = dashboard.validatePriceTargetsData(null);
            if (result.long_target !== null || result.short_target !== null) return false;

            // Test with valid data
            result = dashboard.validatePriceTargetsData({
                long_target: 125.50,
                short_target: 120.25,
                target_reasoning: ['reason1']
            });
            if (result.long_target !== 125.50 || result.short_target !== 120.25) return false;

            // Test with invalid numbers
            result = dashboard.validatePriceTargetsData({
                long_target: 'invalid',
                short_target: null
            });
            if (result.long_target !== null || result.short_target !== null) return false;

            return true;
        }

        function testErrorHandling() {
            // Test that functions don't crash with various invalid inputs
            try {
                dashboard.validateBiasData('string');
                dashboard.validateBiasData(123);
                dashboard.validateBiasData([]);
                dashboard.validateConfidenceData('string');
                dashboard.validatePriceTargetsData(123);
                return true;
            } catch (error) {
                return false;
            }
        }

        // Run all tests
        window.onload = function() {
            addTestResult('Frontend Validation Tests', 'INFO', 'Starting test suite...');
            
            let passCount = 0;
            let totalTests = 0;

            totalTests++;
            if (runTest('Bias Data Validation', testBiasValidation)) passCount++;

            totalTests++;
            if (runTest('Confidence Data Validation', testConfidenceValidation)) passCount++;

            totalTests++;
            if (runTest('Price Targets Validation', testPriceTargetsValidation)) passCount++;

            totalTests++;
            if (runTest('Error Handling', testErrorHandling)) passCount++;

            // Summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-section';
            summaryDiv.innerHTML = `
                <h3>Test Summary</h3>
                <p><strong>Passed:</strong> ${passCount}/${totalTests}</p>
                <p><strong>Status:</strong> ${passCount === totalTests ? '✅ All tests passed!' : '❌ Some tests failed'}</p>
            `;
            document.getElementById('test-results').appendChild(summaryDiv);
        };
    </script>
</body>
</html>
