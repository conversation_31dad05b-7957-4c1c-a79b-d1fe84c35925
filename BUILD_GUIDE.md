# Greek Terminal - Executable Build Guide

This guide explains how to compile your own executables for Greek Terminal.

## 🎯 Quick Start

### Option 1: Use the Build Script (Easiest)
```bash
# Windows
build.bat

# Or directly with Python
python build_executable.py admin
python build_executable.py license
```

### Option 2: Manual PyInstaller
```bash
pyinstaller dashboard.spec
```

## 📋 Prerequisites

1. **Python 3.8+** installed
2. **PyInstaller** installed: `pip install pyinstaller`
3. **All dependencies** installed: `pip install -r requirements.txt`

## 🔧 Build Types

### 1. Admin Version
- **Never expires**
- **Full admin access**
- **No trial limitations**

```bash
python build_executable.py admin --output-name "GreekTerminal-Admin"
```

### 2. License Version
- **Requires valid license key**
- **GitHub-based license validation**
- **Full feature access with valid license**

```bash
python build_executable.py license --output-name "GreekTerminal-License"
```

### 3. Custom Build
- **Fully customizable**
- **Mix and match features**

```bash
python build_executable.py custom \
  --admin-mode \
  --never-expires \
  --output-name "GreekTerminal-Custom" \
  --description "Custom admin build"
```

## 🛠️ Advanced Configuration

### Modify build_info.json Manually

Edit `build_info.json` before building:

```json
{
  "version": "license",
  "admin_mode": false,
  "build_type": "license",
  "never_expires": false,
  "description": "License-based version"
}
```

### Customize the Spec File

Edit `dashboard.spec` to modify:
- **Executable name**: Change `name='GreekTerminal'`
- **Icon**: Add `icon='path/to/icon.ico'`
- **Console mode**: Change `console=True/False`
- **Hidden imports**: Add to `hiddenimports` list

## 📁 Build Output

After building, you'll find:
- **Executable**: `dist/GreekTerminal.exe` (or custom name)
- **Build logs**: `build/` directory
- **Size**: Typically 150-200 MB

## 🔍 Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Add missing modules to `hiddenimports` in `dashboard.spec`
   - Install missing dependencies: `pip install module_name`

2. **Large executable size**
   - Normal for Python apps with many dependencies
   - Use `--exclude-module` to remove unused modules

3. **Slow startup**
   - First run extracts files (normal)
   - Subsequent runs are faster

4. **Missing files**
   - Add data files to `datas` in `dashboard.spec`
   - Check file paths are correct

### Debug Build Issues

```bash
# Verbose output
pyinstaller --log-level DEBUG dashboard.spec

# Check what's included
pyinstaller --analyze dashboard.spec
```

## 🎨 Customization Options

### Change Executable Icon
1. Get a `.ico` file
2. Edit `dashboard.spec`:
   ```python
   exe = EXE(
       # ... other parameters ...
       icon='path/to/your/icon.ico',
   )
   ```

### Hide Console Window
Edit `dashboard.spec`:
```python
exe = EXE(
    # ... other parameters ...
    console=False,  # Change to False
)
```

### Add Version Information
```python
# In dashboard.spec
version_info = (
    'version_info.txt',  # Create this file
    'version_info.txt'
)
```

## 📦 Distribution

### Single File Distribution
Your executable is self-contained and includes:
- Python interpreter
- All dependencies
- Your application code
- Static files and templates

### System Requirements
- **Windows 10/11** (64-bit)
- **No Python installation required**
- **~200MB disk space**

## 🔐 Security Considerations

### For License Builds
- Requires valid license key
- GitHub-based license validation
- Encrypted license storage

## 🚀 Automation

### Batch Build Multiple Versions
```bash
# Build all common versions
python build_executable.py admin --output-name "GT-Admin"
python build_executable.py license --output-name "GT-License"
```

### CI/CD Integration
Add to your build pipeline:
```yaml
- name: Build Executables
  run: |
    pip install pyinstaller
    python build_executable.py admin
    python build_executable.py license
```

## 📊 Build Statistics

Typical build times and sizes:
- **Build time**: 2-5 minutes
- **Executable size**: 150-200 MB
- **Startup time**: 3-10 seconds (first run)
- **Memory usage**: 100-300 MB

## 🆘 Support

If you encounter issues:
1. Check this guide first
2. Verify all dependencies are installed
3. Try a clean build: delete `build/` and `dist/` folders
4. Check PyInstaller documentation
5. Test with a simple Python script first

## 📝 Notes

- Builds are platform-specific (Windows .exe won't run on Mac/Linux)
- Antivirus software may flag executables (false positive)
- First run may be slower due to file extraction
- Keep source code secure - executables can be reverse-engineered
