#!/usr/bin/env python3
"""
Test script for SABR model implementation
This script validates the SABR model against known values and tests model switching.
"""

import sys
import os
import math
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sabr_model import SABRModel, sabr_delta, sabr_gamma, sabr_vega, sabr_theta, sabr_vanna, sabr_charm
from pricing_models import PricingModelManager, get_pricing_manager, set_pricing_model, set_sabr_parameters
from py_vollib.black_scholes.greeks.analytical import delta as bs_delta
from py_vollib.black_scholes.greeks.analytical import gamma as bs_gamma
from py_vollib.black_scholes.greeks.analytical import vega as bs_vega
from py_vollib.black_scholes.greeks.analytical import theta as bs_theta


def test_sabr_basic_functionality():
    """Test basic SABR model functionality"""
    print("=" * 60)
    print("Testing SABR Model Basic Functionality")
    print("=" * 60)
    
    # Test parameters
    S = 100.0  # Current price
    K = 100.0  # Strike price (ATM)
    T = 0.25   # 3 months
    r = 0.05   # 5% risk-free rate
    
    # SABR parameters
    alpha = 0.2
    beta = 0.5
    rho = -0.3
    nu = 0.4
    
    try:
        # Create SABR model
        sabr = SABRModel(alpha=alpha, beta=beta, rho=rho, nu=nu)
        print(f"✅ SABR model created successfully")
        print(f"   Parameters: α={alpha}, β={beta}, ρ={rho}, ν={nu}")
        
        # Test implied volatility calculation
        iv = sabr.implied_volatility(S, K, T)
        print(f"✅ ATM Implied Volatility: {iv:.4f}")
        
        # Test option pricing
        call_price = sabr.option_price(S, K, T, r, 'call')
        put_price = sabr.option_price(S, K, T, r, 'put')
        print(f"✅ Call Price: ${call_price:.4f}")
        print(f"✅ Put Price: ${put_price:.4f}")
        
        # Test Greeks
        delta_call = sabr.delta(S, K, T, r, 'call')
        delta_put = sabr.delta(S, K, T, r, 'put')
        gamma_val = sabr.gamma(S, K, T, r, 'call')
        vega_val = sabr.vega(S, K, T, r, 'call')
        theta_val = sabr.theta(S, K, T, r, 'call')
        vanna_val = sabr.vanna(S, K, T, r, 'call')
        charm_val = sabr.charm(S, K, T, r, 'call')
        
        print(f"✅ Greeks calculated:")
        print(f"   Delta (Call): {delta_call:.4f}")
        print(f"   Delta (Put): {delta_put:.4f}")
        print(f"   Gamma: {gamma_val:.6f}")
        print(f"   Vega: {vega_val:.4f}")
        print(f"   Theta: {theta_val:.4f}")
        print(f"   Vanna: {vanna_val:.6f}")
        print(f"   Charm: {charm_val:.6f}")
        
        # Sanity checks
        assert abs(delta_call - delta_put - 1.0) < 0.01, "Put-call parity for delta failed"
        assert gamma_val > 0, "Gamma should be positive"
        assert vega_val > 0, "Vega should be positive"
        assert theta_val < 0, "Theta should be negative for long options"
        
        print("✅ All basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ SABR basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_convenience_functions():
    """Test SABR convenience functions"""
    print("\n" + "=" * 60)
    print("Testing SABR Convenience Functions")
    print("=" * 60)
    
    # Test parameters
    S = 100.0
    K = 105.0  # OTM call
    T = 0.1    # 1 month
    r = 0.03
    
    try:
        # Test convenience functions
        delta_c = sabr_delta('c', S, K, T, r)
        delta_p = sabr_delta('p', S, K, T, r)
        gamma_val = sabr_gamma('c', S, K, T, r)
        vega_val = sabr_vega('c', S, K, T, r)
        theta_val = sabr_theta('c', S, K, T, r)
        vanna_val = sabr_vanna('c', S, K, T, r)
        charm_val = sabr_charm('c', S, K, T, r)
        
        print(f"✅ Convenience functions work:")
        print(f"   Delta (Call): {delta_c:.4f}")
        print(f"   Delta (Put): {delta_p:.4f}")
        print(f"   Gamma: {gamma_val:.6f}")
        print(f"   Vega: {vega_val:.4f}")
        print(f"   Theta: {theta_val:.4f}")
        print(f"   Vanna: {vanna_val:.6f}")
        print(f"   Charm: {charm_val:.6f}")
        
        print("✅ All convenience function tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pricing_model_manager():
    """Test the pricing model manager"""
    print("\n" + "=" * 60)
    print("Testing Pricing Model Manager")
    print("=" * 60)
    
    # Test parameters
    S = 100.0
    K = 100.0
    T = 0.25
    r = 0.05
    sigma = 0.2  # 20% volatility for Black-Scholes
    
    try:
        # Get pricing manager
        manager = get_pricing_manager()
        print(f"✅ Pricing manager created")
        
        # Test Black-Scholes mode
        manager.set_model('black_scholes')
        print(f"✅ Set to Black-Scholes model")
        
        bs_delta_val = manager.calculate_delta('c', S, K, T, r, sigma)
        bs_gamma_val = manager.calculate_gamma('c', S, K, T, r, sigma)
        bs_vega_val = manager.calculate_vega('c', S, K, T, r, sigma)
        bs_theta_val = manager.calculate_theta('c', S, K, T, r, sigma)
        bs_vanna_val = manager.calculate_vanna('c', S, K, T, r, sigma)
        bs_charm_val = manager.calculate_charm('c', S, K, T, r, sigma)
        
        print(f"   Black-Scholes Greeks:")
        print(f"   Delta: {bs_delta_val:.4f}")
        print(f"   Gamma: {bs_gamma_val:.6f}")
        print(f"   Vega: {bs_vega_val:.4f}")
        print(f"   Theta: {bs_theta_val:.4f}")
        print(f"   Vanna: {bs_vanna_val:.6f}")
        print(f"   Charm: {bs_charm_val:.6f}")
        
        # Test SABR mode
        manager.set_model('sabr')
        manager.set_sabr_params(alpha=0.2, beta=0.5, rho=-0.3, nu=0.4)
        print(f"✅ Set to SABR model")
        
        sabr_delta_val = manager.calculate_delta('c', S, K, T, r, sigma)
        sabr_gamma_val = manager.calculate_gamma('c', S, K, T, r, sigma)
        sabr_vega_val = manager.calculate_vega('c', S, K, T, r, sigma)
        sabr_theta_val = manager.calculate_theta('c', S, K, T, r, sigma)
        sabr_vanna_val = manager.calculate_vanna('c', S, K, T, r, sigma)
        sabr_charm_val = manager.calculate_charm('c', S, K, T, r, sigma)
        
        print(f"   SABR Greeks:")
        print(f"   Delta: {sabr_delta_val:.4f}")
        print(f"   Gamma: {sabr_gamma_val:.6f}")
        print(f"   Vega: {sabr_vega_val:.4f}")
        print(f"   Theta: {sabr_theta_val:.4f}")
        print(f"   Vanna: {sabr_vanna_val:.6f}")
        print(f"   Charm: {sabr_charm_val:.6f}")
        
        # Compare results
        print(f"\n   Comparison (SABR vs Black-Scholes):")
        print(f"   Delta diff: {abs(sabr_delta_val - bs_delta_val):.6f}")
        print(f"   Gamma diff: {abs(sabr_gamma_val - bs_gamma_val):.6f}")
        print(f"   Vega diff: {abs(sabr_vega_val - bs_vega_val):.6f}")
        print(f"   Theta diff: {abs(sabr_theta_val - bs_theta_val):.6f}")
        
        # Test model info
        model_info = manager.get_model_info()
        print(f"✅ Model info: {model_info}")
        
        print("✅ All pricing model manager tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Pricing model manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_global_functions():
    """Test global pricing model functions"""
    print("\n" + "=" * 60)
    print("Testing Global Pricing Model Functions")
    print("=" * 60)
    
    try:
        # Test setting pricing model
        set_pricing_model('black_scholes')
        current_model = get_pricing_manager().get_current_model()
        assert current_model == 'black_scholes', f"Expected black_scholes, got {current_model}"
        print(f"✅ Set pricing model to Black-Scholes")
        
        set_pricing_model('sabr')
        current_model = get_pricing_manager().get_current_model()
        assert current_model == 'sabr', f"Expected sabr, got {current_model}"
        print(f"✅ Set pricing model to SABR")
        
        # Test setting SABR parameters
        set_sabr_parameters(alpha=0.3, beta=0.7, rho=-0.5, nu=0.6)
        params = get_pricing_manager().get_sabr_params()
        assert params['alpha'] == 0.3, f"Expected alpha=0.3, got {params['alpha']}"
        assert params['beta'] == 0.7, f"Expected beta=0.7, got {params['beta']}"
        assert params['rho'] == -0.5, f"Expected rho=-0.5, got {params['rho']}"
        assert params['nu'] == 0.6, f"Expected nu=0.6, got {params['nu']}"
        print(f"✅ Set SABR parameters: {params}")
        
        print("✅ All global function tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Global function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n" + "=" * 60)
    print("Testing Edge Cases and Error Handling")
    print("=" * 60)
    
    try:
        # Test invalid SABR parameters
        try:
            SABRModel(alpha=-0.1, beta=0.5, rho=-0.3, nu=0.4)
            print("❌ Should have failed with negative alpha")
            return False
        except ValueError:
            print("✅ Correctly rejected negative alpha")
        
        try:
            SABRModel(alpha=0.2, beta=1.5, rho=-0.3, nu=0.4)
            print("❌ Should have failed with beta > 1")
            return False
        except ValueError:
            print("✅ Correctly rejected beta > 1")
        
        try:
            SABRModel(alpha=0.2, beta=0.5, rho=1.5, nu=0.4)
            print("❌ Should have failed with rho > 1")
            return False
        except ValueError:
            print("✅ Correctly rejected rho > 1")
        
        # Test zero time to expiry
        sabr = SABRModel()
        price = sabr.option_price(100, 100, 0, 0.05, 'call')
        assert price == 0, f"Expected 0 for zero time, got {price}"
        print("✅ Correctly handled zero time to expiry")
        
        # Test very short time to expiry
        price = sabr.option_price(100, 100, 1/365, 0.05, 'call')
        assert price >= 0, f"Price should be non-negative, got {price}"
        print("✅ Correctly handled very short time to expiry")
        
        print("✅ All edge case tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Edge case test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """Run all tests"""
    print("SABR Model Implementation Test Suite")
    print("=" * 60)
    
    tests = [
        test_sabr_basic_functionality,
        test_convenience_functions,
        test_pricing_model_manager,
        test_global_functions,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! SABR implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
