#!/usr/bin/env python3
"""
SABR (Stochastic Alpha Beta Rho) Option Pricing Model Implementation
This module provides SABR model calculations for option pricing and Greeks.
"""

import numpy as np
import math
from scipy.stats import norm
from scipy.optimize import minimize_scalar
import warnings

class SABRModel:
    """
    SABR (Stochastic Alpha Beta Rho) model implementation for option pricing.
    
    The SABR model is defined by the stochastic differential equations:
    dF_t = α_t * F_t^β * dW_1(t)
    dα_t = ν * α_t * dW_2(t)
    
    Where:
    - F_t is the forward price
    - α_t is the stochastic volatility
    - β is the CEV parameter (0 ≤ β ≤ 1)
    - ρ is the correlation between the two Brownian motions (-1 ≤ ρ ≤ 1)
    - ν is the volatility of volatility (vol-of-vol)
    """
    
    def __init__(self, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
        """
        Initialize SABR model with parameters.
        
        Args:
            alpha (float): Initial volatility parameter
            beta (float): CEV parameter (0 ≤ β ≤ 1)
            rho (float): Correlation parameter (-1 ≤ ρ ≤ 1)
            nu (float): Volatility of volatility parameter
        """
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.nu = nu
        
        # Validate parameters
        self._validate_parameters()
    
    def _validate_parameters(self):
        """Validate SABR model parameters."""
        if not (0 <= self.beta <= 1):
            raise ValueError(f"Beta must be between 0 and 1, got {self.beta}")
        if not (-1 <= self.rho <= 1):
            raise ValueError(f"Rho must be between -1 and 1, got {self.rho}")
        if self.alpha <= 0:
            raise ValueError(f"Alpha must be positive, got {self.alpha}")
        if self.nu < 0:
            raise ValueError(f"Nu must be non-negative, got {self.nu}")
    
    def implied_volatility(self, F, K, T, option_type='call'):
        """
        Calculate implied volatility using SABR model.
        
        Args:
            F (float): Forward price
            K (float): Strike price
            T (float): Time to expiration
            option_type (str): 'call' or 'put'
            
        Returns:
            float: Implied volatility
        """
        if T <= 0:
            return 0.0
        
        if abs(F - K) < 1e-8:  # ATM case
            return self._atm_implied_vol(F, T)
        else:
            return self._sabr_implied_vol(F, K, T)
    
    def _atm_implied_vol(self, F, T):
        """Calculate ATM implied volatility."""
        if self.beta == 1:
            # Log-normal case
            term1 = 1 + (self.nu**2 / 24) * T
            return self.alpha * term1
        else:
            # CEV case
            F_beta = F**(1 - self.beta)
            term1 = 1 + ((1 - self.beta)**2 / 24) * (self.alpha**2 / F_beta**2) * T
            term2 = (self.rho * self.nu * self.alpha) / (4 * F_beta) * T
            term3 = (2 - 3 * self.rho**2) * (self.nu**2 / 24) * T
            
            return (self.alpha / F_beta) * (term1 + term2 + term3)
    
    def _sabr_implied_vol(self, F, K, T):
        """Calculate implied volatility for non-ATM options."""
        try:
            # Optimization 21: Pre-calculate common terms to avoid repeated computation
            nu_over_alpha = self.nu / self.alpha
            one_minus_beta = 1 - self.beta

            # Calculate z and x(z)
            if self.beta == 1:
                # Log-normal case
                z = nu_over_alpha * math.log(F / K)
            else:
                # CEV case - calculate FK_beta once
                FK_beta = (F * K)**(one_minus_beta / 2)
                z = nu_over_alpha * FK_beta * (F**one_minus_beta - K**one_minus_beta) / one_minus_beta

            # Calculate x(z)
            if abs(z) < 1e-8:
                x_z = 1.0
            else:
                sqrt_term = math.sqrt(1 - 2 * self.rho * z + z**2)
                x_z = math.log((sqrt_term + z - self.rho) / (1 - self.rho)) / z

            # Calculate the main volatility term
            if self.beta == 1:
                # Log-normal case
                vol_main = self.alpha * x_z
            else:
                # CEV case - reuse FK_beta calculation
                FK_beta = (F * K)**(one_minus_beta / 2)
                vol_main = (self.alpha * x_z) / FK_beta

            # Optimization 22: Pre-calculate squared terms
            nu_squared = self.nu**2
            rho_squared = self.rho**2

            # Calculate correction terms
            if self.beta == 1:
                # Log-normal case
                A = 0
                B = nu_squared / 24
            else:
                # CEV case - reuse calculations
                FK_beta = (F * K)**(one_minus_beta / 2)
                one_minus_beta_squared = one_minus_beta**2
                alpha_squared = self.alpha**2

                A = (one_minus_beta_squared / 24) * (alpha_squared / (FK_beta**2))
                B = (self.rho * self.nu * self.alpha) / (4 * FK_beta) + (2 - 3 * rho_squared) * (nu_squared / 24)

            correction = 1 + (A + B) * T

            return vol_main * correction
            
        except (ValueError, ZeroDivisionError, OverflowError):
            # Fallback to ATM volatility if calculation fails
            warnings.warn("SABR volatility calculation failed, using ATM approximation")
            return self._atm_implied_vol(F, T)
    
    def option_price(self, S, K, T, r, option_type='call'):
        """
        Calculate option price using SABR model.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            option_type (str): 'call' or 'put'
            
        Returns:
            float: Option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        # Calculate forward price
        F = S * math.exp(r * T)
        
        # Get implied volatility from SABR model
        iv = self.implied_volatility(F, K, T, option_type)
        
        # Use Black-Scholes formula with SABR implied volatility
        return self._black_scholes_price(S, K, T, r, iv, option_type)
    
    def _black_scholes_price(self, S, K, T, r, sigma, option_type):
        """Calculate Black-Scholes price with given volatility."""
        if T <= 0 or sigma <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        if option_type.lower() == 'call':
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        
        return max(price, 0)
    
    def delta(self, S, K, T, r, option_type='call'):
        """Calculate delta using SABR model."""
        if T <= 0:
            if option_type.lower() == 'call':
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0
        
        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)
        
        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        
        if option_type.lower() == 'call':
            return norm.cdf(d1)
        else:
            return norm.cdf(d1) - 1
    
    def gamma(self, S, K, T, r, option_type='call'):
        """Calculate gamma using SABR model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)
        
        if iv <= 0:
            return 0.0
        
        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        
        return norm.pdf(d1) / (S * iv * math.sqrt(T))
    
    def vega(self, S, K, T, r, option_type='call'):
        """Calculate vega using SABR model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)
        
        if iv <= 0:
            return 0.0
        
        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        
        return S * norm.pdf(d1) * math.sqrt(T) / 100  # Divide by 100 for percentage point change
    
    def theta(self, S, K, T, r, option_type='call'):
        """Calculate theta using SABR model."""
        if T <= 0:
            return 0.0
        
        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)
        
        if iv <= 0:
            return 0.0
        
        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        d2 = d1 - iv * math.sqrt(T)
        
        if option_type.lower() == 'call':
            theta = (-S * norm.pdf(d1) * iv / (2 * math.sqrt(T)) 
                    - r * K * math.exp(-r * T) * norm.cdf(d2))
        else:
            theta = (-S * norm.pdf(d1) * iv / (2 * math.sqrt(T)) 
                    + r * K * math.exp(-r * T) * norm.cdf(-d2))
        
        return theta / 365  # Convert to daily theta

    def vanna(self, S, K, T, r, option_type='call'):
        """Calculate vanna (sensitivity of delta to volatility) using SABR model."""
        if T <= 0 or S <= 0:
            return 0.0

        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)

        if iv <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        d2 = d1 - iv * math.sqrt(T)

        # Vanna = ∂²V/∂S∂σ = -norm.pdf(d1) * d2 / σ
        return -norm.pdf(d1) * d2 / iv / 100  # Divide by 100 for percentage point change

    def charm(self, S, K, T, r, option_type='call'):
        """Calculate charm (sensitivity of delta to time) using SABR model."""
        if T <= 0 or S <= 0:
            return 0.0

        F = S * math.exp(r * T)
        iv = self.implied_volatility(F, K, T, option_type)

        if iv <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
        d2 = d1 - iv * math.sqrt(T)

        if option_type.lower() == 'call':
            # Charm for call = -norm.pdf(d1) * [2*r*T - d2*σ*√T] / (2*T*σ*√T)
            charm = -norm.pdf(d1) * (2 * r * T - d2 * iv * math.sqrt(T)) / (2 * T * iv * math.sqrt(T))
        else:
            # Charm for put has additional term
            charm = -norm.pdf(d1) * (2 * r * T - d2 * iv * math.sqrt(T)) / (2 * T * iv * math.sqrt(T))

        return charm / 365  # Convert to daily charm


# Default SABR model instance with reasonable parameters
default_sabr = SABRModel(alpha=0.2, beta=0.5, rho=-0.3, nu=0.4)


def sabr_option_price(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """
    Convenience function for SABR option pricing.
    
    Args:
        flag (str): 'c' for call, 'p' for put
        S (float): Current stock price
        K (float): Strike price
        T (float): Time to expiration
        r (float): Risk-free rate
        alpha (float): SABR alpha parameter
        beta (float): SABR beta parameter
        rho (float): SABR rho parameter
        nu (float): SABR nu parameter
        
    Returns:
        float: Option price
    """
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.option_price(S, K, T, r, option_type)


def sabr_delta(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR delta calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.delta(S, K, T, r, option_type)


def sabr_gamma(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR gamma calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.gamma(S, K, T, r, option_type)


def sabr_vega(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR vega calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.vega(S, K, T, r, option_type)


def sabr_theta(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR theta calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.theta(S, K, T, r, option_type)


def sabr_vanna(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR vanna calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.vanna(S, K, T, r, option_type)


def sabr_charm(flag, S, K, T, r, alpha=0.2, beta=0.5, rho=-0.3, nu=0.4):
    """Convenience function for SABR charm calculation."""
    sabr = SABRModel(alpha, beta, rho, nu)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return sabr.charm(S, K, T, r, option_type)
