#!/usr/bin/env python3
"""
Flask Web Application for Greek Terminal
Converted from PyQt6 desktop application to web interface
"""

import os
import json
import math
import numpy as np
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_socketio import SocketIO, emit
import threading
import time

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment loaded from .env file")
except ImportError:
    print("⚠️ python-dotenv not installed, using system environment variables")
except Exception as e:
    print(f"⚠️ Could not load .env file: {e}")

# Import configuration
from config import config

# Trial system removed - using license system only

# Import license system
try:
    from license_auth import init_license_auth, login_required, api_login_required, current_user, is_logged_in
    from license_admin import get_license_admin
    LICENSE_SYSTEM_AVAILABLE = True
    print("✅ License system loaded successfully")
except ImportError as e:
    print(f"Warning: License system not available: {e}")
    LICENSE_SYSTEM_AVAILABLE = False

    # Fallback functions
    def login_required(f):
        return f

    def api_login_required(f):
        return f

    def current_user():
        return None

    def is_logged_in():
        return True

# Import existing modules
from py_vollib.black_scholes.greeks.analytical import delta as bs_delta
from py_vollib.black_scholes.greeks.analytical import gamma as bs_gamma
from py_vollib.black_scholes.greeks.analytical import vega as bs_vega
from py_vollib.black_scholes.greeks.analytical import theta as bs_theta
import math
from scipy.stats import norm

# Import pricing models
from pricing_models import (get_pricing_manager, set_pricing_model, set_sabr_parameters,
                           set_heston_parameters, set_dupire_parameters, set_dupire_heston_parameters)

# Helper function from original dashboard
def safe_convert(value):
    """Convert numpy types to Python native types and handle NaN values for JSON serialization."""
    import math

    if isinstance(value, (np.integer, np.int64, np.int32, np.int16, np.int8)):
        return int(value)
    elif isinstance(value, (np.floating, np.float64, np.float32, np.float16)):
        # Handle NaN and infinity values
        float_val = float(value)
        if math.isnan(float_val) or math.isinf(float_val):
            return 0  # Convert NaN/Inf to 0 for JSON compatibility
        return float_val
    elif isinstance(value, (np.ndarray, list, tuple)):
        return [safe_convert(x) for x in value]
    elif isinstance(value, dict):
        return {safe_convert(k): safe_convert(v) for k, v in value.items()}
    elif value is None:
        return 0  # Convert None to 0 for consistency
    elif isinstance(value, float):
        # Handle regular Python float NaN values
        if math.isnan(value) or math.isinf(value):
            return 0
        return value
    else:
        return value

def calculate_d1(S, K, T, r, sigma):
    """Calculate d1 for Black-Scholes formula"""
    if T <= 0 or sigma <= 0:
        return 0
    return (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))

def calculate_d2(S, K, T, r, sigma):
    """Calculate d2 for Black-Scholes formula"""
    if T <= 0 or sigma <= 0:
        return 0
    d1 = calculate_d1(S, K, T, r, sigma)
    return d1 - sigma * math.sqrt(T)

def calculate_vanna(flag, S, K, T, r, sigma):
    """Calculate vanna (sensitivity of delta to volatility)"""
    try:
        if T <= 0 or sigma <= 0:
            return 0

        d1 = calculate_d1(S, K, T, r, sigma)
        d2 = calculate_d2(S, K, T, r, sigma)

        # Vanna = -e^(-rT) * phi(d2) * d1 / sigma
        # where phi is the standard normal PDF
        vanna = -math.exp(-r * T) * norm.pdf(d2) * d1 / sigma

        return vanna
    except Exception as e:
        print(f"Error calculating vanna: {e}")
        return 0

def calculate_charm(flag, S, K, T, r, sigma):
    """Calculate charm (sensitivity of delta to time)"""
    try:
        if T <= 0 or sigma <= 0:
            return 0

        d1 = calculate_d1(S, K, T, r, sigma)
        d2 = calculate_d2(S, K, T, r, sigma)

        if flag == 'c':  # Call option
            # Charm = -e^(-rT) * phi(d1) * [2*r*T - d2*sigma*sqrt(T)] / (2*T*sigma*sqrt(T))
            charm = -math.exp(-r * T) * norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))
        else:  # Put option
            # For puts, charm has additional term
            charm = -math.exp(-r * T) * norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))

        return charm
    except Exception as e:
        print(f"Error calculating charm: {e}")
        return 0

# Initialize Flask app with configuration
app = Flask(__name__)

# Load configuration
env = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[env])

# Initialize license authentication if available
if LICENSE_SYSTEM_AVAILABLE:
    license_auth = init_license_auth(app)
    print("✅ License authentication initialized")

# Initialize extensions
import sys
import os

# Check if running as PyInstaller bundle
if hasattr(sys, '_MEIPASS'):
    # Running as executable - use simple mode without real-time updates
    socketio = None
else:
    # Running as script - use simple SocketIO functionality
    try:
        # Use threading mode for better compatibility
        socketio = SocketIO(app,
                          cors_allowed_origins="*",
                          async_mode='threading',
                          logger=False,
                          engineio_logger=False,
                          ping_timeout=60,
                          ping_interval=25)
        print("✅ SocketIO initialized with threading mode")
    except Exception as e:
        print(f"❌ SocketIO initialization failed: {e}")
        socketio = None



# Global variables for data storage
current_data = {
    'ticker': 'SPY',
    'expiry_date': None,
    'current_price': None,
    'calls_df': pd.DataFrame(),
    'puts_df': pd.DataFrame(),
    'last_update': None
}

# Global variables for process tracking
background_threads = []
active_processes = []
cleanup_handlers = []

def register_background_thread(thread):
    """Register a background thread for cleanup"""
    global background_threads
    background_threads.append(thread)

def register_cleanup_handler(handler):
    """Register a cleanup handler function"""
    global cleanup_handlers
    cleanup_handlers.append(handler)

def cleanup_all_resources():
    """Clean up all registered resources"""
    global background_threads, active_processes, cleanup_handlers

    print("🔌 Running registered cleanup handlers...")
    for handler in cleanup_handlers:
        try:
            handler()
        except Exception as e:
            print(f"Cleanup handler failed: {e}")

    print("🔌 Stopping registered background threads...")
    for thread in background_threads:
        try:
            if thread.is_alive():
                print(f"🔌 Stopping thread: {thread.name}")
                # For daemon threads, they should stop when main process exits
                if hasattr(thread, '_stop'):
                    thread._stop()
        except Exception as e:
            print(f"Thread cleanup failed: {e}")

    # Clear the lists
    background_threads.clear()
    active_processes.clear()
    cleanup_handlers.clear()

class WebOptionsAnalyzer:
    """Web version of the options analyzer from the original dashboard"""

    def __init__(self):
        self.risk_free_rate = self.fetch_risk_free_rate()  # Auto-fetch current risk-free rate
        self.last_rate_update = datetime.now()
        self.rate_cache_hours = 24  # Cache rate for 24 hours

    def fetch_risk_free_rate(self):
        """Fetch current risk-free rate from Yahoo Finance (10-Year Treasury)"""
        try:
            print("🏦 Fetching current risk-free rate from Yahoo Finance...")

            # Use 10-Year Treasury Note (^TNX) as risk-free rate proxy
            treasury = yf.Ticker("^TNX")

            # Get current yield
            info = treasury.info
            current_yield = info.get('regularMarketPrice') or info.get('currentPrice')

            if current_yield and current_yield > 0:
                # Convert percentage to decimal (e.g., 4.5% -> 0.045)
                risk_free_rate = current_yield / 100.0
                print(f"✅ Current 10-Year Treasury yield: {current_yield:.3f}% (risk-free rate: {risk_free_rate:.4f})")
                return risk_free_rate
            else:
                # Fallback: try getting from historical data
                hist = treasury.history(period="1d")
                if not hist.empty:
                    current_yield = hist['Close'].iloc[-1]
                    risk_free_rate = current_yield / 100.0
                    print(f"✅ Current 10-Year Treasury yield (from history): {current_yield:.3f}% (risk-free rate: {risk_free_rate:.4f})")
                    return risk_free_rate
                else:
                    raise Exception("No Treasury data available")

        except Exception as e:
            print(f"⚠️ Error fetching risk-free rate: {e}")
            print("🔄 Using fallback risk-free rate: 5.0%")
            return 0.05  # Fallback to 5%

    def get_risk_free_rate(self):
        """Get current risk-free rate with caching"""
        # Check if we need to update the rate (cache expired)
        time_since_update = datetime.now() - self.last_rate_update
        if time_since_update.total_seconds() > (self.rate_cache_hours * 3600):
            print("🔄 Risk-free rate cache expired, fetching new rate...")
            self.risk_free_rate = self.fetch_risk_free_rate()
            self.last_rate_update = datetime.now()

        return self.risk_free_rate

    def get_current_price(self, ticker):
        """Get current price using Yahoo Finance"""
        try:
            # Use Yahoo Finance
            stock = yf.Ticker(ticker)
            info = stock.info
            price = info.get('regularMarketPrice') or info.get('currentPrice')
            return price, 'REGULAR'

        except Exception as e:
            print(f"Error getting current price: {e}")
            return None, 'ERROR'
    
    def get_expiry_dates(self, ticker):
        """Get available expiry dates for options"""
        try:
            print(f"Analyzer: Getting expiry dates for {ticker}")

            # Use Yahoo Finance for expiry dates
            print("Using Yahoo Finance for expiry dates")
            stock = yf.Ticker(ticker)
            options_dates = list(stock.options)
            print(f"Yahoo Finance returned {len(options_dates)} dates: {options_dates}")

            if not options_dates:
                print("No options dates found from Yahoo Finance")
                # Return some default dates for testing
                from datetime import datetime, timedelta
                today = datetime.now()
                default_dates = []
                for i in range(1, 8):  # Next 7 weeks
                    friday = today + timedelta(days=(4 - today.weekday()) + (i * 7))
                    default_dates.append(friday.strftime('%Y-%m-%d'))
                print(f"Returning default dates: {default_dates}")
                return default_dates

            return options_dates

        except Exception as e:
            print(f"Error getting expiry dates: {e}")
            import traceback
            traceback.print_exc()

            # Return some default dates as fallback
            from datetime import datetime, timedelta
            today = datetime.now()
            default_dates = []
            for i in range(1, 8):  # Next 7 weeks
                friday = today + timedelta(days=(4 - today.weekday()) + (i * 7))
                default_dates.append(friday.strftime('%Y-%m-%d'))
            print(f"Exception fallback - returning default dates: {default_dates}")
            return default_dates
    
    def get_options_data(self, ticker, expiry_date):
        """Get options chain data"""
        try:
            print(f"Analyzer: Getting options data for {ticker} {expiry_date}")

            # Use Yahoo Finance for options data
            print(f"Using Yahoo Finance for options data: {ticker} {expiry_date}")
            stock = yf.Ticker(ticker)

            try:
                options_chain = stock.option_chain(expiry_date)
                print("Yahoo Finance options_chain call successful")
            except Exception as e:
                print(f"Yahoo Finance options_chain failed: {e}")
                raise e

            calls_df = options_chain.calls
            puts_df = options_chain.puts

            print(f"Yahoo Finance raw data: {len(calls_df)} calls, {len(puts_df)} puts")

            # Get current price
            current_price, _ = self.get_current_price(ticker)
            print(f"Current price: {current_price}")

            if calls_df.empty and puts_df.empty:
                print("WARNING: No options data from Yahoo Finance")
                return pd.DataFrame(), pd.DataFrame(), current_price

            # Add calculated Greeks if not present
            print("Adding calculated Greeks...")
            calls_df = self.add_calculated_greeks(calls_df, current_price, expiry_date, 'call')
            puts_df = self.add_calculated_greeks(puts_df, current_price, expiry_date, 'put')

            print(f"Final data: {len(calls_df)} calls, {len(puts_df)} puts")
            return calls_df, puts_df, current_price

        except Exception as e:
            print(f"Error getting options data: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame(), pd.DataFrame(), None
    
    def add_calculated_greeks(self, df, current_price, expiry_date, option_type):
        """Add calculated Greeks to options dataframe"""
        if df.empty or not current_price:
            return df
        
        try:
            # Calculate time to expiration
            expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
            today = datetime.now()
            time_to_expiry = (expiry_dt - today).days / 365.0
            
            if time_to_expiry <= 0:
                time_to_expiry = 1/365.0  # Minimum 1 day
            
            # Calculate Greeks for each row
            greeks_data = []
            for _, row in df.iterrows():
                try:
                    strike = row['strike']
                    iv = row.get('impliedVolatility', 0.2)  # Default 20% IV
                    
                    if iv <= 0:
                        iv = 0.2
                    
                    # Calculate Greeks using the selected pricing model
                    flag = 'c' if option_type == 'call' else 'p'
                    current_rf_rate = self.get_risk_free_rate()

                    # Get the pricing model manager
                    pricing_manager = get_pricing_manager()

                    delta = pricing_manager.calculate_delta(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)
                    gamma = pricing_manager.calculate_gamma(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)
                    vega = pricing_manager.calculate_vega(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)
                    theta = pricing_manager.calculate_theta(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)
                    vanna = pricing_manager.calculate_vanna(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)
                    charm = pricing_manager.calculate_charm(flag, current_price, strike, time_to_expiry, current_rf_rate, iv)

                    greeks_data.append({
                        'calc_delta': delta,
                        'calc_gamma': gamma,
                        'calc_vega': vega / 100,  # Convert to per 1% change
                        'calc_theta': theta,
                        'calc_vanna': vanna / 100,  # Convert to per 1% change in volatility
                        'calc_charm': charm,  # Already in correct units (delta per day)
                        'moneyness': self.get_moneyness(current_price, strike, option_type)
                    })
                    
                except Exception as e:
                    print(f"Error calculating Greeks for strike {strike}: {e}")
                    greeks_data.append({
                        'calc_delta': 0,
                        'calc_gamma': 0,
                        'calc_vega': 0,
                        'calc_theta': 0,
                        'calc_vanna': 0,
                        'calc_charm': 0,
                        'moneyness': 'OTM'
                    })
            
            # Add Greeks to dataframe
            greeks_df = pd.DataFrame(greeks_data)
            df = pd.concat([df.reset_index(drop=True), greeks_df], axis=1)
            
            return df
            
        except Exception as e:
            print(f"Error adding calculated Greeks: {e}")
            return df
    
    def get_moneyness(self, current_price, strike, option_type):
        """Determine if option is ITM, ATM, or OTM"""
        threshold = 0.02  # 2% threshold for ATM
        
        if abs(current_price - strike) / current_price < threshold:
            return 'ATM'
        elif option_type == 'call':
            return 'ITM' if current_price > strike else 'OTM'
        else:  # put
            return 'ITM' if current_price < strike else 'OTM'

# Initialize analyzer
analyzer = WebOptionsAnalyzer()

# Trial system removed - using license system only

# SocketIO event handlers
if socketio:
    @socketio.on('connect')
    def handle_connect():
        print('Client connected')
        emit('status', {'msg': 'Connected to Greek Terminal'})

    @socketio.on('disconnect')
    def handle_disconnect():
        print('Client disconnected')

    @socketio.on('ping')
    def handle_ping():
        emit('pong')



@app.route('/')
def index():
    """Main dashboard page"""
    # Check license authentication if enabled
    if LICENSE_SYSTEM_AVAILABLE:
        if not is_logged_in():
            return redirect(url_for('login'))

# Trial system removed - license system only

    # Check if user has admin/developer privileges
    has_admin_access = False
    if LICENSE_SYSTEM_AVAILABLE:
        user_info = current_user()
        if user_info:
            username = user_info.get('username', '')

            # Check if user is admin
            admin_users = ['admin', 'administrator']
            is_admin = username in admin_users

            # Check if user is developer
            is_developer = False
            try:
                from developer_manager import get_developer_manager
                dev_manager = get_developer_manager()
                is_developer = dev_manager.is_developer(username)
            except Exception as e:
                print(f"Error checking developer status: {e}")

            has_admin_access = is_admin or is_developer

    return render_template('index.html', has_admin_access=has_admin_access)

@app.route('/api/socketio-status')
def socketio_status():
    """API endpoint to check if SocketIO is available"""
    return jsonify({
        'socketio_available': socketio is not None,
        'running_as_executable': hasattr(sys, '_MEIPASS'),
        'license_system_available': LICENSE_SYSTEM_AVAILABLE
    })

# Trial API endpoints removed - using license system only

@app.route('/api/expiry-dates/<ticker>')
@api_login_required
def get_expiry_dates(ticker):
    """API endpoint to get expiry dates for a ticker"""
    try:
        print(f"Getting expiry dates for {ticker}")
        expiry_dates = analyzer.get_expiry_dates(ticker.upper())
        print(f"Found {len(expiry_dates) if expiry_dates else 0} expiry dates: {expiry_dates}")

        if not expiry_dates:
            return jsonify({
                'success': False,
                'error': 'No expiry dates found for this ticker'
            })

        return jsonify({
            'success': True,
            'expiry_dates': expiry_dates
        })
    except Exception as e:
        print(f"Error getting expiry dates: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/current-price/<ticker>')
@api_login_required
def get_current_price(ticker):
    """API endpoint to get current price for a ticker"""
    try:
        price, market_state = analyzer.get_current_price(ticker.upper())
        return jsonify({
            'success': True,
            'price': price,
            'market_state': market_state
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/options-data/<ticker>/<expiry_date>')
@api_login_required
def get_options_data(ticker, expiry_date):
    """API endpoint to get options chain data"""
    try:
        print(f"=== FETCHING OPTIONS DATA ===")
        print(f"Ticker: {ticker}")
        print(f"Expiry: {expiry_date}")

        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        print(f"Retrieved data:")
        print(f"  - Calls: {len(calls_df)} rows")
        print(f"  - Puts: {len(puts_df)} rows")
        print(f"  - Current price: {current_price}")

        if calls_df.empty and puts_df.empty:
            print("ERROR: No options data retrieved!")
            return jsonify({
                'success': False,
                'error': f'No options data available for {ticker} on {expiry_date}'
            })

        # Clean DataFrames and convert to JSON-serializable format
        if not calls_df.empty:
            # Replace NaN values with 0 and convert timestamps to strings
            calls_df = calls_df.fillna(0)
            # Convert any datetime columns to strings
            for col in calls_df.columns:
                if calls_df[col].dtype == 'datetime64[ns, UTC]' or 'Timestamp' in str(type(calls_df[col].iloc[0])):
                    calls_df[col] = calls_df[col].astype(str)
            calls_data = calls_df.to_dict('records')
        else:
            calls_data = []

        if not puts_df.empty:
            # Replace NaN values with 0 and convert timestamps to strings
            puts_df = puts_df.fillna(0)
            # Convert any datetime columns to strings
            for col in puts_df.columns:
                if puts_df[col].dtype == 'datetime64[ns, UTC]' or 'Timestamp' in str(type(puts_df[col].iloc[0])):
                    puts_df[col] = puts_df[col].astype(str)
            puts_data = puts_df.to_dict('records')
        else:
            puts_data = []

        print(f"Converted to JSON:")
        print(f"  - Calls data: {len(calls_data)} records")
        print(f"  - Puts data: {len(puts_data)} records")

        # Apply safe_convert to handle numpy types and any remaining NaN
        calls_data = safe_convert(calls_data)
        puts_data = safe_convert(puts_data)

        # Debug: Check for any remaining problematic values
        try:
            import json
            json.dumps(calls_data[:1])  # Test first call record
            json.dumps(puts_data[:1])   # Test first put record
            print("✅ JSON serialization test passed")
        except Exception as json_error:
            print(f"❌ JSON serialization test failed: {json_error}")
            print(f"Sample calls data: {calls_data[:1] if calls_data else 'None'}")
            print(f"Sample puts data: {puts_data[:1] if puts_data else 'None'}")

        # Store in global data for real-time updates
        current_data.update({
            'ticker': ticker.upper(),
            'expiry_date': expiry_date,
            'current_price': current_price,
            'calls_df': calls_df,
            'puts_df': puts_df,
            'last_update': datetime.now().isoformat()
        })

        print(f"SUCCESS: Returning options data for {ticker}")

        return jsonify({
            'success': True,
            'calls': calls_data,
            'puts': puts_data,
            'current_price': current_price,
            'last_update': current_data['last_update']
        })
    except Exception as e:
        print(f"ERROR in get_options_data: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/all-exposures/<ticker>/<expiry_date>')
def get_all_exposures(ticker, expiry_date):
    """API endpoint to calculate all exposure types"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures
        exposure_data = calculate_all_exposures(calls_df, puts_df, current_price)

        return jsonify({
            'success': True,
            'exposure_data': safe_convert(exposure_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/risk-free-rate')
def get_risk_free_rate():
    """API endpoint to get current risk-free rate"""
    try:
        current_rate = analyzer.get_risk_free_rate()
        rate_percentage = current_rate * 100

        return jsonify({
            'success': True,
            'risk_free_rate': current_rate,
            'rate_percentage': round(rate_percentage, 3),
            'last_updated': analyzer.last_rate_update.isoformat(),
            'source': '10-Year Treasury (^TNX)',
            'cache_hours': analyzer.rate_cache_hours
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/gamma-exposure/<ticker>/<expiry_date>')
def get_gamma_exposure(ticker, expiry_date):
    """API endpoint to calculate gamma exposure (GEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate gamma exposure
        gex_data = calculate_gamma_exposure(calls_df, puts_df, current_price)

        return jsonify({
            'success': True,
            'gex_data': safe_convert(gex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/delta-exposure/<ticker>/<expiry_date>')
def get_delta_exposure(ticker, expiry_date):
    """API endpoint to calculate delta exposure (DEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract DEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        dex_data = [{
            'strike': data['strike'],
            'call_dex': data['call_dex'],
            'put_dex': data['put_dex'],
            'net_dex': data['net_dex']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'dex_data': safe_convert(dex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/volume-gamma/<ticker>/<expiry_date>')
def get_volume_gamma(ticker, expiry_date):
    """API endpoint to calculate volume gamma exposure (VGEX)"""
    try:
        print(f"VGEX request for {ticker} {expiry_date}")
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            print("No options data available for VGEX")
            return jsonify({'success': False, 'error': 'No options data available'})

        print(f"Options data loaded: {len(calls_df)} calls, {len(puts_df)} puts")

        # Calculate all exposures and extract VGEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        print(f"Calculated exposures for {len(all_exposures)} strikes")

        vgex_data = [{
            'strike': data['strike'],
            'call_vgex': data['call_vgex'],
            'put_vgex': data['put_vgex'],
            'net_vgex': data['net_vgex']
        } for data in all_exposures]

        print(f"VGEX data sample: {vgex_data[:3] if vgex_data else 'No data'}")

        return jsonify({
            'success': True,
            'vgex_data': safe_convert(vgex_data),
            'current_price': current_price
        })
    except Exception as e:
        print(f"VGEX error: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/theta-exposure/<ticker>/<expiry_date>')
def get_theta_exposure(ticker, expiry_date):
    """API endpoint to calculate theta exposure (TEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract TEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        tex_data = [{
            'strike': data['strike'],
            'call_tex': data['call_tex'],
            'put_tex': data['put_tex'],
            'net_tex': data['net_tex']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'tex_data': safe_convert(tex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/vega-exposure/<ticker>/<expiry_date>')
def get_vega_exposure(ticker, expiry_date):
    """API endpoint to calculate vega exposure (VEGX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract VEGX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        vegx_data = [{
            'strike': data['strike'],
            'call_vegx': data['call_vegx'],
            'put_vegx': data['put_vegx'],
            'net_vegx': data['net_vegx']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'vegx_data': safe_convert(vegx_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def calculate_all_exposures(calls_df, puts_df, current_price):
    """Calculate all exposure types (GEX, DEX, VGEX, TEX, VEGX, VEX, CEX, D_GEX) across strikes"""

    # Combine all strikes
    all_strikes = set()
    if not calls_df.empty:
        all_strikes.update(calls_df['strike'].values)
    if not puts_df.empty:
        all_strikes.update(puts_df['strike'].values)

    exposure_data = []

    for strike in sorted(all_strikes):
        strike_data = {'strike': strike}

        # Get call and put data for this strike
        call_row = calls_df[calls_df['strike'] == strike]
        put_row = puts_df[puts_df['strike'] == strike]

        # Initialize values
        call_data = call_row.iloc[0] if not call_row.empty else {}
        put_data = put_row.iloc[0] if not put_row.empty else {}

        # Extract Greeks and market data with safe defaults
        call_gamma = call_data.get('calc_gamma', 0) or 0
        call_delta = call_data.get('calc_delta', 0) or 0
        call_theta = call_data.get('calc_theta', 0) or 0
        call_vega = call_data.get('calc_vega', 0) or 0
        call_vanna = call_data.get('calc_vanna', 0) or 0
        call_charm = call_data.get('calc_charm', 0) or 0
        call_oi = call_data.get('openInterest', 0) or 0
        call_volume = call_data.get('volume', 0) or 0

        put_gamma = put_data.get('calc_gamma', 0) or 0
        put_delta = put_data.get('calc_delta', 0) or 0
        put_theta = put_data.get('calc_theta', 0) or 0
        put_vega = put_data.get('calc_vega', 0) or 0
        put_vanna = put_data.get('calc_vanna', 0) or 0
        put_charm = put_data.get('calc_charm', 0) or 0
        put_oi = put_data.get('openInterest', 0) or 0
        put_volume = put_data.get('volume', 0) or 0

        # Ensure numeric values and handle NaN
        def safe_numeric(value):
            try:
                if value is None or math.isnan(float(value)):
                    return 0
                return float(value)
            except (ValueError, TypeError):
                return 0

        call_gamma = safe_numeric(call_gamma)
        call_delta = safe_numeric(call_delta)
        call_theta = safe_numeric(call_theta)
        call_vega = safe_numeric(call_vega)
        call_vanna = safe_numeric(call_vanna)
        call_charm = safe_numeric(call_charm)
        call_oi = safe_numeric(call_oi)
        call_volume = safe_numeric(call_volume)

        put_gamma = safe_numeric(put_gamma)
        put_delta = safe_numeric(put_delta)
        put_theta = safe_numeric(put_theta)
        put_vega = safe_numeric(put_vega)
        put_vanna = safe_numeric(put_vanna)
        put_charm = safe_numeric(put_charm)
        put_oi = safe_numeric(put_oi)
        put_volume = safe_numeric(put_volume)

        # Calculate exposure using proper formula
        S = current_price

        # Dollar Gamma Exposure (per contract) = Option Gamma × Underlying Price² × Contract Size
        # For N contracts: Gamma Exposure = Γ × S² × 100 × N
        # Where:
        # - Γ = Option Gamma (change in delta per $1 move)
        # - S² = Underlying Price squared
        # - 100 = Contract Size (shares per contract)
        # - N = Number of contracts (Open Interest or Volume)

        contract_size = 100
        price_squared = S * S

        # 1. Gamma Exposure (GEX) - OI based
        # Gamma Exposure = Γ × S² × 100 × N (where N = Open Interest)
        call_gex = call_gamma * price_squared * contract_size * call_oi
        put_gex = -put_gamma * price_squared * contract_size * put_oi  # Negative for puts
        net_gex = call_gex + put_gex

        # 2. Volume Gamma Exposure (VGEX) - Volume based
        # Gamma Exposure = Γ × S² × 100 × N (where N = Volume)
        call_vgex = call_gamma * price_squared * contract_size * call_volume
        put_vgex = -put_gamma * price_squared * contract_size * put_volume  # Negative for puts
        net_vgex = call_vgex + put_vgex

        # 3. Delta Exposure (DEX) - OI based
        # Delta Exposure = Δ × S² × 100 × N (using same scaling as gamma)
        call_dex = call_delta * price_squared * contract_size * call_oi
        put_dex = put_delta * price_squared * contract_size * put_oi  # Put delta already negative
        net_dex = call_dex + put_dex

        # 4. Delta-Adjusted Gamma Exposure (D_GEX) - OI based
        # D_GEX = Γ × Δ × S² × 100 × N
        call_dgex = call_gamma * call_delta * price_squared * contract_size * call_oi
        put_dgex = put_gamma * put_delta * price_squared * contract_size * put_oi  # Put delta already negative
        net_dgex = call_dgex + put_dgex

        # 5. Theta Exposure (TEX) - OI based
        # Theta Exposure = Θ × S² × 100 × N
        call_tex = -call_theta * price_squared * contract_size * call_oi  # Invert to make calls positive
        put_tex = put_theta * price_squared * contract_size * put_oi  # Keep puts negative
        net_tex = call_tex + put_tex

        # 6. Vega Exposure (VEGX) - OI based
        # Vega Exposure = ν × S² × 100 × N
        call_vegx = call_vega * price_squared * contract_size * call_oi
        put_vegx = -put_vega * price_squared * contract_size * put_oi  # Negative for puts
        net_vegx = call_vegx + put_vegx

        # 7. Vanna Exposure (VEX) - OI based
        # Vanna Exposure = Vanna × S² × 100 × N
        call_vex = call_vanna * price_squared * contract_size * call_oi
        put_vex = put_vanna * price_squared * contract_size * put_oi
        net_vex = call_vex + put_vex

        # 8. Charm Exposure (CEX)
        # Charm Exposure = Charm × S² × 100 × N
        call_cex = call_charm * price_squared * contract_size * call_oi
        put_cex = put_charm * price_squared * contract_size * put_oi
        net_cex = call_cex + put_cex

        # Store all calculated exposures
        strike_data.update({
            # Gamma Exposure (GEX)
            'call_gex': call_gex,
            'put_gex': put_gex,
            'net_gex': net_gex,

            # Volume Gamma Exposure (VGEX)
            'call_vgex': call_vgex,
            'put_vgex': put_vgex,
            'net_vgex': net_vgex,

            # Delta Exposure (DEX)
            'call_dex': call_dex,
            'put_dex': put_dex,
            'net_dex': net_dex,

            # Delta-Adjusted Gamma Exposure (D_GEX)
            'call_dgex': call_dgex,
            'put_dgex': put_dgex,
            'net_dgex': net_dgex,

            # Theta Exposure (TEX)
            'call_tex': call_tex,
            'put_tex': put_tex,
            'net_tex': net_tex,

            # Vega Exposure (VEGX)
            'call_vegx': call_vegx,
            'put_vegx': put_vegx,
            'net_vegx': net_vegx,

            # Vanna Exposure (VEX)
            'call_vex': call_vex,
            'put_vex': put_vex,
            'net_vex': net_vex,

            # Charm Exposure (CEX)
            'call_cex': call_cex,
            'put_cex': put_cex,
            'net_cex': net_cex,

            # Open Interest
            'call_oi': call_oi,
            'put_oi': put_oi,
            'net_oi': call_oi - put_oi,  # Net OI (calls - puts)

            # Volume
            'call_volume': call_volume,
            'put_volume': put_volume,
            'net_volume': call_volume - put_volume  # Net volume (calls - puts)
        })

        exposure_data.append(strike_data)

    return exposure_data

def calculate_gamma_exposure(calls_df, puts_df, current_price):
    """Calculate gamma exposure across strikes (legacy function for compatibility)"""
    all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
    return [{
        'strike': data['strike'],
        'call_gex': data['call_gex'],
        'put_gex': data['put_gex'],
        'net_gex': data['net_gex']
    } for data in all_exposures]

@app.route('/api/vanna-exposure/<ticker>/<expiry_date>')
def get_vanna_exposure(ticker, expiry_date):
    """API endpoint to calculate vanna exposure (VEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract VEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        vex_data = [{
            'strike': data['strike'],
            'call_vex': data['call_vex'],
            'put_vex': data['put_vex'],
            'net_vex': data['net_vex']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'vex_data': safe_convert(vex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/charm-exposure/<ticker>/<expiry_date>')
def get_charm_exposure(ticker, expiry_date):
    """API endpoint to calculate charm exposure (CEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract CEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        cex_data = [{
            'strike': data['strike'],
            'call_cex': data['call_cex'],
            'put_cex': data['put_cex'],
            'net_cex': data['net_cex']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'cex_data': safe_convert(cex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/delta-gamma-exposure/<ticker>/<expiry_date>')
def get_delta_gamma_exposure(ticker, expiry_date):
    """API endpoint to calculate delta-adjusted gamma exposure (D_GEX)"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract D_GEX data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        dgex_data = [{
            'strike': data['strike'],
            'call_dgex': data['call_dgex'],
            'put_dgex': data['put_dgex'],
            'net_dgex': data['net_dgex']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'dgex_data': safe_convert(dgex_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/open-interest/<ticker>/<expiry_date>')
def get_open_interest(ticker, expiry_date):
    """API endpoint to get open interest data"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract OI data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        oi_data = [{
            'strike': data['strike'],
            'call_oi': data['call_oi'],
            'put_oi': data['put_oi'],
            'net_oi': data['net_oi']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'oi_data': safe_convert(oi_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/volume-data/<ticker>/<expiry_date>')
def get_volume_data(ticker, expiry_date):
    """API endpoint to get volume data"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures and extract volume data
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)
        volume_data = [{
            'strike': data['strike'],
            'call_volume': data['call_volume'],
            'put_volume': data['put_volume'],
            'net_volume': data['net_volume']
        } for data in all_exposures]

        return jsonify({
            'success': True,
            'volume_data': safe_convert(volume_data),
            'current_price': current_price
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/premium-analysis/<ticker>/<expiry_date>')
@api_login_required
def get_premium_analysis(ticker, expiry_date):
    """API endpoint to calculate net premium by strike (Top Player Positioning)"""
    try:
        print(f"=== FETCHING PREMIUM ANALYSIS ===")
        print(f"Ticker: {ticker}")
        print(f"Expiry: {expiry_date}")

        # Get view mode from query parameter (default to top10)
        view_mode = request.args.get('view_mode', 'top10')
        print(f"View mode: {view_mode}")

        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate premium spent: volume * lastPrice * contract size (100)
        calls_premium = calls_df.copy()
        puts_premium = puts_df.copy()

        # Handle missing volume data
        calls_premium['volume'] = calls_premium['volume'].fillna(0)
        puts_premium['volume'] = puts_premium['volume'].fillna(0)
        calls_premium['lastPrice'] = calls_premium['lastPrice'].fillna(0)
        puts_premium['lastPrice'] = puts_premium['lastPrice'].fillna(0)

        calls_premium['premium_spent'] = calls_premium['volume'] * calls_premium['lastPrice'] * 100
        puts_premium['premium_spent'] = puts_premium['volume'] * puts_premium['lastPrice'] * 100

        # Merge calls & puts on strike, fill missing with zero
        premium_df = pd.merge(
            calls_premium[['strike', 'premium_spent']],
            puts_premium[['strike', 'premium_spent']],
            on='strike',
            how='outer',
            suffixes=('_call', '_put')
        ).fillna(0)

        # Compute net premium (call minus put)
        premium_df['net_premium'] = premium_df['premium_spent_call'] - premium_df['premium_spent_put']

        # Filter based on view mode
        if view_mode == 'top10':
            # Filter to top 10 strikes by absolute net premium value
            premium_df['abs_net_premium'] = premium_df['net_premium'].abs()
            premium_df = premium_df.nlargest(10, 'abs_net_premium')
        # For 'all' mode, keep all strikes (no filtering)

        # Sort by strike for display
        premium_df = premium_df.sort_values('strike')

        # Convert to list of dictionaries for JSON response
        premium_data = []
        for _, row in premium_df.iterrows():
            premium_data.append({
                'strike': float(row['strike']),
                'call_premium': float(row['premium_spent_call']),
                'put_premium': float(row['premium_spent_put']),
                'net_premium': float(row['net_premium'])
            })

        print(f"Premium analysis completed: {len(premium_data)} strikes (mode: {view_mode})")

        return jsonify({
            'success': True,
            'premium_data': safe_convert(premium_data),
            'current_price': current_price,
            'view_mode': view_mode
        })
    except Exception as e:
        print(f"Error in premium analysis: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/gex-history/<ticker>/<expiry_date>')
def get_gex_history(ticker, expiry_date):
    """API endpoint to collect and return GEX history data for the visualizer"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)

        # Extract GEX data for OI and Volume
        gex_oi_data = []
        gex_vol_data = []

        for data in all_exposures:
            # OI-based GEX
            gex_oi_data.append({
                'strike': data['strike'],
                'gex': data['call_gex'] + data['put_gex']  # Net GEX
            })

            # Volume-based GEX
            gex_vol_data.append({
                'strike': data['strike'],
                'gex': data['call_vgex'] + data['put_vgex']  # Net Volume GEX
            })

        # Find min/max GEX levels for OI
        min_gex_oi = min(gex_oi_data, key=lambda x: x['gex']) if gex_oi_data else None
        max_gex_oi = max(gex_oi_data, key=lambda x: x['gex']) if gex_oi_data else None

        # Find min/max GEX levels for Volume
        min_gex_vol = min(gex_vol_data, key=lambda x: x['gex']) if gex_vol_data else None
        max_gex_vol = max(gex_vol_data, key=lambda x: x['gex']) if gex_vol_data else None

        # Calculate P/N ratios
        oi_ratio = calculate_pn_ratio(all_exposures, 'call_gex', 'put_gex')
        vol_ratio = calculate_pn_ratio(all_exposures, 'call_vgex', 'put_vgex')

        return jsonify({
            'success': True,
            'current_price': current_price,
            'timestamp': datetime.now().isoformat(),
            'oi_data': {
                'min_gex': min_gex_oi['gex'] if min_gex_oi else None,
                'max_gex': max_gex_oi['gex'] if max_gex_oi else None,
                'min_strike': min_gex_oi['strike'] if min_gex_oi else None,
                'max_strike': max_gex_oi['strike'] if max_gex_oi else None,
                'ratio': oi_ratio
            },
            'vol_data': {
                'min_gex': min_gex_vol['gex'] if min_gex_vol else None,
                'max_gex': max_gex_vol['gex'] if max_gex_vol else None,
                'min_strike': min_gex_vol['strike'] if min_gex_vol else None,
                'max_strike': max_gex_vol['strike'] if max_gex_vol else None,
                'ratio': vol_ratio
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Global variable to store exposure surface calculation status
exposure_surface_status = {}

@app.route('/api/gamma-surface-status/<ticker>/<expiry_date>')
def get_exposure_surface_status(ticker, expiry_date):
    """API endpoint to check exposure surface calculation status (for polling when SocketIO unavailable)"""
    try:
        key = f"{ticker.upper()}_{expiry_date}"
        status = exposure_surface_status.get(key, {})

        return jsonify({
            'success': True,
            'complete': status.get('complete', False),
            'progress': status.get('progress', 0),
            'message': status.get('message', 'Not started'),
            'surface_data': status.get('surface_data', []),
            'current_price': status.get('current_price', None),
            'error': status.get('error', None)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/volatility-surface/<ticker>')
def get_volatility_surface(ticker):
    """API endpoint to get volatility surface data across all expiration dates"""
    try:
        print(f"Getting volatility surface for {ticker}")

        # Get all expiry dates for the ticker
        expiry_dates = analyzer.get_expiry_dates(ticker.upper())
        if not expiry_dates:
            return jsonify({'success': False, 'error': 'No expiry dates available'})

        surface_data = []
        current_price = None

        for expiry_date in expiry_dates[:8]:  # Limit to first 8 expiries for performance
            try:
                calls_df, puts_df, price = analyzer.get_options_data(ticker.upper(), expiry_date)
                if current_price is None:
                    current_price = price

                if calls_df.empty and puts_df.empty:
                    continue

                # Calculate time to expiration in days
                expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
                today = datetime.now()
                days_to_expiry = (expiry_dt - today).days

                if days_to_expiry < 0:
                    continue  # Skip expired options

                # Process calls data
                for _, row in calls_df.iterrows():
                    iv = row.get('impliedVolatility', 0)
                    if iv > 0 and iv < 50:  # Relaxed filter for IV values (0-5000%) to include more data points
                        surface_data.append({
                            'strike': float(row['strike']),
                            'days_to_expiry': days_to_expiry,
                            'implied_volatility': float(iv),
                            'option_type': 'call',
                            'expiry_date': expiry_date
                        })

                # Process puts data
                for _, row in puts_df.iterrows():
                    iv = row.get('impliedVolatility', 0)
                    if iv > 0 and iv < 50:  # Relaxed filter for IV values (0-5000%) to include more data points
                        surface_data.append({
                            'strike': float(row['strike']),
                            'days_to_expiry': days_to_expiry,
                            'implied_volatility': float(iv),
                            'option_type': 'put',
                            'expiry_date': expiry_date
                        })

            except Exception as e:
                print(f"Error processing expiry {expiry_date}: {e}")
                continue

        if not surface_data:
            return jsonify({'success': False, 'error': 'No volatility data available'})

        return jsonify({
            'success': True,
            'surface_data': surface_data,
            'current_price': current_price,
            'expiry_dates': expiry_dates[:8]
        })

    except Exception as e:
        print(f"Error getting volatility surface: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/volatility-skew/<ticker>/<expiry_date>')
def get_volatility_skew(ticker, expiry_date):
    """API endpoint to get volatility skew for a specific expiration date"""
    try:
        print(f"Getting volatility skew for {ticker} {expiry_date}")

        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        skew_data = []

        # Process calls data
        for _, row in calls_df.iterrows():
            iv = row.get('impliedVolatility', 0)
            if iv > 0 and iv < 50:  # Relaxed filter for IV values (0-5000%) to include more data points
                # Handle NaN values
                volume = row.get('volume', 0)
                if pd.isna(volume) or volume != volume:  # Check for NaN
                    volume = 0

                open_interest = row.get('openInterest', 0)
                if pd.isna(open_interest) or open_interest != open_interest:  # Check for NaN
                    open_interest = 0

                skew_data.append({
                    'strike': float(row['strike']),
                    'implied_volatility': float(iv),
                    'option_type': 'call',
                    'moneyness': float(row['strike']) / current_price if current_price else 1,
                    'volume': float(volume),
                    'open_interest': float(open_interest)
                })

        # Process puts data
        for _, row in puts_df.iterrows():
            iv = row.get('impliedVolatility', 0)
            if iv > 0 and iv < 50:  # Relaxed filter for IV values (0-5000%) to include more data points
                # Handle NaN values
                volume = row.get('volume', 0)
                if pd.isna(volume) or volume != volume:  # Check for NaN
                    volume = 0

                open_interest = row.get('openInterest', 0)
                if pd.isna(open_interest) or open_interest != open_interest:  # Check for NaN
                    open_interest = 0

                skew_data.append({
                    'strike': float(row['strike']),
                    'implied_volatility': float(iv),
                    'option_type': 'put',
                    'moneyness': float(row['strike']) / current_price if current_price else 1,
                    'volume': float(volume),
                    'open_interest': float(open_interest)
                })

        # Sort by strike
        skew_data.sort(key=lambda x: x['strike'])

        if not skew_data:
            return jsonify({'success': False, 'error': 'No volatility skew data available'})

        return jsonify({
            'success': True,
            'skew_data': skew_data,
            'current_price': current_price,
            'expiry_date': expiry_date
        })

    except Exception as e:
        print(f"Error getting volatility skew: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/gamma-surface/<ticker>/<expiry_date>')
def get_exposure_surface(ticker, expiry_date):
    """API endpoint to start exposure surface calculation with user settings"""
    try:
        # Get user settings from query parameters
        price_range = float(request.args.get('price_range', 5))  # Default ±5%
        time_range = int(request.args.get('time_range', 120))    # Default 2 hours
        price_step = float(request.args.get('price_step', 1.0))  # Default $1.00
        time_step = int(request.args.get('time_step', 3))        # Default 3 minutes
        gex_type = request.args.get('gex_type', 'normal')        # Default normal (OI-based) GEX

        print(f"Starting exposure surface calculation for {ticker} {expiry_date}")
        print(f"Settings: ±{price_range}% range, {time_range}min time, ${price_step} price step, {time_step}min time step, {gex_type} exposure")

        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Create settings dictionary
        settings = {
            'price_range': price_range,
            'time_range': time_range,
            'price_step': price_step,
            'time_step': time_step,
            'gex_type': gex_type
        }

        # Start background calculation with user settings
        import threading
        calculation_thread = threading.Thread(
            target=calculate_exposure_surface_background,
            args=(calls_df, puts_df, current_price, ticker.upper(), expiry_date, settings),
            daemon=True
        )
        calculation_thread.start()

        # Return immediately - results will come via WebSocket
        return jsonify({
            'success': True,
            'message': 'Exposure surface calculation started',
            'ticker': ticker.upper(),
            'expiry_date': expiry_date,
            'settings': settings,
            'status': 'calculating'
        })

    except Exception as e:
        print(f"Error starting exposure surface calculation: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

def calculate_exposure_surface_background(calls_df, puts_df, current_price, ticker, expiry_date, settings=None):
    """Background calculation that sends results via WebSocket when complete"""
    global exposure_surface_status
    key = f"{ticker}_{expiry_date}"

    try:
        print(f"Background calculation started for {ticker} {expiry_date}")
        if settings:
            print(f"Using settings: {settings}")

        # Initialize status for polling
        exposure_surface_status[key] = {
            'complete': False,
            'progress': 0,
            'message': 'Starting calculation...',
            'surface_data': [],
            'current_price': current_price,
            'error': None
        }

        # Calculate the full surface with progress updates and user settings
        surface_data = calculate_exposure_surface_with_progress(calls_df, puts_df, current_price, ticker, expiry_date, settings)

        if surface_data and len(surface_data) > 0:
            # Update status for polling
            exposure_surface_status[key] = {
                'complete': True,
                'progress': 100,
                'message': f'Complete: {len(surface_data)} data points generated',
                'surface_data': surface_data,
                'current_price': current_price,
                'error': None
            }

            # Send completion via WebSocket (if available)
            if socketio:
                socketio.emit('gamma_surface_complete', {
                    'success': True,
                    'surface_data': surface_data,
                    'current_price': current_price,
                    'expiry_date': expiry_date,
                    'ticker': ticker,
                    'data_points': len(surface_data)
                })
            print(f"Background calculation completed: {len(surface_data)} data points")
        else:
            # Update status for polling
            exposure_surface_status[key] = {
                'complete': True,
                'progress': -1,
                'message': 'No exposure surface data generated',
                'surface_data': [],
                'current_price': current_price,
                'error': 'No exposure surface data generated'
            }

            # Send error via WebSocket (if available)
            if socketio:
                socketio.emit('gamma_surface_complete', {
                    'success': False,
                    'error': 'No exposure surface data generated',
                    'ticker': ticker,
                    'expiry_date': expiry_date
                })
            print("Background calculation failed: No data generated")

    except Exception as e:
        print(f"Error in background exposure surface calculation: {e}")
        import traceback
        traceback.print_exc()

        # Update status for polling
        exposure_surface_status[key] = {
            'complete': True,
            'progress': -1,
            'message': f'Calculation failed: {str(e)}',
            'surface_data': [],
            'current_price': current_price,
            'error': f'Calculation failed: {str(e)}'
        }

        # Send error via WebSocket (if available)
        if socketio:
            socketio.emit('gamma_surface_complete', {
                'success': False,
                'error': f'Calculation failed: {str(e)}',
                'ticker': ticker,
                'expiry_date': expiry_date
            })

def calculate_exposure_surface_with_progress(calls_df, puts_df, current_price, ticker, expiry_date, settings=None):
    """Calculate full exposure surface heatmap data with real-time progress updates"""
    try:
        print("Calculating exposure surface with user settings...")

        # Use user settings or defaults
        if settings:
            price_range_pct = settings.get('price_range', 5) / 100  # Convert % to decimal
            time_range_minutes = settings.get('time_range', 120)
            step = settings.get('price_step', 1.0)
            time_step_minutes = settings.get('time_step', 3)
            exposure_type = settings.get('gex_type', 'normal')  # Renamed for clarity
        else:
            # Default settings
            price_range_pct = 0.05  # ±5%
            time_range_minutes = 120  # 2 hours
            step = 1.0  # $1.00
            time_step_minutes = 3  # 3 minutes
            exposure_type = 'normal'

        # Apply limits for executable mode to prevent server crashes
        if hasattr(sys, '_MEIPASS'):  # Running as executable
            # More conservative settings for executable mode
            max_calculations = 10000  # Limit total calculations

            # Calculate current total
            price_levels_count = int((current_price * price_range_pct * 2) / step) + 1
            time_intervals_count = int(time_range_minutes / time_step_minutes) + 1
            total_calcs = price_levels_count * time_intervals_count

            if total_calcs > max_calculations:
                print(f"⚠️ Executable mode: Reducing calculation complexity from {total_calcs} to under {max_calculations}")

                # Reduce complexity by increasing step sizes
                if total_calcs > max_calculations * 2:
                    step = max(step * 2, 2.0)  # At least $2 steps
                    time_step_minutes = max(time_step_minutes * 2, 5)  # At least 5min intervals
                elif total_calcs > max_calculations * 1.5:
                    step = max(step * 1.5, 1.5)  # At least $1.50 steps
                    time_step_minutes = max(time_step_minutes * 1.5, 4)  # At least 4min intervals

                print(f"📉 Adjusted settings: price_step=${step}, time_step={time_step_minutes}min")

        # Calculate price range based on user selection
        range_low = round(current_price * (1 - price_range_pct))
        range_high = round(current_price * (1 + price_range_pct))

        # Create price levels (from high to low like in R code)
        price_levels = []
        level = range_high
        while level >= range_low:
            price_levels.append(level)
            level -= step

        # Time intervals based on user selection
        minutes_remaining = list(range(time_range_minutes, -1, -time_step_minutes))

        # Get current risk-free rate
        risk_free_rate = analyzer.get_risk_free_rate()

        total_calcs = len(price_levels) * len(minutes_remaining)
        print(f"Exposure surface calculation with user settings:")
        print(f"  Exposure Type: {exposure_type}")
        print(f"  Price range: ±{price_range_pct*100:.1f}% (${range_low} to ${range_high})")
        print(f"  Price step: ${step}")
        print(f"  Price levels: {len(price_levels)}")
        print(f"  Time range: {time_range_minutes} minutes")
        print(f"  Time step: {time_step_minutes} minutes")
        print(f"  Time intervals: {len(minutes_remaining)}")
        print(f"  Total calculations: {total_calcs}")

        # Send initial progress update
        if socketio:
            socketio.emit('gamma_surface_progress', {
                'progress': 0,
                'message': f'Starting calculation: {total_calcs:,} data points',
                'ticker': ticker,
                'expiry': expiry_date
            })

        # Filter options to the price range like in R code
        # Use volume or open interest based on exposure_type setting
        data_field = 'volume' if exposure_type == 'volume' else 'openInterest'

        if not calls_df.empty:
            calls_filtered = calls_df[
                (calls_df['strike'] >= range_low) &
                (calls_df['strike'] <= range_high) &
                (calls_df.get(data_field, 0) > 0)
            ].copy()
        else:
            calls_filtered = calls_df

        if not puts_df.empty:
            puts_filtered = puts_df[
                (puts_df['strike'] >= range_low) &
                (puts_df['strike'] <= range_high) &
                (puts_df.get(data_field, 0) > 0)
            ].copy()
        else:
            puts_filtered = puts_df

        print(f"  Filtered options: {len(calls_filtered)} calls, {len(puts_filtered)} puts")
        print(f"  Using {data_field} for {exposure_type} exposure calculations")

        # Pre-extract data for faster processing

        call_data = []
        if not calls_filtered.empty:
            for _, call in calls_filtered.iterrows():
                strike = call['strike']
                iv = call.get('impliedVolatility', 0.2)  # Default 20% IV
                data_value = call.get(data_field, 0)
                if iv > 0 and data_value > 0:
                    call_data.append((strike, iv, data_value))

        put_data = []
        if not puts_filtered.empty:
            for _, put in puts_filtered.iterrows():
                strike = put['strike']
                iv = put.get('impliedVolatility', 0.2)  # Default 20% IV
                data_value = put.get(data_field, 0)
                if iv > 0 and data_value > 0:
                    put_data.append((strike, iv, data_value))

        print(f"  Processing: {len(call_data)} calls, {len(put_data)} puts")

        # Send data preparation complete update
        if socketio:
            socketio.emit('gamma_surface_progress', {
                'progress': 5,
                'message': f'Data prepared: {len(call_data)} calls, {len(put_data)} puts',
                'ticker': ticker,
                'expiry': expiry_date
            })

        surface_data = []
        calc_count = 0
        last_progress_update = 0

        # VECTORIZED APPROACH - Calculate exposure surface using NumPy for massive speedup
        import numpy as np

        print("🚀 Using vectorized calculation for maximum performance...")

        # Pre-extract option data into arrays for vectorization
        if call_data:
            call_strikes = np.array([strike for strike, iv, data_val in call_data])
            call_ivs = np.array([iv for strike, iv, data_val in call_data])
            call_data_values = np.array([data_val for strike, iv, data_val in call_data])
        else:
            call_strikes = np.array([])
            call_ivs = np.array([])
            call_data_values = np.array([])

        if put_data:
            put_strikes = np.array([strike for strike, iv, data_val in put_data])
            put_ivs = np.array([iv for strike, iv, data_val in put_data])
            put_data_values = np.array([data_val for strike, iv, data_val in put_data])
        else:
            put_strikes = np.array([])
            put_ivs = np.array([])
            put_data_values = np.array([])

        # Calculate exposure surface for each time and price combination (vectorized)
        for t_idx, minutes_left in enumerate(minutes_remaining):
            # Convert minutes to trading years (like R code)
            time_to_expiry = minutes_left / (60 * 24 * 265)

            if time_to_expiry <= 0:
                # Add zero exposure for expired options
                for price_level in price_levels:
                    surface_data.append({
                        'price_level': price_level,
                        'minutes_remaining': minutes_left,
                        'call_gamma_exposure': 0.0,
                        'put_gamma_exposure': 0.0,  # Already zero, no need to negate
                        'net_gamma_exposure': 0.0,
                        'time_to_expiry': time_to_expiry
                    })
                    calc_count += 1
                continue

            for p_idx, price_level in enumerate(price_levels):
                call_gamma_exposure = 0.0
                put_gamma_exposure = 0.0

                # Vectorized exposure calculation based on exposure type
                if len(call_strikes) > 0:
                    call_S_array = np.full(len(call_strikes), price_level)
                    call_T_array = np.full(len(call_strikes), time_to_expiry)

                    if exposure_type in ['normal', 'volume']:
                        call_exposure = calculate_gamma_exposure_vectorized(
                            call_S_array, call_strikes, call_ivs, call_T_array,
                            risk_free_rate, 0, call_data_values
                        )
                    elif exposure_type == 'delta':
                        call_exposure = calculate_delta_exposure_vectorized(
                            call_S_array, call_strikes, call_ivs, call_T_array,
                            risk_free_rate, 0, call_data_values, 'call'
                        )
                    elif exposure_type == 'vanna':
                        call_exposure = calculate_vanna_exposure_vectorized(
                            call_S_array, call_strikes, call_ivs, call_T_array,
                            risk_free_rate, 0, call_data_values
                        )
                    elif exposure_type == 'charm':
                        call_exposure = calculate_charm_exposure_vectorized(
                            call_S_array, call_strikes, call_ivs, call_T_array,
                            risk_free_rate, 0, call_data_values, 'call'
                        )
                    else:
                        call_exposure = 0

                # Vectorized put exposure calculation
                if len(put_strikes) > 0:
                    put_S_array = np.full(len(put_strikes), price_level)
                    put_T_array = np.full(len(put_strikes), time_to_expiry)

                    if exposure_type in ['normal', 'volume']:
                        put_exposure = calculate_gamma_exposure_vectorized(
                            put_S_array, put_strikes, put_ivs, put_T_array,
                            risk_free_rate, 0, put_data_values
                        )
                    elif exposure_type == 'delta':
                        put_exposure = calculate_delta_exposure_vectorized(
                            put_S_array, put_strikes, put_ivs, put_T_array,
                            risk_free_rate, 0, put_data_values, 'put'
                        )
                    elif exposure_type == 'vanna':
                        put_exposure = calculate_vanna_exposure_vectorized(
                            put_S_array, put_strikes, put_ivs, put_T_array,
                            risk_free_rate, 0, put_data_values
                        )
                    elif exposure_type == 'charm':
                        put_exposure = calculate_charm_exposure_vectorized(
                            put_S_array, put_strikes, put_ivs, put_T_array,
                            risk_free_rate, 0, put_data_values, 'put'
                        )
                    else:
                        put_exposure = 0

                # Store separate call and put exposures
                # Put exposure should be negative for proper visualization
                surface_data.append({
                    'price_level': price_level,
                    'minutes_remaining': minutes_left,
                    'call_gamma_exposure': call_exposure,
                    'put_gamma_exposure': -put_exposure,  # Make puts negative (below zero)
                    'net_gamma_exposure': call_exposure - put_exposure,  # Net for reference
                    'time_to_expiry': time_to_expiry
                })

                calc_count += 1

                # Reduced CPU yielding since vectorization is much faster
                if calc_count % 200 == 0:
                    import time as time_module  # Use alias to avoid conflicts
                    time_module.sleep(0.001)  # 1ms sleep to yield CPU

                # Send progress updates every 2% or every 500 calculations (less frequent due to speed)
                progress = (calc_count / total_calcs) * 100
                if progress - last_progress_update >= 2 or calc_count % 500 == 0:
                    last_progress_update = progress

                    # Update status for polling
                    global exposure_surface_status
                    key = f"{ticker}_{expiry_date}"
                    if key in exposure_surface_status:
                        exposure_surface_status[key].update({
                            'progress': min(95, progress),  # Cap at 95% until complete
                            'message': f'Vectorized calc: {calc_count:,}/{total_calcs:,} points ({progress:.1f}%)'
                        })

                    # Send via WebSocket if available
                    if socketio:
                        import time as time_module  # Use alias to avoid conflicts
                        socketio.emit('gamma_surface_progress', {
                            'progress': min(95, progress),  # Cap at 95% until complete
                            'message': f'Vectorized calc: {calc_count:,}/{total_calcs:,} points ({progress:.1f}%)',
                            'ticker': ticker,
                            'expiry': expiry_date,
                            'timestamp': time_module.time()  # Add timestamp for heartbeat
                        })
                    print(f"  Vectorized Progress: {calc_count}/{total_calcs} ({progress:.1f}%)")

                # Send heartbeat every 200 calculations (less frequent due to speed)
                if calc_count % 200 == 0:
                    if socketio:
                        import time as time_module  # Use alias to avoid conflicts
                        socketio.emit('heartbeat', {
                            'timestamp': time_module.time(),
                            'calculation_active': True
                        })

                    # Yield CPU to prevent starving the main Flask thread
                    import time as time_module  # Use alias to avoid conflicts
                    time_module.sleep(0.001)  # 1ms sleep every 200 calculations

        # Send completion update
        if socketio:
            socketio.emit('gamma_surface_progress', {
                'progress': 100,
                'message': f'Complete: {len(surface_data):,} data points generated',
                'ticker': ticker,
                'expiry': expiry_date
            })

        print(f"🚀 Vectorized calculation completed! Generated {len(surface_data)} surface data points")
        print(f"⚡ Performance boost: Vectorized Black-Scholes calculations used")
        return surface_data

    except Exception as e:
        print(f"Error calculating exposure surface: {e}")
        if socketio:
            socketio.emit('gamma_surface_progress', {
                'progress': -1,
                'message': f'Error: {str(e)}',
                'ticker': ticker,
                'expiry': expiry_date
            })
        import traceback
        traceback.print_exc()
        return []

def calculate_exposure_surface(calls_df, puts_df, current_price):
    """Fallback function for compatibility"""
    return calculate_exposure_surface_with_progress(calls_df, puts_df, current_price, 'UNKNOWN', 'UNKNOWN')

def calculate_gamma_exposure_single(S, K, vol, T, r, q, OI):
    """Calculate gamma exposure for a single option using proper formula"""
    try:
        if T <= 0 or vol == 0:
            return 0

        # Black-Scholes d1 calculation
        d1 = (math.log(S/K) + T*(r - q + (vol**2)/2)) / (vol * math.sqrt(T))

        # Gamma calculation
        gamma = math.exp(-q*T) * norm.pdf(d1) / (S * vol * math.sqrt(T))

        # Dollar Gamma Exposure = Γ × S² × 100 × N
        # Where:
        # - Γ = Option Gamma (change in delta per $1 move)
        # - S² = Underlying Price squared
        # - 100 = Contract Size (shares per contract)
        # - N = Number of contracts (Open Interest)
        gamma_exposure = gamma * S * S * 100 * OI

        return gamma_exposure

    except Exception as e:
        print(f"Error calculating single gamma exposure: {e}")
        return 0

def calculate_gamma_exposure_vectorized(S_array, K_array, vol_array, T_array, r, q, OI_array):
    """
    Vectorized gamma calculation using the selected pricing model.
    Falls back to Black-Scholes for performance when using complex models.
    """
    try:
        import numpy as np

        # Get the current pricing model
        pricing_manager = get_pricing_manager()
        current_model = pricing_manager.get_current_model()

        # For performance, use Black-Scholes vectorization for complex models
        # Individual option calculations will still use the selected model
        if current_model in ['heston', 'dupire_heston']:
            # Use Black-Scholes vectorization for performance with complex models
            pass  # Continue with existing Black-Scholes calculation
        elif current_model == 'sabr':
            # For SABR, we could implement vectorized SABR, but for now use Black-Scholes
            # The individual Greeks calculations will use SABR
            pass
        elif current_model == 'dupire':
            # For Dupire, use Black-Scholes as approximation for vectorization
            pass

        # Convert to numpy arrays for vectorization
        S_arr = np.array(S_array, dtype=np.float64)
        K_arr = np.array(K_array, dtype=np.float64)
        vol_arr = np.array(vol_array, dtype=np.float64)
        T_arr = np.array(T_array, dtype=np.float64)
        OI_arr = np.array(OI_array, dtype=np.float64)

        # Filter out invalid values (T <= 0 or vol == 0)
        valid_mask = (T_arr > 0) & (vol_arr > 0) & (S_arr > 0) & (K_arr > 0)

        if not np.any(valid_mask):
            return 0.0

        # Apply mask to all arrays
        S_valid = S_arr[valid_mask]
        K_valid = K_arr[valid_mask]
        vol_valid = vol_arr[valid_mask]
        T_valid = T_arr[valid_mask]
        OI_valid = OI_arr[valid_mask]

        # Use Black-Scholes for vectorized calculation (performance optimization)
        # Note: Individual Greeks in the main calculations use the selected model
        d1 = (np.log(S_valid/K_valid) + T_valid*(r - q + (vol_valid**2)/2)) / (vol_valid * np.sqrt(T_valid))

        # Vectorized gamma calculation
        # Using fast normal PDF: 1/sqrt(2π) * e^(-x²/2)
        norm_pdf_d1 = 0.3989422804014327 * np.exp(-0.5 * d1 * d1)
        gamma = np.exp(-q*T_valid) * norm_pdf_d1 / (S_valid * vol_valid * np.sqrt(T_valid))

        # Vectorized gamma exposure calculation
        gamma_exposure = gamma * S_valid * S_valid * 100 * OI_valid

        # Sum all exposures and return
        return np.sum(gamma_exposure)

    except Exception as e:
        print(f"Error in vectorized gamma exposure calculation: {e}")
        return 0.0

def calculate_delta_exposure_vectorized(S_array, K_array, vol_array, T_array, r, q, OI_array, option_type='call'):
    """Vectorized Black-Scholes delta exposure calculation"""
    try:
        import numpy as np
        from scipy.stats import norm

        # Convert to numpy arrays for vectorization
        S_arr = np.array(S_array, dtype=np.float64)
        K_arr = np.array(K_array, dtype=np.float64)
        vol_arr = np.array(vol_array, dtype=np.float64)
        T_arr = np.array(T_array, dtype=np.float64)
        OI_arr = np.array(OI_array, dtype=np.float64)

        # Filter out invalid values
        valid_mask = (T_arr > 0) & (vol_arr > 0) & (S_arr > 0) & (K_arr > 0)

        if not np.any(valid_mask):
            return 0.0

        # Apply mask to all arrays
        S_valid = S_arr[valid_mask]
        K_valid = K_arr[valid_mask]
        vol_valid = vol_arr[valid_mask]
        T_valid = T_arr[valid_mask]
        OI_valid = OI_arr[valid_mask]

        # Vectorized Black-Scholes d1 calculation
        d1 = (np.log(S_valid/K_valid) + T_valid*(r - q + (vol_valid**2)/2)) / (vol_valid * np.sqrt(T_valid))

        # Vectorized delta calculation
        if option_type == 'call':
            delta = np.exp(-q*T_valid) * norm.cdf(d1)
        else:  # put
            delta = -np.exp(-q*T_valid) * norm.cdf(-d1)

        # Delta exposure calculation: Δ × S² × 100 × N
        delta_exposure = delta * S_valid * S_valid * 100 * OI_valid

        # Sum all exposures and return
        return np.sum(delta_exposure)

    except Exception as e:
        print(f"Error in vectorized delta exposure calculation: {e}")
        return 0.0

def calculate_vanna_exposure_vectorized(S_array, K_array, vol_array, T_array, r, q, OI_array):
    """Vectorized Black-Scholes vanna exposure calculation"""
    try:
        import numpy as np

        # Convert to numpy arrays for vectorization
        S_arr = np.array(S_array, dtype=np.float64)
        K_arr = np.array(K_array, dtype=np.float64)
        vol_arr = np.array(vol_array, dtype=np.float64)
        T_arr = np.array(T_array, dtype=np.float64)
        OI_arr = np.array(OI_array, dtype=np.float64)

        # Filter out invalid values
        valid_mask = (T_arr > 0) & (vol_arr > 0) & (S_arr > 0) & (K_arr > 0)

        if not np.any(valid_mask):
            return 0.0

        # Apply mask to all arrays
        S_valid = S_arr[valid_mask]
        K_valid = K_arr[valid_mask]
        vol_valid = vol_arr[valid_mask]
        T_valid = T_arr[valid_mask]
        OI_valid = OI_arr[valid_mask]

        # Vectorized Black-Scholes d1 and d2 calculation
        d1 = (np.log(S_valid/K_valid) + T_valid*(r - q + (vol_valid**2)/2)) / (vol_valid * np.sqrt(T_valid))
        d2 = d1 - vol_valid * np.sqrt(T_valid)

        # Vectorized vanna calculation
        # Vanna = ∂²V/∂S∂σ = -e^(-qT) * φ(d1) * d2 / σ
        norm_pdf_d1 = 0.3989422804014327 * np.exp(-0.5 * d1 * d1)
        vanna = -np.exp(-q*T_valid) * norm_pdf_d1 * d2 / vol_valid

        # Vanna exposure calculation: Vanna × S² × 100 × N
        vanna_exposure = vanna * S_valid * S_valid * 100 * OI_valid

        # Sum all exposures and return
        return np.sum(vanna_exposure)

    except Exception as e:
        print(f"Error in vectorized vanna exposure calculation: {e}")
        return 0.0

def calculate_charm_exposure_vectorized(S_array, K_array, vol_array, T_array, r, q, OI_array, option_type='call'):
    """Vectorized Black-Scholes charm exposure calculation"""
    try:
        import numpy as np
        from scipy.stats import norm

        # Convert to numpy arrays for vectorization
        S_arr = np.array(S_array, dtype=np.float64)
        K_arr = np.array(K_array, dtype=np.float64)
        vol_arr = np.array(vol_array, dtype=np.float64)
        T_arr = np.array(T_array, dtype=np.float64)
        OI_arr = np.array(OI_array, dtype=np.float64)

        # Filter out invalid values
        valid_mask = (T_arr > 0) & (vol_arr > 0) & (S_arr > 0) & (K_arr > 0)

        if not np.any(valid_mask):
            return 0.0

        # Apply mask to all arrays
        S_valid = S_arr[valid_mask]
        K_valid = K_arr[valid_mask]
        vol_valid = vol_arr[valid_mask]
        T_valid = T_arr[valid_mask]
        OI_valid = OI_arr[valid_mask]

        # Vectorized Black-Scholes d1 and d2 calculation
        d1 = (np.log(S_valid/K_valid) + T_valid*(r - q + (vol_valid**2)/2)) / (vol_valid * np.sqrt(T_valid))
        d2 = d1 - vol_valid * np.sqrt(T_valid)

        # Vectorized charm calculation
        # Charm = ∂²V/∂S∂T
        norm_pdf_d1 = 0.3989422804014327 * np.exp(-0.5 * d1 * d1)

        if option_type == 'call':
            charm = -np.exp(-q*T_valid) * norm_pdf_d1 * (2*(r-q)*T_valid - d2*vol_valid*np.sqrt(T_valid)) / (2*T_valid*vol_valid*np.sqrt(T_valid))
        else:  # put
            charm = np.exp(-q*T_valid) * norm_pdf_d1 * (2*(r-q)*T_valid - d2*vol_valid*np.sqrt(T_valid)) / (2*T_valid*vol_valid*np.sqrt(T_valid))

        # Charm exposure calculation: Charm × S² × 100 × N
        charm_exposure = charm * S_valid * S_valid * 100 * OI_valid

        # Sum all exposures and return
        return np.sum(charm_exposure)

    except Exception as e:
        print(f"Error in vectorized charm exposure calculation: {e}")
        return 0.0

def calculate_pn_ratio(exposures, call_field, put_field):
    """Calculate Positive/Negative ratio for exposure data"""
    try:
        positive_total = sum(data[call_field] for data in exposures if data[call_field] > 0)
        negative_total = abs(sum(data[put_field] for data in exposures if data[put_field] < 0))

        if negative_total != 0:
            return positive_total / negative_total
        else:
            return float('inf') if positive_total > 0 else 0
    except Exception as e:
        print(f"Error calculating P/N ratio: {e}")
        return 0

def find_zero_gex_trigger(net_exposures):
    """Find the strike price where exposure transitions from negative to positive (zero GEX trigger)"""
    try:
        if not net_exposures:
            return None

        # Convert to sorted list of (strike, exposure) tuples
        sorted_exposures = sorted(net_exposures.items())

        # Find the transition point from negative to positive
        for i in range(len(sorted_exposures) - 1):
            current_strike, current_exposure = sorted_exposures[i]
            next_strike, next_exposure = sorted_exposures[i + 1]

            # Check if we cross from negative to positive
            if current_exposure <= 0 and next_exposure > 0:
                # Linear interpolation to find the exact zero crossing point
                if current_exposure == next_exposure:
                    # Avoid division by zero
                    return current_strike

                # Calculate the interpolated strike where exposure = 0
                weight = -current_exposure / (next_exposure - current_exposure)
                zero_strike = current_strike + weight * (next_strike - current_strike)
                print(f"Found zero GEX crossing at: ${zero_strike:.2f}")
                return zero_strike

        # If no transition found, check if we have a strike very close to zero
        min_abs_exposure = min(abs(exp) for _, exp in sorted_exposures)
        max_abs_exposure = max(abs(exp) for _, exp in sorted_exposures)

        if max_abs_exposure > 0 and min_abs_exposure < max_abs_exposure * 0.01:  # Within 1% of max exposure
            # Find the strike with minimum absolute exposure
            zero_strike = min(sorted_exposures, key=lambda x: abs(x[1]))[0]
            print(f"Found approximate zero GEX at: ${zero_strike:.2f}")
            return zero_strike

        print("No zero GEX crossing found")
        return None

    except Exception as e:
        print(f"Error finding zero GEX trigger: {e}")
        return None

@app.route('/api/export-tradingview/<ticker>/<expiry_date>')
def export_tradingview(ticker, expiry_date):
    """API endpoint to generate TradingView Pine Script for gamma exposure levels"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)

        # Filter data by strike range (±20 from current price)
        strike_range = 20
        min_strike = current_price - strike_range
        max_strike = current_price + strike_range

        filtered_exposures = [
            exp for exp in all_exposures
            if min_strike <= exp['strike'] <= max_strike
        ]

        if not filtered_exposures:
            return jsonify({'success': False, 'error': 'No exposure data in strike range'})

        # Calculate net exposure for each strike
        net_exposures = {}
        for exp in filtered_exposures:
            strike = exp['strike']
            net_gex = exp['call_gex'] + exp['put_gex']
            net_exposures[strike] = net_gex

        # Get top 3 positive and negative strikes
        positive_strikes = []
        negative_strikes = []

        if net_exposures:
            # Sort by net GEX value
            sorted_strikes = sorted(net_exposures.items(), key=lambda x: x[1], reverse=True)

            # Get positive strikes (top 3)
            positive_strikes = [strike for strike, gex in sorted_strikes if gex > 0][:3]

            # Get negative strikes (bottom 3)
            negative_strikes = [strike for strike, gex in sorted_strikes if gex < 0][-3:]
            negative_strikes.reverse()  # Most negative first

        # Calculate zero GEX level using proper interpolation
        zero_gex_level = find_zero_gex_trigger(net_exposures)

        # Generate Pine Script
        pine_script = generate_pine_script(
            ticker=ticker.upper(),
            current_price=current_price,
            positive_strikes=positive_strikes,
            negative_strikes=negative_strikes,
            zero_gex_level=zero_gex_level
        )

        # Debug information
        print(f"TradingView Export Debug:")
        print(f"  Current Price: ${current_price:.2f}")
        print(f"  Positive Strikes: {positive_strikes}")
        print(f"  Negative Strikes: {negative_strikes}")
        print(f"  Zero GEX Level: {zero_gex_level}")
        print(f"  Net Exposures Sample: {dict(list(net_exposures.items())[:5])}")

        return jsonify({
            'success': True,
            'pine_script': pine_script,
            'current_price': current_price,
            'positive_strikes': positive_strikes,
            'negative_strikes': negative_strikes,
            'zero_gex_level': zero_gex_level
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def generate_pine_script(ticker, current_price, positive_strikes, negative_strikes, zero_gex_level):
    """Generate Pine Script code for TradingView"""
    script_lines = []

    # Header
    script_lines.append("//@version=5")
    script_lines.append(f'indicator("{ticker} Gamma Exposure Levels", overlay=true)')
    script_lines.append("")
    script_lines.append("// Generated from EzOptions MME Dashboard")
    script_lines.append(f"// Ticker: {ticker}")
    script_lines.append(f"// Current Price: ${current_price:.2f}")
    script_lines.append(f"// Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    script_lines.append("")

    # Market close time and extension logic
    script_lines.append("// Market close time (4:30 PM ET)")
    script_lines.append("market_close = timestamp('America/New_York', year, month, dayofmonth, 16, 30)")
    script_lines.append("extend_condition = time <= market_close")
    script_lines.append("")
    script_lines.append("// Variables to track if labels have been created")
    script_lines.append("var labels_created = false")
    script_lines.append("")

    # Declare line variables
    script_lines.append("// Declare line variables")
    for i in range(len(positive_strikes)):
        script_lines.append(f"var line pos_{i+1}_line = na")
        script_lines.append(f"var line pos_{i+1}_upper_line = na")
        script_lines.append(f"var line pos_{i+1}_lower_line = na")
    for i in range(len(negative_strikes)):
        script_lines.append(f"var line neg_{i+1}_line = na")
        script_lines.append(f"var line neg_{i+1}_upper_line = na")
        script_lines.append(f"var line neg_{i+1}_lower_line = na")
    script_lines.append("")

    # Positive strikes (resistance levels)
    for i, strike in enumerate(positive_strikes):
        upper_level = round(strike * 1.0008, 2)  # +0.08%
        lower_level = round(strike * 0.9992, 2)  # -0.08%

        script_lines.append(f"// Top {i+1} Positive Strike: ${strike:.2f}")
        script_lines.append(f"pos_{i+1}_strike = {strike:.2f}")
        script_lines.append(f"pos_{i+1}_upper = {upper_level:.2f}")
        script_lines.append(f"pos_{i+1}_lower = {lower_level:.2f}")
        script_lines.append(f'if barstate.islast and not labels_created')
        script_lines.append(f'    pos_{i+1}_line := line.new(bar_index, pos_{i+1}_strike, bar_index + 1, pos_{i+1}_strike, extend=extend.right, color=color.green, width=2)')
        script_lines.append(f'    pos_{i+1}_upper_line := line.new(bar_index, pos_{i+1}_upper, bar_index + 1, pos_{i+1}_upper, extend=extend.right, color=color.new(color.green, 70), width=1, style=line.style_dashed)')
        script_lines.append(f'    pos_{i+1}_lower_line := line.new(bar_index, pos_{i+1}_lower, bar_index + 1, pos_{i+1}_lower, extend=extend.right, color=color.new(color.green, 70), width=1, style=line.style_dashed)')
        script_lines.append(f'    label.new(bar_index, pos_{i+1}_strike, "Resistance {i+1}: ${strike:.2f}", style=label.style_label_left, color=color.green, textcolor=color.white, size=size.small)')
        script_lines.append("")

    # Negative strikes (support levels)
    for i, strike in enumerate(negative_strikes):
        upper_level = round(strike * 1.0008, 2)  # +0.08%
        lower_level = round(strike * 0.9992, 2)  # -0.08%

        script_lines.append(f"// Top {i+1} Negative Strike: ${strike:.2f}")
        script_lines.append(f"neg_{i+1}_strike = {strike:.2f}")
        script_lines.append(f"neg_{i+1}_upper = {upper_level:.2f}")
        script_lines.append(f"neg_{i+1}_lower = {lower_level:.2f}")
        script_lines.append(f'if barstate.islast and not labels_created')
        script_lines.append(f'    neg_{i+1}_line := line.new(bar_index, neg_{i+1}_strike, bar_index + 1, neg_{i+1}_strike, extend=extend.right, color=color.red, width=2)')
        script_lines.append(f'    neg_{i+1}_upper_line := line.new(bar_index, neg_{i+1}_upper, bar_index + 1, neg_{i+1}_upper, extend=extend.right, color=color.new(color.red, 70), width=1, style=line.style_dashed)')
        script_lines.append(f'    neg_{i+1}_lower_line := line.new(bar_index, neg_{i+1}_lower, bar_index + 1, neg_{i+1}_lower, extend=extend.right, color=color.new(color.red, 70), width=1, style=line.style_dashed)')
        script_lines.append(f'    label.new(bar_index, neg_{i+1}_strike, "Support {i+1}: ${strike:.2f}", style=label.style_label_left, color=color.red, textcolor=color.white, size=size.small)')
        script_lines.append("")

    # Zero GEX level
    if zero_gex_level is not None:
        script_lines.append("// Zero GEX Level")
        script_lines.append(f"zero_gex = {zero_gex_level:.2f}")
        script_lines.append('if barstate.islast and not labels_created')
        script_lines.append('    zero_gex_line = line.new(bar_index, zero_gex, bar_index + 1, zero_gex, extend=extend.right, color=color.purple, width=2, style=line.style_dashed)')
        script_lines.append(f'    label.new(bar_index, zero_gex, "Zero GEX: ${zero_gex_level:.2f}", style=label.style_label_left, color=color.purple, textcolor=color.white, size=size.small)')
        script_lines.append("")

    # Current price reference
    script_lines.append("// Current Price Reference")
    script_lines.append(f"current_ref = {current_price:.2f}")
    script_lines.append('if barstate.islast and not labels_created')
    script_lines.append('    current_ref_line = line.new(bar_index, current_ref, bar_index + 1, current_ref, extend=extend.right, color=color.white, width=1, style=line.style_dotted)')
    script_lines.append(f'    label.new(bar_index, current_ref, "Current: ${current_price:.2f}", style=label.style_label_left, color=color.white, textcolor=color.black, size=size.small)')
    script_lines.append("")

    # Background coloring
    if zero_gex_level is not None:
        script_lines.append("// Background coloring based on zero GEX level")
        script_lines.append("current_close = close")
        script_lines.append("above_zero_gex = current_close > zero_gex")
        script_lines.append("below_zero_gex = current_close <= zero_gex")
        script_lines.append("")
        script_lines.append("// Background colors with transparency")
        script_lines.append("green_bg = color.new(color.green, 92)  // Very subtle green (8% opacity)")
        script_lines.append("red_bg = color.new(color.red, 92)      // Very subtle red (8% opacity)")
        script_lines.append("")
        script_lines.append("// Apply background coloring")
        script_lines.append("bgcolor(above_zero_gex ? green_bg : below_zero_gex ? red_bg : na, title=\"GEX Background\")")
        script_lines.append("")

    # Zone fills
    script_lines.append("// Zone fill areas using linefill.new() function")
    script_lines.append("")

    # Positive zone fills
    for i, strike in enumerate(positive_strikes):
        transparency_levels = [60, 70, 80]  # 40%, 30%, 20% opacity
        transparency = transparency_levels[i] if i < len(transparency_levels) else 80

        script_lines.append(f"// Positive Zone {i+1} Fill Area")
        script_lines.append(f"pos_{i+1}_fill_color = color.new(color.green, {transparency})")
        script_lines.append(f"if not na(pos_{i+1}_upper_line) and not na(pos_{i+1}_lower_line)")
        script_lines.append(f"    linefill.new(pos_{i+1}_upper_line, pos_{i+1}_lower_line, color=pos_{i+1}_fill_color)")
        script_lines.append("")

    # Negative zone fills
    for i, strike in enumerate(negative_strikes):
        transparency_levels = [60, 70, 80]  # 40%, 30%, 20% opacity
        transparency = transparency_levels[i] if i < len(transparency_levels) else 80

        script_lines.append(f"// Negative Zone {i+1} Fill Area")
        script_lines.append(f"neg_{i+1}_fill_color = color.new(color.red, {transparency})")
        script_lines.append(f"if not na(neg_{i+1}_upper_line) and not na(neg_{i+1}_lower_line)")
        script_lines.append(f"    linefill.new(neg_{i+1}_upper_line, neg_{i+1}_lower_line, color=neg_{i+1}_fill_color)")
        script_lines.append("")

    # Mark labels as created
    script_lines.append("// Mark labels as created")
    script_lines.append("if barstate.islast and not labels_created")
    script_lines.append("    labels_created := true")

    return "\n".join(script_lines)



@app.route('/api/test-yahoo/<ticker>')
def test_yahoo_connection(ticker):
    """Test endpoint to debug Yahoo Finance connection"""
    try:
        print(f"=== TESTING YAHOO FINANCE CONNECTION ===")
        print(f"Ticker: {ticker}")

        stock = yf.Ticker(ticker.upper())

        # Test 1: Get basic info
        try:
            info = stock.info
            current_price = info.get('regularMarketPrice') or info.get('currentPrice')
            print(f"✅ Current price: {current_price}")
        except Exception as e:
            print(f"❌ Current price failed: {e}")
            current_price = None

        # Test 2: Get expiry dates
        try:
            expiry_dates = list(stock.options)
            print(f"✅ Expiry dates: {expiry_dates}")
        except Exception as e:
            print(f"❌ Expiry dates failed: {e}")
            expiry_dates = []

        # Test 3: Get options chain for first expiry
        options_data = None
        if expiry_dates:
            try:
                first_expiry = expiry_dates[0]
                options_chain = stock.option_chain(first_expiry)
                calls_count = len(options_chain.calls)
                puts_count = len(options_chain.puts)
                print(f"✅ Options chain for {first_expiry}: {calls_count} calls, {puts_count} puts")
                options_data = {
                    'expiry': first_expiry,
                    'calls_count': calls_count,
                    'puts_count': puts_count
                }
            except Exception as e:
                print(f"❌ Options chain failed: {e}")

        return jsonify({
            'success': True,
            'ticker': ticker.upper(),
            'current_price': current_price,
            'expiry_dates': expiry_dates,
            'options_test': options_data
        })

    except Exception as e:
        print(f"❌ Yahoo Finance test failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/historical-price/<ticker>')
def get_historical_price(ticker):
    """API endpoint to get historical price data for candlestick charts"""
    try:
        period = request.args.get('period', '5d')  # Default 5 days
        interval = request.args.get('interval', '1h')  # Default 1 hour

        stock = yf.Ticker(ticker.upper())
        hist = stock.history(period=period, interval=interval)

        if hist.empty:
            return jsonify({'success': False, 'error': 'No historical data available'})

        # Convert to format suitable for Plotly candlestick chart
        candlestick_data = []
        for index, row in hist.iterrows():
            candlestick_data.append({
                'timestamp': index.isoformat(),
                'open': row['Open'],
                'high': row['High'],
                'low': row['Low'],
                'close': row['Close'],
                'volume': row['Volume']
            })

        return jsonify({
            'success': True,
            'candlestick_data': safe_convert(candlestick_data),
            'ticker': ticker.upper()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/gamma-surface-price-data/<ticker>')
def get_exposure_surface_price_data(ticker):
    """API endpoint to get historical price data for exposure surface charts"""
    try:
        # Get time range from query parameters (in minutes)
        time_range = int(request.args.get('time_range', 120))  # Default 2 hours
        time_step = int(request.args.get('time_step', 3))      # Default 3 minutes

        print(f"📈 Fetching price data for {ticker}: time_range={time_range}min, time_step={time_step}min")

        # Calculate how much historical data we need
        # Add some buffer to ensure we have enough data
        total_minutes_needed = time_range + 60  # Add 1 hour buffer

        # Determine appropriate interval based on time step with fallback logic
        if time_step <= 1:
            interval = '1m'
            # For 1-minute data, use shorter period due to Yahoo Finance limitations
            if total_minutes_needed <= 60:
                period = '1d'
            elif total_minutes_needed <= 420:  # 7 hours (max for 1m data)
                period = '7d'
            else:
                period = '7d'  # Maximum for 1-minute data
        elif time_step <= 5:
            interval = '5m'
            if total_minutes_needed <= 60:
                period = '1d'
            elif total_minutes_needed <= 300:  # 5 hours
                period = '5d'
            else:
                period = '1mo'
        elif time_step <= 15:
            interval = '15m'
            if total_minutes_needed <= 60:
                period = '1d'
            elif total_minutes_needed <= 300:  # 5 hours
                period = '5d'
            else:
                period = '1mo'
        else:
            interval = '1h'
            if total_minutes_needed <= 60:
                period = '1d'
            elif total_minutes_needed <= 300:  # 5 hours
                period = '5d'
            else:
                period = '1mo'

        stock = yf.Ticker(ticker.upper())
        hist = stock.history(period=period, interval=interval)

        # If 1-minute data is empty or insufficient, fallback to 5-minute data
        if (interval == '1m' and (hist.empty or len(hist) < 10)) and time_step == 1:
            print(f"1-minute data insufficient ({len(hist) if not hist.empty else 0} points), falling back to 5-minute data")
            interval = '5m'
            period = '5d' if total_minutes_needed <= 300 else '1mo'
            hist = stock.history(period=period, interval=interval)

        if hist.empty:
            return jsonify({'success': False, 'error': 'No historical price data available'})

        # Get the most recent data points based on time_range and time_step
        # Calculate how many data points we need
        num_points = (time_range // time_step) + 1

        # For fallback scenarios, adjust num_points based on actual interval
        if interval != '1m' and time_step == 1:
            # If we fell back from 1m to 5m, adjust the number of points
            num_points = (time_range // 5) + 1

        # Take the most recent data points
        recent_hist = hist.tail(num_points)

        # Convert to format suitable for gamma surface charts
        price_data = []
        line_data = []

        for index, row in recent_hist.iterrows():
            timestamp = index.isoformat()

            # For candlestick data (2D charts)
            price_data.append({
                'timestamp': timestamp,
                'open': row['Open'],
                'high': row['High'],
                'low': row['Low'],
                'close': row['Close'],
                'volume': row['Volume']
            })

            # For line data (3D charts) - use close price
            line_data.append({
                'timestamp': timestamp,
                'price': row['Close']
            })

        print(f"📈 Returning price data: {len(price_data)} points, interval: {interval}, period: {period}")

        return jsonify({
            'success': True,
            'candlestick_data': safe_convert(price_data),
            'line_data': safe_convert(line_data),
            'ticker': ticker.upper(),
            'time_range': time_range,
            'time_step': time_step,
            'data_points': len(price_data),
            'actual_interval': interval,  # Include actual interval used
            'period_used': period  # Include period used
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/signals/<ticker>/<expiry_date>')
def get_signals(ticker, expiry_date):
    """API endpoint to get trading signals with price targets and bias"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures for signal analysis
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)

        # Calculate signals
        signals_data = calculate_trading_signals(all_exposures, current_price, ticker.upper())

        return jsonify({
            'success': True,
            'signals': safe_convert(signals_data),
            'current_price': current_price,
            'ticker': ticker.upper(),
            'expiry_date': expiry_date,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def calculate_trading_signals(all_exposures, current_price, ticker):
    """Calculate trading signals with price targets and bias"""
    try:
        # Extract gamma exposure data using correct keys from calculate_all_exposures
        gamma_data = []
        for data in all_exposures:
            gamma_data.append({
                'strike': data['strike'],
                'gamma_exposure': data['net_vgex'],  # Use net_vgex (volume-based) as gamma_exposure
                'call_oi': data.get('call_oi', 0),
                'put_oi': data.get('put_oi', 0),
                'call_volume': data.get('call_volume', 0),
                'put_volume': data.get('put_volume', 0)
            })

        # Sort by strike for analysis
        gamma_data.sort(key=lambda x: x['strike'])

        # Find key gamma levels
        gamma_levels = find_key_gamma_levels(gamma_data, current_price)

        # Calculate bias based on gamma regime and positioning
        bias_analysis = calculate_market_bias(gamma_data, current_price, gamma_levels)

        # Calculate price targets
        price_targets = calculate_price_targets(gamma_levels, current_price, bias_analysis)

        # Determine signal confidence
        confidence = calculate_signal_confidence(gamma_data, current_price, bias_analysis)

        # Generate simple BUY/SELL signal
        signal_action = generate_simple_signal(bias_analysis, confidence)

        return {
            'signal': signal_action,
            'bias': bias_analysis,
            'confidence': confidence,
            'gamma_levels': gamma_levels,
            'market_regime': determine_market_regime(gamma_data, current_price),
            'signal_strength': calculate_signal_strength(gamma_data, current_price)
        }

    except Exception as e:
        print(f"Error calculating trading signals: {e}")
        return {
            'bias': {'direction': 'NEUTRAL', 'strength': 0, 'reason': 'Error in calculation'},
            'price_targets': {'long_target': None, 'short_target': None},
            'confidence': 0,
            'gamma_levels': {},
            'market_regime': 'UNKNOWN',
            'signal_strength': 0
        }

def find_key_gamma_levels(gamma_data, current_price):
    """Find key gamma exposure levels for support/resistance"""
    levels = {
        'zero_gex': None,
        'max_positive_gex': None,
        'max_negative_gex': None,
        'nearest_support': None,
        'nearest_resistance': None
    }

    # Validate input data
    if not gamma_data or len(gamma_data) == 0:
        print("Warning: Empty gamma_data provided to find_key_gamma_levels")
        return levels

    # Ensure all data has required fields
    valid_data = []
    for d in gamma_data:
        if isinstance(d, dict) and 'gamma_exposure' in d and 'strike' in d:
            if d['gamma_exposure'] is not None and d['strike'] is not None:
                valid_data.append(d)

    if not valid_data:
        print("Warning: No valid gamma data found")
        return levels

    gamma_data = valid_data

    # Find zero GEX level - closest strike to current price that flips gamma sign
    closest_flip_strike = None
    closest_distance = float('inf')

    for i in range(len(gamma_data) - 1):
        current_gex = gamma_data[i]['gamma_exposure']
        next_gex = gamma_data[i + 1]['gamma_exposure']
        current_strike = gamma_data[i]['strike']
        next_strike = gamma_data[i + 1]['strike']

        # Check if gamma flips sign between these strikes
        if current_gex <= 0 <= next_gex or next_gex <= 0 <= current_gex:
            # Find which strike is closer to current price
            distance_current = abs(current_strike - current_price)
            distance_next = abs(next_strike - current_price)

            if distance_current <= distance_next:
                candidate_strike = current_strike
                candidate_distance = distance_current
            else:
                candidate_strike = next_strike
                candidate_distance = distance_next

            # Keep the closest flip to current price
            if candidate_distance < closest_distance:
                closest_distance = candidate_distance
                closest_flip_strike = candidate_strike

    if closest_flip_strike is not None:
        levels['zero_gex'] = closest_flip_strike

    # Find maximum positive and negative GEX levels with error handling
    try:
        max_pos_gex = max(gamma_data, key=lambda x: x['gamma_exposure'])
        max_neg_gex = min(gamma_data, key=lambda x: x['gamma_exposure'])
    except (ValueError, TypeError) as e:
        print(f"Error finding max/min gamma levels: {e}")
        return levels

    levels['max_positive_gex'] = {
        'strike': max_pos_gex['strike'],
        'value': max_pos_gex['gamma_exposure']
    }
    levels['max_negative_gex'] = {
        'strike': max_neg_gex['strike'],
        'value': max_neg_gex['gamma_exposure']
    }

    # Find nearest support and resistance levels based on correct gamma interpretation
    # Positive gamma exposure:
    #   - Acts as SUPPORT when current price is ABOVE the strike
    #   - Acts as RESISTANCE when current price is BELOW the strike
    # Negative gamma exposure:
    #   - Acts as RESISTANCE when current price is ABOVE the strike
    #   - Acts as SUPPORT when current price is BELOW the strike

    # Find support levels (strikes below current price)
    potential_support = []
    for d in gamma_data:
        try:
            if d['strike'] < current_price:
                if d['gamma_exposure'] > 0:  # Positive gamma below price = support
                    potential_support.append(d)
                # Note: Negative gamma below price would be resistance, not support
        except (KeyError, TypeError):
            continue

    # Find resistance levels (strikes above current price)
    potential_resistance = []
    for d in gamma_data:
        try:
            if d['strike'] > current_price:
                if d['gamma_exposure'] > 0:  # Positive gamma above price = resistance
                    potential_resistance.append(d)
                # Note: Negative gamma above price would be support, not resistance
        except (KeyError, TypeError):
            continue

    # Get the strongest support (highest positive gamma below price)
    if potential_support:
        try:
            levels['nearest_support'] = max(potential_support, key=lambda x: x['gamma_exposure'])
        except (ValueError, TypeError):
            pass

    # Get the strongest resistance (highest positive gamma above price)
    if potential_resistance:
        try:
            levels['nearest_resistance'] = max(potential_resistance, key=lambda x: x['gamma_exposure'])
        except (ValueError, TypeError):
            pass

    return levels

def calculate_market_bias(gamma_data, current_price, gamma_levels):
    """Calculate market bias based on gamma positioning and current price"""
    bias = {
        'direction': 'NEUTRAL',
        'strength': 0,
        'reason': '',
        'factors': []
    }

    # Validate input data
    if not gamma_data or len(gamma_data) == 0:
        bias['reason'] = 'No gamma data available'
        return bias

    factors = []
    bias_score = 0

    # Factor 1: Position relative to zero GEX
    if gamma_levels and gamma_levels.get('zero_gex'):
        if current_price > gamma_levels['zero_gex']:
            factors.append('Above Zero GEX (Positive Gamma Regime)')
            bias_score += 1
        else:
            factors.append('Below Zero GEX (Negative Gamma Regime)')
            bias_score -= 1

    # Factor 2: Call vs Put positioning with safe data handling
    try:
        total_call_oi = sum(d.get('call_oi', 0) for d in gamma_data if isinstance(d, dict))
        total_put_oi = sum(d.get('put_oi', 0) for d in gamma_data if isinstance(d, dict))

        if total_call_oi > 0 and total_put_oi > 0:
            if total_call_oi > total_put_oi * 1.2:
                factors.append('Call OI Dominance')
                bias_score += 0.5
            elif total_put_oi > total_call_oi * 1.2:
                factors.append('Put OI Dominance')
                bias_score -= 0.5
    except (TypeError, ValueError) as e:
        print(f"Error calculating OI bias: {e}")

    # Factor 3: Volume analysis with safe data handling
    try:
        total_call_volume = sum(d.get('call_volume', 0) for d in gamma_data if isinstance(d, dict))
        total_put_volume = sum(d.get('put_volume', 0) for d in gamma_data if isinstance(d, dict))

        if total_call_volume > 0 and total_put_volume > 0:
            if total_call_volume > total_put_volume * 1.3:
                factors.append('Heavy Call Volume')
                bias_score += 0.3
            elif total_put_volume > total_call_volume * 1.3:
                factors.append('Heavy Put Volume')
                bias_score -= 0.3
    except (TypeError, ValueError) as e:
        print(f"Error calculating volume bias: {e}")

    # Factor 4: Distance from major gamma levels and their implications
    try:
        if (gamma_levels and
            gamma_levels.get('nearest_resistance') and
            gamma_levels.get('nearest_support')):

            resistance_distance = abs(gamma_levels['nearest_resistance']['strike'] - current_price)
            support_distance = abs(gamma_levels['nearest_support']['strike'] - current_price)

            if resistance_distance < support_distance:
                factors.append('Near Resistance (Positive Gamma Above)')
                bias_score -= 0.3  # Stronger negative bias near resistance
            else:
                factors.append('Near Support (Positive Gamma Below)')
                bias_score += 0.3  # Stronger positive bias near support
        elif gamma_levels and gamma_levels.get('nearest_resistance'):
            factors.append('Resistance Above (Positive Gamma)')
            bias_score -= 0.2
        elif gamma_levels and gamma_levels.get('nearest_support'):
            factors.append('Support Below (Positive Gamma)')
            bias_score += 0.2
    except (TypeError, KeyError) as e:
        print(f"Error calculating gamma level bias: {e}")

    # Determine final bias
    if bias_score > 0.5:
        bias['direction'] = 'BULLISH'
        bias['strength'] = min(abs(bias_score) * 50, 100)
    elif bias_score < -0.5:
        bias['direction'] = 'BEARISH'
        bias['strength'] = min(abs(bias_score) * 50, 100)
    else:
        bias['direction'] = 'NEUTRAL'
        bias['strength'] = 0

    bias['factors'] = factors
    bias['reason'] = f"Score: {bias_score:.2f} based on {len(factors)} factors"

    return bias

def generate_simple_signal(bias_analysis, confidence):
    """Generate simple BUY/SELL signal based on bias and confidence"""

    # Validate inputs
    if not bias_analysis or not isinstance(bias_analysis, dict):
        return {
            'action': 'HOLD',
            'reason': 'No bias data available',
            'confidence_level': 'LOW'
        }

    if not confidence or not isinstance(confidence, dict):
        confidence = {'level': 'LOW', 'score': 0}

    direction = bias_analysis.get('direction', 'NEUTRAL')
    strength = bias_analysis.get('strength', 0)
    confidence_level = confidence.get('level', 'LOW')
    confidence_score = confidence.get('score', 0)

    # Simple signal logic
    if direction == 'BULLISH' and strength >= 50 and confidence_score >= 40:
        action = 'BUY'
        reason = f"Strong bullish bias ({strength}%) with {confidence_level.lower()} confidence"
    elif direction == 'BEARISH' and strength >= 50 and confidence_score >= 40:
        action = 'SELL'
        reason = f"Strong bearish bias ({strength}%) with {confidence_level.lower()} confidence"
    elif direction == 'BULLISH' and strength >= 30:
        action = 'BUY'
        reason = f"Moderate bullish bias ({strength}%) - consider smaller position"
    elif direction == 'BEARISH' and strength >= 30:
        action = 'SELL'
        reason = f"Moderate bearish bias ({strength}%) - consider smaller position"
    else:
        action = 'HOLD'
        reason = f"Neutral or weak signal ({direction}, {strength}%)"

    return {
        'action': action,
        'reason': reason,
        'confidence_level': confidence_level,
        'bias_strength': strength,
        'confidence_score': confidence_score
    }

def calculate_price_targets(gamma_levels, current_price, bias_analysis):
    """Calculate price targets for long and short positions"""
    targets = {
        'long_target': None,
        'short_target': None,
        'long_stop': None,
        'short_stop': None,
        'target_reasoning': []
    }

    reasoning = []

    # Long target calculation - target resistance levels (positive gamma above current price)
    if bias_analysis['direction'] in ['BULLISH', 'NEUTRAL']:
        if gamma_levels['nearest_resistance']:
            # Target just below resistance level (positive gamma above price acts as resistance)
            resistance_strike = gamma_levels['nearest_resistance']['strike']
            targets['long_target'] = resistance_strike * 0.995  # 0.5% below resistance
            reasoning.append(f"Long target at {targets['long_target']:.2f} (0.5% below positive gamma resistance at {resistance_strike})")
        elif gamma_levels['max_positive_gex']:
            # Target at max positive GEX level
            targets['long_target'] = gamma_levels['max_positive_gex']['strike']
            reasoning.append(f"Long target at max positive GEX level: {targets['long_target']:.2f}")

    # Short target calculation - target support levels (positive gamma below current price)
    if bias_analysis['direction'] in ['BEARISH', 'NEUTRAL']:
        if gamma_levels['nearest_support']:
            # Target just above support level (positive gamma below price acts as support)
            support_strike = gamma_levels['nearest_support']['strike']
            targets['short_target'] = support_strike * 1.005  # 0.5% above support
            reasoning.append(f"Short target at {targets['short_target']:.2f} (0.5% above positive gamma support at {support_strike})")
        elif gamma_levels['max_negative_gex']:
            # Target at max negative GEX level
            targets['short_target'] = gamma_levels['max_negative_gex']['strike']
            reasoning.append(f"Short target at max negative GEX level: {targets['short_target']:.2f}")

    # Stop loss calculations
    if targets['long_target']:
        if gamma_levels['nearest_support']:
            targets['long_stop'] = gamma_levels['nearest_support']['strike'] * 0.99
        else:
            targets['long_stop'] = current_price * 0.98  # 2% stop loss

    if targets['short_target']:
        if gamma_levels['nearest_resistance']:
            targets['short_stop'] = gamma_levels['nearest_resistance']['strike'] * 1.01
        else:
            targets['short_stop'] = current_price * 1.02  # 2% stop loss

    targets['target_reasoning'] = reasoning
    return targets

def calculate_signal_confidence(gamma_data, current_price, bias_analysis):
    """Calculate confidence level for the signals"""
    confidence_factors = []
    confidence_score = 0

    # Validate input data
    if not gamma_data or len(gamma_data) == 0:
        return {
            'score': 0,
            'factors': ['No data available'],
            'level': 'LOW'
        }

    if not bias_analysis or not isinstance(bias_analysis, dict):
        bias_analysis = {'strength': 0, 'factors': []}

    # Factor 1: Bias strength
    bias_strength = bias_analysis.get('strength', 0)
    if bias_strength > 70:
        confidence_score += 30
        confidence_factors.append('Strong bias signal')
    elif bias_strength > 40:
        confidence_score += 20
        confidence_factors.append('Moderate bias signal')

    # Factor 2: Volume confirmation with safe data handling
    try:
        total_volume = sum(
            d.get('call_volume', 0) + d.get('put_volume', 0)
            for d in gamma_data
            if isinstance(d, dict)
        )
        if total_volume > 1000:  # Arbitrary threshold for high volume
            confidence_score += 25
            confidence_factors.append('High volume confirmation')
        elif total_volume > 500:
            confidence_score += 15
            confidence_factors.append('Moderate volume')
    except (TypeError, ValueError) as e:
        print(f"Error calculating volume confidence: {e}")

    # Factor 3: Gamma exposure magnitude with safe data handling
    try:
        gamma_exposures = [
            abs(d.get('gamma_exposure', 0))
            for d in gamma_data
            if isinstance(d, dict) and d.get('gamma_exposure') is not None
        ]
        if gamma_exposures:
            max_gex = max(gamma_exposures)
            if max_gex > 1000000:  # $1M+ gamma exposure
                confidence_score += 25
                confidence_factors.append('High gamma exposure magnitude')
            elif max_gex > 500000:
                confidence_score += 15
                confidence_factors.append('Moderate gamma exposure')
    except (TypeError, ValueError) as e:
        print(f"Error calculating gamma exposure confidence: {e}")

    # Factor 4: Number of supporting factors
    factor_count = len(bias_analysis.get('factors', []))
    if factor_count >= 3:
        confidence_score += 20
        confidence_factors.append('Multiple supporting factors')
    elif factor_count >= 2:
        confidence_score += 10
        confidence_factors.append('Some supporting factors')

    return {
        'score': min(confidence_score, 100),
        'factors': confidence_factors,
        'level': 'HIGH' if confidence_score >= 70 else 'MEDIUM' if confidence_score >= 40 else 'LOW'
    }

def determine_market_regime(gamma_data, current_price):
    """Determine the current market regime based on gamma positioning

    Gamma Regime Interpretation:
    - Positive Gamma Regime: Market makers provide liquidity, dampening volatility
      * Positive gamma below price = support
      * Positive gamma above price = resistance
    - Negative Gamma Regime: Market makers amplify moves, increasing volatility
      * Negative gamma below price = resistance
      * Negative gamma above price = support
    """
    total_positive_gex = sum(d['gamma_exposure'] for d in gamma_data if d['gamma_exposure'] > 0)
    total_negative_gex = sum(d['gamma_exposure'] for d in gamma_data if d['gamma_exposure'] < 0)

    net_gex = total_positive_gex + total_negative_gex

    if net_gex > 0:
        if net_gex > 1000000:
            return 'STRONG_POSITIVE_GAMMA'
        else:
            return 'POSITIVE_GAMMA'
    else:
        if net_gex < -1000000:
            return 'STRONG_NEGATIVE_GAMMA'
        else:
            return 'NEGATIVE_GAMMA'

def calculate_signal_strength(gamma_data, current_price):
    """Calculate overall signal strength"""
    # Validate input data
    if not gamma_data or len(gamma_data) == 0:
        return 0

    try:
        # Based on gamma exposure concentration and volume with safe data handling
        gamma_exposures = [
            abs(d.get('gamma_exposure', 0))
            for d in gamma_data
            if isinstance(d, dict) and d.get('gamma_exposure') is not None
        ]

        volumes = [
            d.get('call_volume', 0) + d.get('put_volume', 0)
            for d in gamma_data
            if isinstance(d, dict)
        ]

        max_gex = max(gamma_exposures) if gamma_exposures else 0
        total_volume = sum(volumes) if volumes else 0

        # Normalize to 0-100 scale
        gex_strength = min(max_gex / 1000000 * 50, 50)  # Max 50 points from GEX
        volume_strength = min(total_volume / 2000 * 50, 50)  # Max 50 points from volume

        return min(gex_strength + volume_strength, 100)
    except (TypeError, ValueError) as e:
        print(f"Error calculating signal strength: {e}")
        return 0

@app.route('/api/eod-vanna-signals/<ticker>/<expiry_date>')
def get_eod_vanna_signals(ticker, expiry_date):
    """API endpoint to get EOD vanna-based entry signals"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures for vanna analysis
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)

        # Calculate EOD vanna signals
        vanna_signals = calculate_eod_vanna_signals(all_exposures, current_price, ticker.upper(), expiry_date)

        return jsonify({
            'success': True,
            'vanna_signals': safe_convert(vanna_signals),
            'current_price': current_price,
            'ticker': ticker.upper(),
            'expiry_date': expiry_date,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/eod-charm-signals/<ticker>/<expiry_date>')
def get_eod_charm_signals(ticker, expiry_date):
    """API endpoint to get EOD charm-based entry signals"""
    try:
        calls_df, puts_df, current_price = analyzer.get_options_data(ticker.upper(), expiry_date)

        if calls_df.empty and puts_df.empty:
            return jsonify({'success': False, 'error': 'No options data available'})

        # Calculate all exposures for charm analysis
        all_exposures = calculate_all_exposures(calls_df, puts_df, current_price)

        # Calculate EOD charm signals
        charm_signals = calculate_eod_charm_signals(all_exposures, current_price, ticker.upper(), expiry_date)

        return jsonify({
            'success': True,
            'charm_signals': safe_convert(charm_signals),
            'current_price': current_price,
            'ticker': ticker.upper(),
            'expiry_date': expiry_date,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

def calculate_eod_vanna_signals(all_exposures, current_price, ticker, expiry_date):
    """Calculate EOD vanna-based entry signals"""
    try:
        # Extract vanna exposure data
        vanna_data = []
        for data in all_exposures:
            vanna_data.append({
                'strike': data['strike'],
                'vanna_exposure': data['net_vex'],  # Use net vanna exposure
                'call_oi': data.get('call_oi', 0),
                'put_oi': data.get('put_oi', 0),
                'call_volume': data.get('call_volume', 0),
                'put_volume': data.get('put_volume', 0)
            })

        # Sort by strike for analysis
        vanna_data.sort(key=lambda x: x['strike'])

        # Calculate vanna regime
        vanna_regime = determine_vanna_regime(vanna_data, current_price)

        # Calculate volatility sensitivity
        vol_sensitivity = calculate_volatility_sensitivity(vanna_data, current_price)

        # Calculate cross-effect risk (vanna impact on delta)
        cross_risk = calculate_vanna_cross_risk(vanna_data, current_price)

        # Calculate overnight risk based on vanna positioning
        overnight_risk = calculate_vanna_overnight_risk(vanna_data, current_price, expiry_date)

        # Generate EOD entry signals based on vanna analysis
        entry_signals = generate_vanna_entry_signals(vanna_data, current_price, vanna_regime, vol_sensitivity)

        return {
            'regime': vanna_regime,
            'volatility_sensitivity': vol_sensitivity,
            'cross_effect_risk': cross_risk,
            'overnight_risk': overnight_risk,
            'entry_signals': entry_signals,
            'vanna_levels': find_key_vanna_levels(vanna_data, current_price),
            'signal_strength': calculate_vanna_signal_strength(vanna_data, current_price)
        }

    except Exception as e:
        print(f"Error calculating EOD vanna signals: {e}")
        return {
            'regime': 'UNKNOWN',
            'volatility_sensitivity': 0,
            'cross_effect_risk': 'LOW',
            'overnight_risk': 'LOW',
            'entry_signals': [],
            'vanna_levels': {},
            'signal_strength': 0
        }

def determine_vanna_regime(vanna_data, current_price):
    """Determine the current vanna regime"""
    total_positive_vanna = sum(d['vanna_exposure'] for d in vanna_data if d['vanna_exposure'] > 0)
    total_negative_vanna = sum(d['vanna_exposure'] for d in vanna_data if d['vanna_exposure'] < 0)

    net_vanna = total_positive_vanna + total_negative_vanna

    if net_vanna > 100000:  # $100k+ net positive vanna
        return 'POSITIVE_VANNA'
    elif net_vanna < -100000:  # $100k+ net negative vanna
        return 'NEGATIVE_VANNA'
    else:
        return 'NEUTRAL_VANNA'

def calculate_volatility_sensitivity(vanna_data, current_price):
    """Calculate overall volatility sensitivity based on vanna positioning"""
    # Find vanna exposure near current price (within 2%)
    price_range = current_price * 0.02
    near_price_vanna = [d for d in vanna_data
                       if abs(d['strike'] - current_price) <= price_range]

    if not near_price_vanna:
        return 0

    # Sum vanna exposure near current price
    total_vanna = sum(d['vanna_exposure'] for d in near_price_vanna)

    # Normalize to percentage (rough approximation)
    # Positive vanna = delta increases with vol increase
    # Negative vanna = delta decreases with vol increase
    sensitivity = min(abs(total_vanna) / 1000000 * 100, 100)  # Scale to 0-100%

    return sensitivity

def calculate_vanna_cross_risk(vanna_data, current_price):
    """Calculate cross-effect risk from vanna exposure"""
    max_vanna = max(abs(d['vanna_exposure']) for d in vanna_data)

    if max_vanna > 1000000:  # $1M+ vanna exposure
        return 'HIGH'
    elif max_vanna > 500000:  # $500k+ vanna exposure
        return 'MEDIUM'
    else:
        return 'LOW'

def calculate_vanna_overnight_risk(vanna_data, current_price, expiry_date):
    """Calculate overnight risk based on vanna positioning and time to expiry"""
    try:
        # Calculate time to expiry
        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
        today = datetime.now()
        days_to_expiry = (expiry_dt - today).days

        # Higher vanna risk closer to expiry
        time_factor = max(1 - (days_to_expiry / 30), 0.1)  # Higher risk closer to expiry

        # Calculate total vanna exposure
        total_vanna = sum(abs(d['vanna_exposure']) for d in vanna_data)

        # Risk score based on vanna magnitude and time
        risk_score = (total_vanna / 1000000) * time_factor

        if risk_score > 2:
            return 'HIGH'
        elif risk_score > 1:
            return 'MEDIUM'
        else:
            return 'LOW'

    except Exception:
        return 'LOW'

def find_key_vanna_levels(vanna_data, current_price):
    """Find key vanna exposure levels"""
    levels = {
        'max_positive_vanna': None,
        'max_negative_vanna': None,
        'nearest_vanna_support': None,
        'nearest_vanna_resistance': None
    }

    # Find maximum positive and negative vanna levels
    max_pos_vanna = max(vanna_data, key=lambda x: x['vanna_exposure'])
    max_neg_vanna = min(vanna_data, key=lambda x: x['vanna_exposure'])

    levels['max_positive_vanna'] = {
        'strike': max_pos_vanna['strike'],
        'value': max_pos_vanna['vanna_exposure']
    }
    levels['max_negative_vanna'] = {
        'strike': max_neg_vanna['strike'],
        'value': max_neg_vanna['vanna_exposure']
    }

    # Find vanna-based support/resistance
    # Positive vanna below price can act as support in vol expansion
    # Negative vanna above price can act as resistance in vol expansion
    below_price = [d for d in vanna_data if d['strike'] < current_price and d['vanna_exposure'] > 0]
    above_price = [d for d in vanna_data if d['strike'] > current_price and d['vanna_exposure'] < 0]

    if below_price:
        levels['nearest_vanna_support'] = max(below_price, key=lambda x: x['vanna_exposure'])

    if above_price:
        levels['nearest_vanna_resistance'] = min(above_price, key=lambda x: x['vanna_exposure'])

    return levels

def generate_vanna_entry_signals(vanna_data, current_price, vanna_regime, vol_sensitivity):
    """Generate EOD entry signals based on vanna analysis"""
    signals = []

    # Signal 1: Volatility Expansion Play
    if vanna_regime == 'POSITIVE_VANNA' and vol_sensitivity > 50:
        signals.append({
            'type': 'VOL_EXPANSION_LONG',
            'direction': 'LONG',
            'confidence': min(vol_sensitivity, 90),
            'reasoning': 'Positive vanna suggests delta will increase with vol expansion',
            'target_type': 'VOLATILITY_PLAY',
            'time_horizon': 'EOD_TO_NEXT_DAY'
        })

    # Signal 2: Volatility Contraction Play
    if vanna_regime == 'NEGATIVE_VANNA' and vol_sensitivity > 50:
        signals.append({
            'type': 'VOL_CONTRACTION_SHORT',
            'direction': 'SHORT',
            'confidence': min(vol_sensitivity, 90),
            'reasoning': 'Negative vanna suggests delta will decrease with vol expansion',
            'target_type': 'VOLATILITY_PLAY',
            'time_horizon': 'EOD_TO_NEXT_DAY'
        })

    # Signal 3: Vanna Squeeze Setup
    max_vanna = max(abs(d['vanna_exposure']) for d in vanna_data)
    if max_vanna > 1000000:  # High vanna concentration
        signals.append({
            'type': 'VANNA_SQUEEZE',
            'direction': 'NEUTRAL',
            'confidence': 75,
            'reasoning': 'High vanna concentration suggests potential squeeze setup',
            'target_type': 'RANGE_PLAY',
            'time_horizon': 'OVERNIGHT'
        })

    return signals

def calculate_vanna_signal_strength(vanna_data, current_price):
    """Calculate overall vanna signal strength"""
    max_vanna = max(abs(d['vanna_exposure']) for d in vanna_data)
    total_volume = sum(d['call_volume'] + d['put_volume'] for d in vanna_data)

    # Normalize to 0-100 scale
    vanna_strength = min(max_vanna / 1000000 * 60, 60)  # Max 60 points from vanna
    volume_strength = min(total_volume / 2000 * 40, 40)  # Max 40 points from volume

    return min(vanna_strength + volume_strength, 100)

def calculate_eod_charm_signals(all_exposures, current_price, ticker, expiry_date):
    """Calculate EOD charm-based entry signals"""
    try:
        # Extract charm exposure data
        charm_data = []
        for data in all_exposures:
            charm_data.append({
                'strike': data['strike'],
                'charm_exposure': data['net_cex'],  # Use net charm exposure
                'call_oi': data.get('call_oi', 0),
                'put_oi': data.get('put_oi', 0),
                'call_volume': data.get('call_volume', 0),
                'put_volume': data.get('put_volume', 0)
            })

        # Sort by strike for analysis
        charm_data.sort(key=lambda x: x['strike'])

        # Calculate charm regime
        charm_regime = determine_charm_regime(charm_data, current_price)

        # Calculate time decay rate impact
        decay_rate = calculate_charm_decay_rate(charm_data, current_price, expiry_date)

        # Calculate delta sensitivity to time
        delta_sensitivity = calculate_charm_delta_sensitivity(charm_data, current_price)

        # Calculate EOD risk level
        eod_risk = calculate_charm_eod_risk(charm_data, current_price, expiry_date)

        # Calculate charm-based bias analysis
        charm_bias = calculate_charm_market_bias(charm_data, current_price, charm_regime, decay_rate)

        # Calculate charm signal confidence
        charm_confidence = calculate_charm_signal_confidence(charm_data, current_price, charm_bias, decay_rate)

        # Generate simple BUY/SELL signal based on charm analysis
        charm_signal = generate_simple_charm_signal(charm_bias, charm_confidence)

        return {
            'signal': charm_signal,
            'bias': charm_bias,
            'confidence': charm_confidence,
            'regime': charm_regime,
            'time_decay_rate': decay_rate,
            'delta_sensitivity': delta_sensitivity,
            'eod_risk_level': eod_risk,
            'charm_levels': find_key_charm_levels(charm_data, current_price),
            'signal_strength': calculate_charm_signal_strength(charm_data, current_price)
        }

    except Exception as e:
        print(f"Error calculating EOD charm signals: {e}")
        return {
            'regime': 'UNKNOWN',
            'time_decay_rate': 0,
            'delta_sensitivity': 0,
            'eod_risk_level': 'LOW',
            'entry_signals': [],
            'charm_levels': {},
            'signal_strength': 0
        }

def determine_charm_regime(charm_data, current_price):
    """Determine the current charm regime"""
    total_positive_charm = sum(d['charm_exposure'] for d in charm_data if d['charm_exposure'] > 0)
    total_negative_charm = sum(d['charm_exposure'] for d in charm_data if d['charm_exposure'] < 0)

    net_charm = total_positive_charm + total_negative_charm

    if net_charm > 50000:  # $50k+ net positive charm
        return 'POSITIVE_CHARM'
    elif net_charm < -50000:  # $50k+ net negative charm
        return 'NEGATIVE_CHARM'
    else:
        return 'NEUTRAL_CHARM'

def calculate_charm_decay_rate(charm_data, current_price, expiry_date):
    """Calculate time decay rate impact based on charm positioning"""
    try:
        # Calculate time to expiry
        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
        today = datetime.now()
        days_to_expiry = (expiry_dt - today).days

        # Find charm exposure near current price
        price_range = current_price * 0.02
        near_price_charm = [d for d in charm_data
                           if abs(d['strike'] - current_price) <= price_range]

        if not near_price_charm:
            return 0

        # Sum charm exposure near current price
        total_charm = sum(d['charm_exposure'] for d in near_price_charm)

        # Time decay accelerates closer to expiry
        time_factor = max(1 - (days_to_expiry / 30), 0.1)

        # Calculate decay rate (normalized to percentage)
        decay_rate = abs(total_charm) / 100000 * time_factor * 100

        return min(decay_rate, 100)

    except Exception:
        return 0

def calculate_charm_delta_sensitivity(charm_data, current_price):
    """Calculate delta sensitivity to time decay"""
    # Find charm exposure near current price
    price_range = current_price * 0.03
    near_price_charm = [d for d in charm_data
                       if abs(d['strike'] - current_price) <= price_range]

    if not near_price_charm:
        return 0

    # Sum charm exposure near current price
    total_charm = sum(abs(d['charm_exposure']) for d in near_price_charm)

    # Normalize to percentage
    sensitivity = min(total_charm / 100000 * 100, 100)

    return sensitivity

def calculate_charm_eod_risk(charm_data, current_price, expiry_date):
    """Calculate EOD risk level based on charm positioning"""
    try:
        # Calculate time to expiry
        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
        today = datetime.now()
        days_to_expiry = (expiry_dt - today).days

        # Higher charm risk closer to expiry
        time_factor = max(1 - (days_to_expiry / 7), 0.1)  # Weekly decay acceleration

        # Calculate total charm exposure
        total_charm = sum(abs(d['charm_exposure']) for d in charm_data)

        # Risk score based on charm magnitude and time
        risk_score = (total_charm / 100000) * time_factor

        if risk_score > 3:
            return 'HIGH'
        elif risk_score > 1.5:
            return 'MEDIUM'
        else:
            return 'LOW'

    except Exception:
        return 'LOW'

def find_key_charm_levels(charm_data, current_price):
    """Find key charm exposure levels"""
    levels = {
        'max_positive_charm': None,
        'max_negative_charm': None,
        'decay_acceleration_zone': None
    }

    # Validate input data
    if not charm_data or len(charm_data) == 0:
        print("Warning: Empty charm_data provided to find_key_charm_levels")
        return levels

    # Ensure all data has required fields
    valid_data = []
    for d in charm_data:
        if (isinstance(d, dict) and
            'charm_exposure' in d and
            'strike' in d and
            d['charm_exposure'] is not None and
            d['strike'] is not None):
            valid_data.append(d)

    if not valid_data:
        print("Warning: No valid charm data found")
        return levels

    charm_data = valid_data

    try:
        # Find maximum positive and negative charm levels
        max_pos_charm = max(charm_data, key=lambda x: x['charm_exposure'])
        max_neg_charm = min(charm_data, key=lambda x: x['charm_exposure'])

        levels['max_positive_charm'] = {
            'strike': max_pos_charm['strike'],
            'value': max_pos_charm['charm_exposure']
        }
        levels['max_negative_charm'] = {
            'strike': max_neg_charm['strike'],
            'value': max_neg_charm['charm_exposure']
        }

        # Find decay acceleration zone (highest absolute charm near current price)
        price_range = current_price * 0.05  # 5% range
        near_price_charm = [d for d in charm_data
                           if abs(d['strike'] - current_price) <= price_range]

        if near_price_charm:
            max_decay_zone = max(near_price_charm, key=lambda x: abs(x['charm_exposure']))
            levels['decay_acceleration_zone'] = {
                'strike': max_decay_zone['strike'],
                'value': max_decay_zone['charm_exposure']
            }
    except (ValueError, TypeError, KeyError) as e:
        print(f"Error finding charm levels: {e}")

    return levels

def generate_charm_entry_signals(charm_data, current_price, charm_regime, decay_rate):
    """Generate EOD entry signals based on charm analysis"""
    signals = []

    # Signal 1: Time Decay Acceleration Play
    if decay_rate > 60:
        if charm_regime == 'POSITIVE_CHARM':
            signals.append({
                'type': 'TIME_DECAY_LONG',
                'direction': 'LONG',
                'confidence': min(decay_rate, 85),
                'reasoning': 'Positive charm suggests delta increases as time passes (beneficial for longs)',
                'target_type': 'TIME_DECAY_PLAY',
                'time_horizon': 'EOD_CLOSE'
            })
        elif charm_regime == 'NEGATIVE_CHARM':
            signals.append({
                'type': 'TIME_DECAY_SHORT',
                'direction': 'SHORT',
                'confidence': min(decay_rate, 85),
                'reasoning': 'Negative charm suggests delta decreases as time passes (beneficial for shorts)',
                'target_type': 'TIME_DECAY_PLAY',
                'time_horizon': 'EOD_CLOSE'
            })

    # Signal 2: Charm Reversal Setup
    max_charm = max(abs(d['charm_exposure']) for d in charm_data)
    if max_charm > 200000:  # High charm concentration
        signals.append({
            'type': 'CHARM_REVERSAL',
            'direction': 'NEUTRAL',
            'confidence': 70,
            'reasoning': 'High charm concentration suggests potential reversal at EOD',
            'target_type': 'REVERSAL_PLAY',
            'time_horizon': 'EOD_TO_OPEN'
        })

    # Signal 3: Delta Stability Play
    if charm_regime == 'NEUTRAL_CHARM' and decay_rate < 30:
        signals.append({
            'type': 'DELTA_STABILITY',
            'direction': 'NEUTRAL',
            'confidence': 60,
            'reasoning': 'Low charm suggests delta stability through EOD',
            'target_type': 'STABILITY_PLAY',
            'time_horizon': 'OVERNIGHT'
        })

    return signals

def calculate_charm_signal_strength(charm_data, current_price):
    """Calculate overall charm signal strength"""
    # Validate input data
    if not charm_data or len(charm_data) == 0:
        return 0

    try:
        # Safe data handling for charm exposures
        charm_exposures = [
            abs(d.get('charm_exposure', 0))
            for d in charm_data
            if isinstance(d, dict) and d.get('charm_exposure') is not None
        ]

        volumes = [
            d.get('call_volume', 0) + d.get('put_volume', 0)
            for d in charm_data
            if isinstance(d, dict)
        ]

        max_charm = max(charm_exposures) if charm_exposures else 0
        total_volume = sum(volumes) if volumes else 0

        # Normalize to 0-100 scale
        charm_strength = min(max_charm / 200000 * 60, 60)  # Max 60 points from charm
        volume_strength = min(total_volume / 2000 * 40, 40)  # Max 40 points from volume

        return min(charm_strength + volume_strength, 100)
    except (TypeError, ValueError) as e:
        print(f"Error calculating charm signal strength: {e}")
        return 0

def calculate_charm_market_bias(charm_data, current_price, charm_regime, decay_rate):
    """Calculate market bias based on charm positioning and time decay"""
    bias = {
        'direction': 'NEUTRAL',
        'strength': 0,
        'reason': '',
        'factors': []
    }

    # Validate input data
    if not charm_data or len(charm_data) == 0:
        bias['reason'] = 'No charm data available'
        return bias

    factors = []
    bias_score = 0

    # Factor 1: Charm regime (Weight: ±1.0)
    if charm_regime == 'POSITIVE_CHARM':
        factors.append('Positive Charm Regime (Delta increases with time decay)')
        bias_score += 1.0
    elif charm_regime == 'NEGATIVE_CHARM':
        factors.append('Negative Charm Regime (Delta decreases with time decay)')
        bias_score -= 1.0

    # Factor 2: Time decay rate (Weight: ±0.6)
    if decay_rate > 70:
        if charm_regime == 'POSITIVE_CHARM':
            factors.append('High time decay rate favors longs')
            bias_score += 0.6
        elif charm_regime == 'NEGATIVE_CHARM':
            factors.append('High time decay rate favors shorts')
            bias_score -= 0.6
    elif decay_rate > 40:
        if charm_regime == 'POSITIVE_CHARM':
            factors.append('Moderate time decay rate supports longs')
            bias_score += 0.3
        elif charm_regime == 'NEGATIVE_CHARM':
            factors.append('Moderate time decay rate supports shorts')
            bias_score -= 0.3

    # Factor 3: Charm concentration near current price (Weight: ±0.4)
    try:
        near_price_charm = [d for d in charm_data if abs(d['strike'] - current_price) <= 5]
        if near_price_charm:
            total_near_charm = sum(d['charm_exposure'] for d in near_price_charm)
            if total_near_charm > 50000:
                factors.append('High charm concentration near current price')
                bias_score += 0.4 if total_near_charm > 0 else -0.4
    except (TypeError, KeyError):
        pass

    # Determine final bias
    if bias_score > 0.5:
        bias['direction'] = 'BULLISH'
        bias['strength'] = min(abs(bias_score) * 50, 100)
    elif bias_score < -0.5:
        bias['direction'] = 'BEARISH'
        bias['strength'] = min(abs(bias_score) * 50, 100)
    else:
        bias['direction'] = 'NEUTRAL'
        bias['strength'] = 0

    bias['factors'] = factors
    bias['reason'] = f"Score: {bias_score:.2f} based on {len(factors)} factors"

    return bias

def calculate_charm_signal_confidence(charm_data, current_price, bias_analysis, decay_rate):
    """Calculate confidence level for charm signals"""
    confidence_factors = []
    confidence_score = 0

    # Validate input data
    if not charm_data or len(charm_data) == 0:
        return {
            'score': 0,
            'factors': ['No data available'],
            'level': 'LOW'
        }

    if not bias_analysis or not isinstance(bias_analysis, dict):
        bias_analysis = {'strength': 0, 'factors': []}

    # Factor 1: Bias strength (Max 30 points)
    bias_strength = bias_analysis.get('strength', 0)
    if bias_strength > 70:
        confidence_score += 30
        confidence_factors.append('Strong charm bias signal')
    elif bias_strength > 40:
        confidence_score += 20
        confidence_factors.append('Moderate charm bias signal')

    # Factor 2: Time decay rate (Max 25 points)
    if decay_rate > 70:
        confidence_score += 25
        confidence_factors.append('High time decay rate')
    elif decay_rate > 40:
        confidence_score += 15
        confidence_factors.append('Moderate time decay rate')

    # Factor 3: Charm magnitude (Max 25 points)
    try:
        charm_exposures = [abs(d.get('charm_exposure', 0)) for d in charm_data]
        max_charm = max(charm_exposures) if charm_exposures else 0
        if max_charm > 200000:
            confidence_score += 25
            confidence_factors.append('High charm exposure magnitude')
        elif max_charm > 100000:
            confidence_score += 15
            confidence_factors.append('Moderate charm exposure magnitude')
    except (TypeError, ValueError):
        pass

    # Factor 4: Volume confirmation (Max 20 points)
    try:
        volumes = [d.get('call_volume', 0) + d.get('put_volume', 0) for d in charm_data]
        total_volume = sum(volumes) if volumes else 0
        if total_volume > 1000:
            confidence_score += 20
            confidence_factors.append('High volume confirmation')
        elif total_volume > 500:
            confidence_score += 10
            confidence_factors.append('Moderate volume confirmation')
    except (TypeError, ValueError):
        pass

    # Determine confidence level
    if confidence_score >= 70:
        confidence_level = 'HIGH'
    elif confidence_score >= 40:
        confidence_level = 'MEDIUM'
    else:
        confidence_level = 'LOW'

    return {
        'score': confidence_score,
        'factors': confidence_factors,
        'level': confidence_level
    }

def generate_simple_charm_signal(bias_analysis, confidence):
    """Generate simple BUY/SELL signal based on charm bias and confidence"""

    # Validate inputs
    if not bias_analysis or not isinstance(bias_analysis, dict):
        return {
            'action': 'HOLD',
            'reason': 'No charm bias data available',
            'confidence_level': 'LOW'
        }

    if not confidence or not isinstance(confidence, dict):
        confidence = {'level': 'LOW', 'score': 0}

    direction = bias_analysis.get('direction', 'NEUTRAL')
    strength = bias_analysis.get('strength', 0)
    confidence_level = confidence.get('level', 'LOW')
    confidence_score = confidence.get('score', 0)

    # Generate signal based on bias and confidence
    if direction == 'BULLISH' and strength >= 50:
        if confidence_level in ['HIGH', 'MEDIUM'] and confidence_score >= 40:
            return {
                'action': 'BUY',
                'reason': f'Strong bullish charm bias ({strength:.1f}%) with {confidence_level.lower()} confidence',
                'confidence_level': confidence_level
            }
        else:
            return {
                'action': 'HOLD',
                'reason': f'Bullish charm bias ({strength:.1f}%) but low confidence ({confidence_score}%)',
                'confidence_level': confidence_level
            }
    elif direction == 'BEARISH' and strength >= 50:
        if confidence_level in ['HIGH', 'MEDIUM'] and confidence_score >= 40:
            return {
                'action': 'SELL',
                'reason': f'Strong bearish charm bias ({strength:.1f}%) with {confidence_level.lower()} confidence',
                'confidence_level': confidence_level
            }
        else:
            return {
                'action': 'HOLD',
                'reason': f'Bearish charm bias ({strength:.1f}%) but low confidence ({confidence_score}%)',
                'confidence_level': confidence_level
            }
    else:
        return {
            'action': 'HOLD',
            'reason': f'Neutral or weak charm bias ({direction}, {strength:.1f}%)',
            'confidence_level': confidence_level
        }

@app.route('/api/shutdown', methods=['POST'])
def shutdown_server():
    """API endpoint to gracefully shutdown the server and all associated processes"""
    try:
        print("🔌 Shutdown request received...")

        # Send response immediately
        def comprehensive_shutdown():
            import time
            import os
            import signal
            import threading
            import sys
            import gc

            # Give time for response to be sent
            time.sleep(1)

            print("🔌 Initiating comprehensive shutdown...")

            # Step 1: Stop all background threads and cleanup
            try:
                print("🔌 Cleaning up application resources...")

                # Run all registered cleanup handlers
                cleanup_all_resources()

                # Clear global data
                global current_data
                current_data.clear()

                # Force garbage collection
                gc.collect()

            except Exception as e:
                print(f"Resource cleanup failed: {e}")

            # Step 2: SocketIO shutdown
            try:
                if socketio:
                    print("🔌 Stopping SocketIO server...")
                    socketio.stop()
                    time.sleep(0.5)
            except Exception as e:
                print(f"SocketIO shutdown failed: {e}")

            # Step 3: Stop all active threads
            try:
                print("🔌 Stopping background threads...")
                active_threads = threading.active_count()
                print(f"🔌 Found {active_threads} active threads")

                # Get all threads except main thread
                for thread in threading.enumerate():
                    if thread != threading.main_thread() and thread.is_alive():
                        print(f"🔌 Stopping thread: {thread.name}")
                        if hasattr(thread, '_stop'):
                            thread._stop()

                time.sleep(0.5)  # Give threads time to stop

            except Exception as e:
                print(f"Thread cleanup failed: {e}")

            # Step 4: Close any open file handles or network connections
            try:
                print("🔌 Closing file handles and connections...")

                # Close any matplotlib figures if they exist
                try:
                    import matplotlib.pyplot as plt
                    plt.close('all')
                except ImportError:
                    pass

                # Force close any remaining network connections
                import socket

            except Exception as e:
                print(f"Handle cleanup failed: {e}")

            # Step 5: Platform-specific process termination
            try:
                import platform
                import psutil

                # Get current process and all its children
                current_process = psutil.Process(os.getpid())
                children = current_process.children(recursive=True)

                print(f"🔌 Found {len(children)} child processes")

                # Terminate child processes first
                for child in children:
                    try:
                        print(f"🔌 Terminating child process: {child.pid}")
                        child.terminate()
                    except psutil.NoSuchProcess:
                        pass

                # Wait for children to terminate
                psutil.wait_procs(children, timeout=3)

                # Kill any remaining children
                for child in children:
                    try:
                        if child.is_running():
                            print(f"🔌 Force killing child process: {child.pid}")
                            child.kill()
                    except psutil.NoSuchProcess:
                        pass

            except ImportError:
                print("🔌 psutil not available, using basic process termination")
            except Exception as e:
                print(f"Process cleanup failed: {e}")

            # Step 6: Final termination
            try:
                import platform
                if platform.system() == "Windows":
                    print("🔌 Windows detected - using direct exit...")
                    os._exit(0)
                else:
                    print("🔌 Unix system - sending SIGINT signal...")
                    os.kill(os.getpid(), signal.SIGINT)
            except Exception as e:
                print(f"Platform-specific shutdown failed: {e}")

                try:
                    print("🔌 Trying alternative shutdown method...")
                    if hasattr(signal, 'SIGTERM'):
                        os.kill(os.getpid(), signal.SIGTERM)
                    else:
                        os._exit(0)
                except Exception as e2:
                    print(f"Alternative shutdown failed: {e2}")

                    try:
                        print("🔌 Force shutdown (last resort)...")
                        sys.exit(0)
                    except Exception as e3:
                        print(f"All shutdown methods failed: {e3}")
                        os._exit(1)

        # Start comprehensive shutdown in background thread
        import threading
        shutdown_thread = threading.Thread(target=comprehensive_shutdown, daemon=True)
        shutdown_thread.start()

        return jsonify({
            'success': True,
            'message': 'Server and all processes are shutting down...'
        })

    except Exception as e:
        print(f"Shutdown error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })



# WebSocket events for real-time updates (only if SocketIO is available)
if socketio:
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        print('Client connected')
        emit('status', {'msg': 'Connected to Options Dashboard'})

    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        print('Client disconnected')

    @socketio.on('subscribe_ticker')
    def handle_subscribe_ticker(data):
        """Handle ticker subscription for real-time updates"""
        ticker = data.get('ticker', '').upper()
        expiry_date = data.get('expiry_date', '')

        if ticker and expiry_date:
            # Start real-time updates for this ticker
            emit('ticker_subscribed', {
                'ticker': ticker,
                'expiry_date': expiry_date,
                'message': f'Subscribed to {ticker} updates'
            })

# Register cleanup handler for SocketIO
if socketio:
    def socketio_cleanup():
        """Cleanup handler for SocketIO"""
        try:
            print("🔌 SocketIO cleanup handler called")
            socketio.stop()
        except Exception as e:
            print(f"SocketIO cleanup error: {e}")

    register_cleanup_handler(socketio_cleanup)

# License System Routes
if LICENSE_SYSTEM_AVAILABLE:
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """License-based login page"""
        if request.method == 'POST':
            username = request.form.get('username', '').strip()
            license_key = request.form.get('license_key', '').strip().upper()

            if not username:
                flash('Please enter your username', 'error')
                return render_template('login.html')

            if not license_key:
                flash('Please enter a license key', 'error')
                return render_template('login.html')

            # Attempt login with username validation
            success, message = license_auth.login_user(license_key, username)

            if success:
                flash('Login successful!', 'success')
                # Redirect to original URL or dashboard
                next_url = session.pop('next_url', url_for('index'))
                return redirect(next_url)
            else:
                flash(message, 'error')
                return render_template('login.html')

        # GET request - show login form
        return render_template('login.html')

    @app.route('/logout')
    def logout():
        """Logout user"""
        if LICENSE_SYSTEM_AVAILABLE:
            license_auth.logout_user()

        # Clear all session data
        session.clear()

        flash('You have been logged out', 'info')
        return redirect(url_for('login'))

    @app.route('/api/logout', methods=['POST'])
    def api_logout():
        """API endpoint for logout"""
        try:
            if LICENSE_SYSTEM_AVAILABLE:
                license_auth.logout_user()

            # Clear all session data
            session.clear()

            return jsonify({
                'success': True,
                'message': 'Logged out successfully'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    @app.route('/clear-session')
    def clear_session():
        """Debug route to clear session"""
        session.clear()
        return redirect(url_for('login'))

    @app.route('/admin/refresh-cache', methods=['POST'])
    @login_required
    def admin_refresh_cache():
        """Refresh license cache (Administrator only)"""
        user_info = current_user()

        if not check_admin_only_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            from license_manager import refresh_license_cache
            result = refresh_license_cache()

            if result:
                return jsonify({
                    'success': True,
                    'message': 'License cache refreshed successfully',
                    'source': result.get('source', 'unknown')
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Failed to refresh license cache'
                })

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    def get_developers_list():
        """Get list of developers from developer manager"""
        try:
            from developer_manager import get_developer_manager
            manager = get_developer_manager()
            return manager.get_developer_usernames()
        except Exception as e:
            print(f"Error getting developers list: {e}")
            # Fallback to admin users
            admin_users = os.environ.get('LICENSE_ADMIN_USERS', '').split(',')
            return [user.strip() for user in admin_users if user.strip()]

    @app.route('/admin')
    @login_required
    def admin():
        """Admin dashboard for license management"""
        user_info = current_user()

        if not user_info:
            flash('Access denied. Please log in.', 'error')
            return redirect(url_for('login'))

        username = user_info.get('username', '')

        # Check if user has admin or developer privileges
        admin_users = ['admin', 'administrator']
        is_admin = username in admin_users

        # Check if user is developer
        is_developer = False
        try:
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()
            is_developer = dev_manager.is_developer(username)
        except Exception as e:
            print(f"Error checking developer status: {e}")

        # Allow access for admins or developers
        if not (is_admin or is_developer):
            flash('Access denied. Developer privileges required.', 'error')
            return redirect(url_for('index'))

        # Get license statistics and list
        admin = get_license_admin()
        license_result = admin.list_licenses()

        if not license_result['success']:
            # If GitHub is not available, create a demo license list with the known working license
            print(f"GitHub not available, creating demo license list: {license_result.get('error', 'Unknown error')}")
            licenses = [
                {
                    'license_key': 'DEV-52344-E1C3',
                    'discord_id': '987654321098765432',
                    'username': 'demo_user',
                    'expiry_date': '2025/12/31',
                    'is_expired': False,
                    'days_remaining': 192
                }
            ]
            stats = {'total_licenses': 1, 'active_licenses': 1, 'expired_licenses': 0, 'expiring_soon': 0}
        else:
            licenses = license_result['licenses']

            # Calculate statistics
            total_licenses = len(licenses)
            active_licenses = sum(1 for l in licenses if not l['is_expired'] and l['days_remaining'] > 7)
            expired_licenses = sum(1 for l in licenses if l['is_expired'])
            expiring_soon = sum(1 for l in licenses if not l['is_expired'] and l['days_remaining'] <= 7)

            stats = {
                'total_licenses': total_licenses,
                'active_licenses': active_licenses,
                'expired_licenses': expired_licenses,
                'expiring_soon': expiring_soon
            }

        # Get developers list and information
        try:
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()
            developers_list = dev_manager.get_developer_usernames()
            developers_info = dev_manager.get_developers()
        except Exception as e:
            print(f"Error getting developer info: {e}")
            developers_list = get_developers_list()
            developers_info = []

        # Recent activity (mock data for now)
        recent_activity = [
            {'action': 'License Created', 'license_key': 'DEV-12345-ABCD', 'username': 'new_user', 'date': '2025-06-22', 'icon': 'plus'},
            {'action': 'License Extended', 'license_key': 'DEV-67890-EFGH', 'username': 'existing_user', 'date': '2025-06-21', 'icon': 'clock'},
        ]

        # Configuration info
        config = {
            'github_repo': os.environ.get('GITHUB_REPO', 'Not configured'),
            'license_file': os.environ.get('LICENSE_FILE', 'licenses.txt'),
            'admin_users': os.environ.get('LICENSE_ADMIN_USERS', '').split(','),
            'webhook_enabled': os.environ.get('DISCORD_WEBHOOK_ENABLED', 'false').lower() == 'true',
            'login_notifications': os.environ.get('DISCORD_NOTIFY_LOGINS', 'false').lower() == 'true'
        }

        # Determine user role for display
        user_role = get_user_role(user_info)

        return render_template('admin.html',
                             licenses=licenses,
                             stats=stats,
                             developers=developers_list,
                             developers_info=developers_info,
                             recent_activity=recent_activity,
                             config=config,
                             current_user_info=user_info,
                             user_role=user_role)

    def check_admin_access(user_info):
        """Helper function to check if user has developer access (highest level)"""
        if not user_info:
            return False

        username = user_info.get('username', '')

        # Check if user is developer (highest level of access)
        try:
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()
            return dev_manager.is_developer(username)
        except Exception as e:
            print(f"Error checking developer status: {e}")
            return False

    def check_admin_only_access(user_info):
        """Helper function to check if user has developer access (same as admin access now)"""
        return check_admin_access(user_info)

    def get_user_role(user_info):
        """Get the user's role (Developer or User)"""
        if not user_info:
            return "User"

        username = user_info.get('username', '')

        try:
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()
            if dev_manager.is_developer(username):
                return "Developer"
        except Exception as e:
            print(f"Error checking developer status: {e}")

        return "User"

    @app.route('/admin/add-license', methods=['POST'])
    @login_required
    def admin_add_license():
        """Add a new license"""
        user_info = current_user()

        if not check_admin_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            discord_id = data.get('discord_id')
            username = data.get('username')
            days_valid = data.get('days_valid', 30)

            admin = get_license_admin()
            result = admin.add_license(discord_id, username, days_valid)

            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/remove-license', methods=['POST'])
    @login_required
    def admin_remove_license():
        """Remove a license"""
        user_info = current_user()

        if not check_admin_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            license_key = data.get('license_key')

            admin = get_license_admin()
            result = admin.remove_license(license_key)

            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/extend-license', methods=['POST'])
    @login_required
    def admin_extend_license():
        """Extend a license"""
        user_info = current_user()

        if not check_admin_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            license_key = data.get('license_key')
            additional_days = data.get('additional_days', 30)

            admin = get_license_admin()
            result = admin.extend_license(license_key, additional_days)

            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/add-developer', methods=['POST'])
    @login_required
    def admin_add_developer():
        """Add a user as a developer (Administrator only)"""
        user_info = current_user()

        if not check_admin_only_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            username = data.get('username')
            protected = data.get('protected', False)

            if not username:
                return jsonify({'success': False, 'error': 'Username is required'})

            # Use developer manager to add developer
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()

            result = dev_manager.add_developer(username, added_by=user_info.get('username', 'admin'), protected=protected)

            if result['success']:
                # Send Discord notification if available
                try:
                    from discord_webhook import get_discord_webhook
                    webhook = get_discord_webhook()

                    # Create a simple notification for developer promotion
                    embed = {
                        "title": "👨‍💻 Developer Promoted",
                        "description": f"**{username}** has been promoted to developer status",
                        "color": 0xC0C0C0,
                        "fields": [
                            {
                                "name": "👤 Username",
                                "value": f"`{username}`",
                                "inline": True
                            },
                            {
                                "name": "👑 Promoted By",
                                "value": f"`{user_info.get('username', 'admin')}`",
                                "inline": True
                            },
                            {
                                "name": "📊 Status",
                                "value": "🟢 **Active Developer**",
                                "inline": True
                            }
                        ],
                        "timestamp": datetime.now().isoformat()
                    }

                    payload = {
                        "username": "Greek Terminal",
                        "embeds": [embed]
                    }

                    webhook._send_webhook(payload)
                except Exception as e:
                    print(f"Failed to send developer promotion notification: {e}")

                return jsonify(result)
            else:
                return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/remove-developer', methods=['POST'])
    @login_required
    def admin_remove_developer():
        """Remove a user from developer status (Administrator only)"""
        user_info = current_user()

        if not check_admin_only_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            target_username = data.get('username')

            if not target_username:
                return jsonify({'success': False, 'error': 'Username is required'})

            # Use developer manager to remove developer
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()

            username = user_info.get('username', 'admin')
            result = dev_manager.remove_developer(target_username, removed_by=username)

            if result['success']:
                # Send Discord notification if available
                try:
                    from discord_webhook import get_discord_webhook
                    webhook = get_discord_webhook()

                    # Create a notification for developer removal
                    embed = {
                        "title": "🗑️ Developer Removed",
                        "description": f"**{target_username}** has been removed from developer status",
                        "color": 0xFF6B6B,  # Red color
                        "fields": [
                            {
                                "name": "👤 Username",
                                "value": f"`{target_username}`",
                                "inline": True
                            },
                            {
                                "name": "👑 Removed By",
                                "value": f"`{username}`",
                                "inline": True
                            },
                            {
                                "name": "📊 Status",
                                "value": "🔴 **Developer Access Revoked**",
                                "inline": True
                            }
                        ],
                        "timestamp": datetime.now().isoformat()
                    }

                    payload = {
                        "username": "Greek Terminal",
                        "embeds": [embed]
                    }

                    webhook._send_webhook(payload)
                except Exception as e:
                    print(f"Failed to send developer removal notification: {e}")

                return jsonify(result)
            else:
                return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/toggle-developer-protection', methods=['POST'])
    @login_required
    def admin_toggle_developer_protection():
        """Toggle protected status for a developer (Administrator only)"""
        user_info = current_user()

        if not check_admin_only_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            target_username = data.get('username')
            protected = data.get('protected', False)

            if not target_username:
                return jsonify({'success': False, 'error': 'Username is required'})

            # Use developer manager to toggle protection
            from developer_manager import get_developer_manager
            dev_manager = get_developer_manager()

            username = user_info.get('username', 'admin')
            result = dev_manager.toggle_protected_status(target_username, protected, changed_by=username)

            if result['success']:
                # Send Discord notification if available
                try:
                    from discord_webhook import get_discord_webhook
                    webhook = get_discord_webhook()

                    status_text = "Protected" if protected else "Unprotected"
                    status_emoji = "🛡️" if protected else "🔓"
                    color = 0xFFD700 if protected else 0x95A5A6  # Gold for protected, Gray for unprotected

                    # Create a notification for protection status change
                    embed = {
                        "title": f"{status_emoji} Developer Protection {status_text}",
                        "description": f"**{target_username}** has been marked as {status_text.lower()}",
                        "color": color,
                        "fields": [
                            {
                                "name": "👤 Username",
                                "value": f"`{target_username}`",
                                "inline": True
                            },
                            {
                                "name": "👑 Changed By",
                                "value": f"`{username}`",
                                "inline": True
                            },
                            {
                                "name": "📊 Status",
                                "value": f"{status_emoji} **{status_text} Developer**",
                                "inline": True
                            }
                        ],
                        "timestamp": datetime.now().isoformat()
                    }

                    payload = {
                        "username": "Greek Terminal",
                        "embeds": [embed]
                    }

                    webhook._send_webhook(payload)
                except Exception as e:
                    print(f"Failed to send protection status notification: {e}")

                return jsonify(result)
            else:
                return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/edit-license', methods=['POST'])
    @login_required
    def admin_edit_license():
        """Edit license details"""
        user_info = current_user()

        if not check_admin_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            data = request.get_json()
            license_key = data.get('license_key')
            username = data.get('username')
            discord_id = data.get('discord_id')

            if not all([license_key, username, discord_id]):
                return jsonify({'success': False, 'error': 'All fields are required'})

            admin = get_license_admin()

            # Remove old license and add new one with updated details
            remove_result = admin.remove_license(license_key)
            if not remove_result['success']:
                return jsonify({'success': False, 'error': f'Failed to remove old license: {remove_result["error"]}'})

            # Get the expiry date from the removed license
            removed_license = remove_result['removed_license']
            parts = removed_license.split('|')
            if len(parts) >= 4:
                expiry_date = parts[3]

                # Calculate days remaining
                try:
                    expiry_dt = datetime.strptime(expiry_date, '%Y/%m/%d')
                    days_remaining = max(0, (expiry_dt - datetime.now()).days)
                except:
                    days_remaining = 30  # Default if parsing fails

                # Add new license with updated details
                add_result = admin.add_license(discord_id, username, days_remaining)
                if add_result['success']:
                    return jsonify({'success': True, 'message': 'License updated successfully'})
                else:
                    return jsonify({'success': False, 'error': f'Failed to create updated license: {add_result["error"]}'})
            else:
                return jsonify({'success': False, 'error': 'Invalid license format'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/test-webhook', methods=['POST'])
    @login_required
    def admin_test_webhook():
        """Test Discord webhook (Administrator only)"""
        user_info = current_user()

        if not check_admin_only_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            from discord_webhook import test_webhook
            success = test_webhook()

            if success:
                return jsonify({'success': True, 'message': 'Webhook test successful'})
            else:
                return jsonify({'success': False, 'error': 'Webhook test failed'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    @app.route('/admin/cleanup-expired', methods=['POST'])
    @login_required
    def admin_cleanup_expired():
        """Cleanup expired licenses"""
        user_info = current_user()

        if not check_admin_access(user_info):
            return jsonify({'success': False, 'error': 'Access denied. Developer privileges required.'}), 403

        try:
            admin = get_license_admin()
            result = admin.cleanup_expired_licenses()
            return jsonify(result)

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

def run_dashboard():
    """Function to run the dashboard - can be called from external script"""
    try:
        # Create templates and static directories if they don't exist
        os.makedirs('templates', exist_ok=True)
        os.makedirs('static/css', exist_ok=True)
        os.makedirs('static/js', exist_ok=True)

        print("=" * 60)
        print("🚀 Greek Terminal")
        print("=" * 60)
        print("📊 Dashboard: http://localhost:5000")
        print("💡 Press Ctrl+C to stop")
        print("=" * 60)

        # For production, disable debug mode
        debug_mode = os.environ.get('FLASK_ENV', 'production') == 'development'

        if socketio:
            socketio.run(app, debug=debug_mode, host='localhost', port=5000)
        else:
            app.run(debug=debug_mode, host='localhost', port=5000)

    except Exception as e:
        print(f"Error in run_dashboard: {e}")
        raise

@app.route('/api/set-pricing-model', methods=['POST'])
def set_pricing_model_endpoint():
    """API endpoint to set the pricing model"""
    try:
        data = request.get_json()
        model_name = data.get('model', 'black_scholes')

        valid_models = ['black_scholes', 'sabr', 'dupire', 'heston', 'dupire_heston']
        if model_name not in valid_models:
            return jsonify({
                'success': False,
                'error': f'Invalid model: {model_name}. Must be one of {valid_models}'
            })

        set_pricing_model(model_name)

        # Clear any cached data to force recalculation with new model
        # This ensures that when charts are refreshed, they use the new pricing model
        print(f"✅ Pricing model changed to: {model_name}")
        print("   Charts will use the new model on next refresh")

        return jsonify({
            'success': True,
            'model': model_name,
            'message': f'Pricing model set to {model_name}. Charts will update on next refresh.'
        })
    except Exception as e:
        print(f"Error setting pricing model: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/set-sabr-parameters', methods=['POST'])
def set_sabr_parameters_endpoint():
    """API endpoint to set SABR model parameters"""
    try:
        data = request.get_json()
        alpha = data.get('alpha')
        beta = data.get('beta')
        rho = data.get('rho')
        nu = data.get('nu')

        set_sabr_parameters(alpha, beta, rho, nu)

        pricing_manager = get_pricing_manager()
        current_params = pricing_manager.get_sabr_params()

        return jsonify({
            'success': True,
            'parameters': current_params
        })
    except Exception as e:
        print(f"Error setting SABR parameters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/set-heston-parameters', methods=['POST'])
def set_heston_parameters_endpoint():
    """API endpoint to set Heston model parameters"""
    try:
        data = request.get_json()
        kappa = data.get('kappa')
        theta = data.get('theta')
        sigma = data.get('sigma')
        rho = data.get('rho')
        v0 = data.get('v0')

        set_heston_parameters(kappa, theta, sigma, rho, v0)

        pricing_manager = get_pricing_manager()
        current_params = pricing_manager.get_heston_params()

        return jsonify({
            'success': True,
            'parameters': current_params
        })
    except Exception as e:
        print(f"Error setting Heston parameters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/set-dupire-parameters', methods=['POST'])
def set_dupire_parameters_endpoint():
    """API endpoint to set Dupire model parameters"""
    try:
        data = request.get_json()
        spot_price = data.get('spot_price')
        risk_free_rate = data.get('risk_free_rate')
        dividend_yield = data.get('dividend_yield')

        set_dupire_parameters(spot_price, risk_free_rate, dividend_yield)

        pricing_manager = get_pricing_manager()
        current_params = pricing_manager.get_dupire_params()

        return jsonify({
            'success': True,
            'parameters': current_params
        })
    except Exception as e:
        print(f"Error setting Dupire parameters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/set-dupire-heston-parameters', methods=['POST'])
def set_dupire_heston_parameters_endpoint():
    """API endpoint to set Dupire-Heston hybrid model parameters"""
    try:
        data = request.get_json()
        dupire_weight = data.get('dupire_weight')
        correlation_adjustment = data.get('correlation_adjustment')

        set_dupire_heston_parameters(dupire_weight, correlation_adjustment)

        pricing_manager = get_pricing_manager()
        current_params = pricing_manager.get_dupire_heston_params()

        return jsonify({
            'success': True,
            'parameters': current_params
        })
    except Exception as e:
        print(f"Error setting Dupire-Heston parameters: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/get-pricing-model-info')
def get_pricing_model_info():
    """API endpoint to get current pricing model information"""
    try:
        pricing_manager = get_pricing_manager()
        model_info = pricing_manager.get_model_info()

        return jsonify({
            'success': True,
            'model_info': model_info
        })
    except Exception as e:
        print(f"Error getting pricing model info: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@app.route('/api/test-pricing-models')
def test_pricing_models():
    """Test endpoint to verify different models produce different results"""
    try:
        # Test parameters
        S = 100.0
        K = 100.0
        T = 0.25
        r = 0.05
        sigma = 0.2

        pricing_manager = get_pricing_manager()
        results = {}

        models = ['black_scholes', 'sabr', 'dupire', 'heston', 'dupire_heston']

        for model in models:
            try:
                pricing_manager.set_model(model)

                delta = pricing_manager.calculate_delta('c', S, K, T, r, sigma)
                gamma = pricing_manager.calculate_gamma('c', S, K, T, r, sigma)

                results[model] = {
                    'delta': round(delta, 6),
                    'gamma': round(gamma, 8)
                }
            except Exception as e:
                results[model] = {'error': str(e)}

        return jsonify({
            'success': True,
            'test_parameters': {'S': S, 'K': K, 'T': T, 'r': r, 'sigma': sigma},
            'results': results
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })


if __name__ == '__main__':
    run_dashboard()
