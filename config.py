#!/usr/bin/env python3
"""
Configuration settings for the Greek Terminal
Includes security settings and environment configuration
"""

import os
import secrets
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Security Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour
    
    # Session Configuration
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    # Session timeout removed - sessions persist until manual logout
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = "memory://"
    RATELIMIT_DEFAULT = "100 per hour"
    
    # Login Security
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_LOCKOUT_DURATION = timedelta(minutes=15)  # 15 minute lockout
    
    # Password Requirements
    MIN_PASSWORD_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_NUMBERS = True
    REQUIRE_SPECIAL_CHARS = True

    # Pricing Model Configuration
    DEFAULT_PRICING_MODEL = os.environ.get('DEFAULT_PRICING_MODEL', 'black_scholes')  # 'black_scholes' or 'sabr'
    ENABLE_MODEL_SWITCHING = os.environ.get('ENABLE_MODEL_SWITCHING', 'true').lower() == 'true'

    # SABR Model Default Parameters
    SABR_DEFAULT_ALPHA = float(os.environ.get('SABR_DEFAULT_ALPHA', '0.2'))
    SABR_DEFAULT_BETA = float(os.environ.get('SABR_DEFAULT_BETA', '0.5'))
    SABR_DEFAULT_RHO = float(os.environ.get('SABR_DEFAULT_RHO', '-0.3'))
    SABR_DEFAULT_NU = float(os.environ.get('SABR_DEFAULT_NU', '0.4'))
    
    # Application Settings
    FLASK_ENV = os.environ.get('FLASK_ENV', 'development')
    DEBUG = FLASK_ENV == 'development'

    # Trial system removed - using license system only
    
    # Database (for future user storage)
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///dashboard.db')
    
    # Default Admin Credentials (change these!)
    DEFAULT_ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', 'admin')
    DEFAULT_ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'ChangeMe123!')
    
    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdn.plot.ly https://unpkg.com https://cdn.socket.io https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self' ws: wss:;"
    }

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    SESSION_COOKIE_SECURE = False

    # Trial system removed

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True

    # Stricter rate limiting for production
    RATELIMIT_DEFAULT = "50 per hour"
    MAX_LOGIN_ATTEMPTS = 3
    LOGIN_LOCKOUT_DURATION = timedelta(minutes=30)

    # Trial system removed

# TrialConfig removed - using license system only

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
