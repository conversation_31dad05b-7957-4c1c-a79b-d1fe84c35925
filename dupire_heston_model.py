#!/usr/bin/env python3
"""
Dupire-Heston Hybrid Model Implementation
This module provides a hybrid model combining Dupire local volatility with Heston stochastic volatility.
"""

import numpy as np
import math
import cmath
from scipy.stats import norm
from scipy.integrate import quad
from scipy.interpolate import RectBivariateSpline, interp2d
import warnings

from dupire_model import DupireModel
from heston_model import HestonModel


class DupireHestonModel:
    """
    Dupire-Heston Hybrid Model implementation.
    
    This model combines the local volatility approach of Dupire with the 
    stochastic volatility features of <PERSON>ston. The hybrid model uses:
    
    1. Dupire local volatility surface for the deterministic component
    2. Heston stochastic volatility for the random component
    3. A mixing parameter to blend between the two approaches
    
    The effective volatility is: σ_eff = α * σ_local + (1-α) * σ_heston
    where α is the mixing parameter (0 ≤ α ≤ 1).
    """
    
    def __init__(self, dupire_weight=0.5, heston_params=None, dupire_params=None):
        """
        Initialize Dupire-Heston hybrid model.
        
        Args:
            dupire_weight (float): Weight for Dupire component (0-1)
            heston_params (dict): Heston model parameters
            dupire_params (dict): Dupire model parameters
        """
        self.dupire_weight = max(0.0, min(1.0, dupire_weight))  # Clamp to [0,1]
        self.heston_weight = 1.0 - self.dupire_weight
        
        # Initialize component models
        if heston_params is None:
            heston_params = {
                'kappa': 2.0,
                'theta': 0.04,
                'sigma': 0.3,
                'rho': -0.7,
                'v0': 0.04
            }
        
        if dupire_params is None:
            dupire_params = {
                'spot_price': 100.0,
                'risk_free_rate': 0.05,
                'dividend_yield': 0.0
            }
        
        self.heston_model = HestonModel(**heston_params)
        self.dupire_model = DupireModel(**dupire_params)
        
        # Hybrid model parameters
        self.correlation_adjustment = 0.1  # Adjustment for model correlation
        self.volatility_floor = 0.01  # Minimum volatility
        self.volatility_cap = 2.0     # Maximum volatility
        
    def set_dupire_weight(self, weight):
        """Set the weight for Dupire component."""
        self.dupire_weight = max(0.0, min(1.0, weight))
        self.heston_weight = 1.0 - self.dupire_weight
    
    def get_effective_volatility(self, S, T):
        """
        Calculate effective volatility combining Dupire and Heston components.
        
        Args:
            S (float): Spot price
            T (float): Time to expiration
            
        Returns:
            float: Effective volatility
        """
        if T <= 0:
            return self.volatility_floor
        
        try:
            # Get Dupire local volatility
            dupire_vol = self.dupire_model.get_local_volatility(S, T)
            
            # Get Heston instantaneous volatility
            heston_vol = math.sqrt(self.heston_model.v0)
            
            # Combine with weights
            effective_vol = (self.dupire_weight * dupire_vol + 
                           self.heston_weight * heston_vol)
            
            # Apply correlation adjustment
            correlation_factor = 1.0 + self.correlation_adjustment * abs(self.heston_model.rho)
            effective_vol *= correlation_factor
            
            # Apply bounds
            effective_vol = max(self.volatility_floor, min(self.volatility_cap, effective_vol))
            
            return effective_vol
            
        except Exception as e:
            warnings.warn(f"Error calculating effective volatility: {e}")
            return math.sqrt(self.heston_model.v0)
    
    def option_price(self, S, K, T, r, option_type='call', q=0.0):
        """
        Calculate option price using hybrid model.
        
        The hybrid approach uses a weighted combination of:
        1. Dupire model price (with local volatility)
        2. Heston model price (with stochastic volatility)
        3. Correlation adjustment between the models
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            option_type (str): 'call' or 'put'
            q (float): Dividend yield
            
        Returns:
            float: Option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        try:
            # Get prices from both models
            dupire_price = self.dupire_model.option_price(S, K, T, r, option_type)
            heston_price = self.heston_model.option_price(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_price = (self.dupire_weight * dupire_price + 
                          self.heston_weight * heston_price)
            
            # Apply model correlation adjustment
            moneyness = S / K
            correlation_adjustment = self._calculate_correlation_adjustment(moneyness, T)
            hybrid_price *= (1.0 + correlation_adjustment)
            
            return max(hybrid_price, 0.0)
            
        except Exception as e:
            warnings.warn(f"Hybrid pricing failed: {e}, using fallback")
            return self._fallback_price(S, K, T, r, option_type, q)
    
    def _calculate_correlation_adjustment(self, moneyness, T):
        """
        Calculate correlation adjustment based on moneyness and time.
        
        Args:
            moneyness (float): S/K ratio
            T (float): Time to expiration
            
        Returns:
            float: Correlation adjustment factor
        """
        # Adjustment is stronger for OTM options and shorter times
        otm_factor = abs(moneyness - 1.0)  # Distance from ATM
        time_factor = math.exp(-T)  # Stronger for shorter times
        
        adjustment = self.correlation_adjustment * otm_factor * time_factor
        
        # Apply Heston correlation influence
        rho_influence = abs(self.heston_model.rho) * 0.5
        adjustment *= (1.0 + rho_influence)
        
        return adjustment
    
    def _fallback_price(self, S, K, T, r, option_type, q=0.0):
        """Fallback pricing using effective volatility in Black-Scholes."""
        effective_vol = self.get_effective_volatility(S, T)
        return self._black_scholes_price(S, K, T, r, effective_vol, option_type, q)
    
    def _black_scholes_price(self, S, K, T, r, sigma, option_type, q=0.0):
        """Calculate Black-Scholes price with given volatility."""
        if T <= 0 or sigma <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1 = (math.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        if option_type.lower() == 'call':
            price = S * math.exp(-q * T) * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * math.exp(-q * T) * norm.cdf(-d1)
        
        return max(price, 0)
    
    def delta(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate delta using hybrid model."""
        if T <= 0:
            if option_type.lower() == 'call':
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0
        
        try:
            # Get deltas from both models
            dupire_delta = self.dupire_model.delta(S, K, T, r, option_type)
            heston_delta = self.heston_model.delta(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_delta = (self.dupire_weight * dupire_delta + 
                          self.heston_weight * heston_delta)
            
            return hybrid_delta
            
        except Exception as e:
            warnings.warn(f"Hybrid delta calculation failed: {e}")
            return self._fallback_delta(S, K, T, r, option_type, q)
    
    def _fallback_delta(self, S, K, T, r, option_type, q=0.0):
        """Fallback delta calculation."""
        effective_vol = self.get_effective_volatility(S, T)
        d1 = (math.log(S / K) + (r - q + 0.5 * effective_vol**2) * T) / (effective_vol * math.sqrt(T))
        
        if option_type.lower() == 'call':
            return math.exp(-q * T) * norm.cdf(d1)
        else:
            return math.exp(-q * T) * (norm.cdf(d1) - 1)
    
    def gamma(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate gamma using hybrid model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        try:
            # Get gammas from both models
            dupire_gamma = self.dupire_model.gamma(S, K, T, r, option_type)
            heston_gamma = self.heston_model.gamma(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_gamma = (self.dupire_weight * dupire_gamma + 
                          self.heston_weight * heston_gamma)
            
            return max(hybrid_gamma, 0.0)
            
        except Exception as e:
            warnings.warn(f"Hybrid gamma calculation failed: {e}")
            return self._fallback_gamma(S, K, T, r, option_type, q)
    
    def _fallback_gamma(self, S, K, T, r, option_type, q=0.0):
        """Fallback gamma calculation."""
        effective_vol = self.get_effective_volatility(S, T)
        d1 = (math.log(S / K) + (r - q + 0.5 * effective_vol**2) * T) / (effective_vol * math.sqrt(T))
        
        return math.exp(-q * T) * norm.pdf(d1) / (S * effective_vol * math.sqrt(T))
    
    def vega(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate vega using hybrid model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        try:
            # Get vegas from both models
            dupire_vega = self.dupire_model.vega(S, K, T, r, option_type)
            heston_vega = self.heston_model.vega(S, K, T, r, option_type, q)
            
            # Weighted combination with volatility sensitivity adjustment
            hybrid_vega = (self.dupire_weight * dupire_vega + 
                         self.heston_weight * heston_vega)
            
            # Apply hybrid adjustment for volatility sensitivity
            vol_sensitivity_factor = 1.0 + 0.2 * self.heston_weight  # Heston adds vol sensitivity
            hybrid_vega *= vol_sensitivity_factor
            
            return hybrid_vega
            
        except Exception as e:
            warnings.warn(f"Hybrid vega calculation failed: {e}")
            return self._fallback_vega(S, K, T, r, option_type, q)
    
    def _fallback_vega(self, S, K, T, r, option_type, q=0.0):
        """Fallback vega calculation."""
        effective_vol = self.get_effective_volatility(S, T)
        d1 = (math.log(S / K) + (r - q + 0.5 * effective_vol**2) * T) / (effective_vol * math.sqrt(T))
        
        return S * math.exp(-q * T) * norm.pdf(d1) * math.sqrt(T) / 100
    
    def theta(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate theta using hybrid model."""
        if T <= 0:
            return 0.0
        
        try:
            # Get thetas from both models
            dupire_theta = self.dupire_model.theta(S, K, T, r, option_type)
            heston_theta = self.heston_model.theta(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_theta = (self.dupire_weight * dupire_theta + 
                          self.heston_weight * heston_theta)
            
            return hybrid_theta
            
        except Exception as e:
            warnings.warn(f"Hybrid theta calculation failed: {e}")
            return self._fallback_theta(S, K, T, r, option_type, q)
    
    def _fallback_theta(self, S, K, T, r, option_type, q=0.0):
        """Fallback theta calculation."""
        effective_vol = self.get_effective_volatility(S, T)
        d1 = (math.log(S / K) + (r - q + 0.5 * effective_vol**2) * T) / (effective_vol * math.sqrt(T))
        d2 = d1 - effective_vol * math.sqrt(T)
        
        if option_type.lower() == 'call':
            theta = (-S * math.exp(-q * T) * norm.pdf(d1) * effective_vol / (2 * math.sqrt(T))
                    - r * K * math.exp(-r * T) * norm.cdf(d2)
                    + q * S * math.exp(-q * T) * norm.cdf(d1))
        else:
            theta = (-S * math.exp(-q * T) * norm.pdf(d1) * effective_vol / (2 * math.sqrt(T))
                    + r * K * math.exp(-r * T) * norm.cdf(-d2)
                    - q * S * math.exp(-q * T) * norm.cdf(-d1))
        
        return theta / 365
    
    def vanna(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate vanna using hybrid model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        try:
            # Get vannas from both models
            dupire_vanna = self.dupire_model.vanna(S, K, T, r, option_type)
            heston_vanna = self.heston_model.vanna(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_vanna = (self.dupire_weight * dupire_vanna + 
                          self.heston_weight * heston_vanna)
            
            return hybrid_vanna
            
        except Exception as e:
            warnings.warn(f"Hybrid vanna calculation failed: {e}")
            return 0.0
    
    def charm(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate charm using hybrid model."""
        if T <= 0 or S <= 0:
            return 0.0
        
        try:
            # Get charms from both models
            dupire_charm = self.dupire_model.charm(S, K, T, r, option_type)
            heston_charm = self.heston_model.charm(S, K, T, r, option_type, q)
            
            # Weighted combination
            hybrid_charm = (self.dupire_weight * dupire_charm + 
                          self.heston_weight * heston_charm)
            
            return hybrid_charm
            
        except Exception as e:
            warnings.warn(f"Hybrid charm calculation failed: {e}")
            return 0.0
    
    def calibrate_dupire_surface(self, strikes, times, implied_vols, spot_price=None):
        """Calibrate the Dupire component from market data."""
        self.dupire_model.calibrate_from_market_data(strikes, times, implied_vols, spot_price)
    
    def update_heston_parameters(self, **params):
        """Update Heston model parameters."""
        for param, value in params.items():
            if hasattr(self.heston_model, param):
                setattr(self.heston_model, param, value)
        
        # Re-validate parameters
        self.heston_model._validate_parameters()
    
    def get_model_info(self):
        """Get information about the hybrid model."""
        return {
            'dupire_weight': self.dupire_weight,
            'heston_weight': self.heston_weight,
            'heston_params': {
                'kappa': self.heston_model.kappa,
                'theta': self.heston_model.theta_param,
                'sigma': self.heston_model.sigma,
                'rho': self.heston_model.rho,
                'v0': self.heston_model.v0
            },
            'correlation_adjustment': self.correlation_adjustment,
            'volatility_bounds': [self.volatility_floor, self.volatility_cap]
        }


# Default Dupire-Heston hybrid model instance
default_dupire_heston = DupireHestonModel()


def dupire_heston_option_price(flag, S, K, T, r, dupire_weight=0.5,
                              kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston option pricing."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.option_price(S, K, T, r, option_type, q)


def dupire_heston_delta(flag, S, K, T, r, dupire_weight=0.5,
                       kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston delta calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.delta(S, K, T, r, option_type, q)


def dupire_heston_gamma(flag, S, K, T, r, dupire_weight=0.5,
                       kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston gamma calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.gamma(S, K, T, r, option_type, q)


def dupire_heston_vega(flag, S, K, T, r, dupire_weight=0.5,
                      kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston vega calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.vega(S, K, T, r, option_type, q)


def dupire_heston_theta(flag, S, K, T, r, dupire_weight=0.5,
                       kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston theta calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.theta(S, K, T, r, option_type, q)


def dupire_heston_vanna(flag, S, K, T, r, dupire_weight=0.5,
                       kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston vanna calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.vanna(S, K, T, r, option_type, q)


def dupire_heston_charm(flag, S, K, T, r, dupire_weight=0.5,
                       kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Dupire-Heston charm calculation."""
    heston_params = {'kappa': kappa, 'theta': theta, 'sigma': sigma, 'rho': rho, 'v0': v0}
    model = DupireHestonModel(dupire_weight=dupire_weight, heston_params=heston_params)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.charm(S, K, T, r, option_type, q)
