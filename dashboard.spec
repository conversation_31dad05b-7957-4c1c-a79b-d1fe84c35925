# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Collect all data files
datas = [
    ('templates', 'templates'),
    ('static', 'static'),
    ('config.py', '.'),
    ('build_info.json', '.'),
    ('.env', '.')  # Include .env file for license system
]

# Hidden imports for packages that PyInstaller might miss
hiddenimports = [
    'flask',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'jinja2',
    'jinja2.ext',
    'markupsafe',
    'itsdangerous',
    'click',
    'dnspython',
    'dns',
    'dns.resolver',
    'dns.reversename',
    'dns.e164',
    'yfinance',
    'pandas',
    'numpy',
    'scipy',
    'py_vollib',
    'py_vollib.black_scholes',
    'py_vollib.black_scholes.greeks',
    'py_vollib.black_scholes.implied_volatility',
    'sabr_model',
    'dupire_model',
    'heston_model',
    'dupire_heston_model',
    'pricing_models',
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'cryptography.hazmat.primitives',
    'cryptography.hazmat.primitives.hashes',
    'cryptography.hazmat.primitives.kdf',
    'cryptography.hazmat.primitives.kdf.pbkdf2',
    # License system imports
    'hashlib',
    'hmac',
    'base64',
    'platform',
    'dotenv',
    # Expiration functionality imports
    'json',
    'tempfile',
    'subprocess',
    'datetime',
    'ctypes',
    'ctypes.wintypes'
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GreekTerminal',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
