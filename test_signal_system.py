#!/usr/bin/env python3
"""
Test script for the Greek Terminal Signal System
Tests the core signal calculation functions with various edge cases
"""

import sys
import os

# Add the current directory to Python path to import app functions
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_empty_data_handling():
    """Test signal functions with empty data"""
    print("Testing empty data handling...")
    
    # Import the functions we need to test
    from app import (
        find_key_gamma_levels, 
        calculate_market_bias, 
        calculate_signal_confidence,
        calculate_signal_strength
    )
    
    # Test with empty data
    empty_data = []
    current_price = 100.0
    
    # Test find_key_gamma_levels
    levels = find_key_gamma_levels(empty_data, current_price)
    assert levels['zero_gex'] is None, "Empty data should return None for zero_gex"
    assert levels['max_positive_gex'] is None, "Empty data should return None for max_positive_gex"
    print("✅ find_key_gamma_levels handles empty data correctly")
    
    # Test calculate_market_bias
    bias = calculate_market_bias(empty_data, current_price, levels)
    assert bias['direction'] == 'NEUTRAL', "Empty data should return NEUTRAL bias"
    assert bias['strength'] == 0, "Empty data should return 0 strength"
    print("✅ calculate_market_bias handles empty data correctly")
    
    # Test calculate_signal_confidence
    confidence = calculate_signal_confidence(empty_data, current_price, bias)
    assert confidence['score'] == 0, "Empty data should return 0 confidence score"
    assert confidence['level'] == 'LOW', "Empty data should return LOW confidence level"
    print("✅ calculate_signal_confidence handles empty data correctly")
    
    # Test calculate_signal_strength
    strength = calculate_signal_strength(empty_data, current_price)
    assert strength == 0, "Empty data should return 0 signal strength"
    print("✅ calculate_signal_strength handles empty data correctly")

def test_invalid_data_handling():
    """Test signal functions with invalid/malformed data"""
    print("\nTesting invalid data handling...")
    
    from app import find_key_gamma_levels, calculate_market_bias
    
    # Test with invalid data structures
    invalid_data = [
        {"strike": None, "gamma_exposure": 1000},  # None strike
        {"strike": 100, "gamma_exposure": None},   # None gamma_exposure
        {"strike": 100},                           # Missing gamma_exposure
        {"gamma_exposure": 1000},                  # Missing strike
        None,                                      # None entry
        "invalid",                                 # String instead of dict
    ]
    
    current_price = 100.0
    
    # These should not crash and should handle invalid data gracefully
    levels = find_key_gamma_levels(invalid_data, current_price)
    bias = calculate_market_bias(invalid_data, current_price, levels)
    
    print("✅ Functions handle invalid data without crashing")

def test_normal_data_processing():
    """Test signal functions with normal, valid data"""
    print("\nTesting normal data processing...")

    from app import (
        find_key_gamma_levels,
        calculate_market_bias,
        calculate_signal_confidence,
        calculate_signal_strength,
        generate_simple_signal
    )
    
    # Create sample valid data
    gamma_data = [
        {
            "strike": 95,
            "gamma_exposure": 500000,
            "call_oi": 1000,
            "put_oi": 500,
            "call_volume": 200,
            "put_volume": 100
        },
        {
            "strike": 100,
            "gamma_exposure": -200000,
            "call_oi": 800,
            "put_oi": 1200,
            "call_volume": 150,
            "put_volume": 300
        },
        {
            "strike": 105,
            "gamma_exposure": 800000,
            "call_oi": 1500,
            "put_oi": 400,
            "call_volume": 400,
            "put_volume": 80
        }
    ]
    
    current_price = 102.0
    
    # Test all functions
    levels = find_key_gamma_levels(gamma_data, current_price)
    bias = calculate_market_bias(gamma_data, current_price, levels)
    confidence = calculate_signal_confidence(gamma_data, current_price, bias)
    strength = calculate_signal_strength(gamma_data, current_price)
    signal = generate_simple_signal(bias, confidence)

    # Verify results are reasonable
    assert isinstance(levels, dict), "Levels should be a dictionary"
    assert isinstance(bias, dict), "Bias should be a dictionary"
    assert isinstance(confidence, dict), "Confidence should be a dictionary"
    assert isinstance(strength, (int, float)), "Strength should be a number"
    assert isinstance(signal, dict), "Signal should be a dictionary"

    assert bias['direction'] in ['BULLISH', 'BEARISH', 'NEUTRAL'], "Bias direction should be valid"
    assert 0 <= bias['strength'] <= 100, "Bias strength should be 0-100"
    assert 0 <= confidence['score'] <= 100, "Confidence score should be 0-100"
    assert 0 <= strength <= 100, "Signal strength should be 0-100"
    assert signal['action'] in ['BUY', 'SELL', 'HOLD'], "Signal action should be valid"

    print("✅ Normal data processing works correctly")
    print(f"   Bias: {bias['direction']} ({bias['strength']}%)")
    print(f"   Confidence: {confidence['level']} ({confidence['score']}%)")
    print(f"   Signal Strength: {strength}%")
    print(f"   Signal Action: {signal['action']} - {signal['reason']}")

def test_zero_division_protection():
    """Test protection against zero division errors"""
    print("\nTesting zero division protection...")

    from app import find_key_gamma_levels

    # Create data that could cause zero division in interpolation
    gamma_data = [
        {"strike": 100, "gamma_exposure": 1000},
        {"strike": 101, "gamma_exposure": 1000},  # Same value - could cause zero division
    ]

    current_price = 100.5

    # This should not crash
    levels = find_key_gamma_levels(gamma_data, current_price)
    print("✅ Zero division protection works correctly")

def test_simple_signal_generation():
    """Test the new simple BUY/SELL signal generation"""
    print("\nTesting simple signal generation...")

    from app import generate_simple_signal

    # Test strong bullish signal
    strong_bullish_bias = {
        'direction': 'BULLISH',
        'strength': 75,
        'factors': ['factor1', 'factor2']
    }
    high_confidence = {
        'level': 'HIGH',
        'score': 80
    }

    signal = generate_simple_signal(strong_bullish_bias, high_confidence)
    assert signal['action'] == 'BUY', "Strong bullish bias should generate BUY signal"

    # Test strong bearish signal
    strong_bearish_bias = {
        'direction': 'BEARISH',
        'strength': 70,
        'factors': ['factor1']
    }

    signal = generate_simple_signal(strong_bearish_bias, high_confidence)
    assert signal['action'] == 'SELL', "Strong bearish bias should generate SELL signal"

    # Test weak signal
    weak_bias = {
        'direction': 'NEUTRAL',
        'strength': 20
    }
    low_confidence = {
        'level': 'LOW',
        'score': 25
    }

    signal = generate_simple_signal(weak_bias, low_confidence)
    assert signal['action'] == 'HOLD', "Weak signal should generate HOLD"

    # Test with invalid data
    signal = generate_simple_signal(None, None)
    assert signal['action'] == 'HOLD', "Invalid data should generate HOLD"

    print("✅ Simple signal generation works correctly")

def run_all_tests():
    """Run all signal system tests"""
    print("🧪 Running Signal System Tests\n")
    
    try:
        test_empty_data_handling()
        test_invalid_data_handling()
        test_normal_data_processing()
        test_zero_division_protection()
        test_simple_signal_generation()

        print("\n🎉 All tests passed! Signal system is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
