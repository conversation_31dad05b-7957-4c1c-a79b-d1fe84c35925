#!/usr/bin/env python3
"""
Dupire Local Volatility Model Implementation
This module provides Dupire local volatility model for option pricing and Greeks.
"""

import numpy as np
import math
from scipy.stats import norm
from scipy.interpolate import RectBivariateSpline, interp2d
from scipy.optimize import minimize_scalar, brentq
import warnings


class DupireModel:
    """
    Dupire Local Volatility Model implementation.
    
    The Dupire model uses local volatility σ(S,t) that depends on both
    the underlying price and time. The local volatility is calibrated
    from market implied volatilities using the Dupire formula:
    
    σ_local²(K,T) = (∂C/∂T + rK∂C/∂K) / (½K²∂²C/∂K²)
    
    Where C(K,T) is the call option price as a function of strike and time.
    """
    
    def __init__(self, spot_price=100.0, risk_free_rate=0.05, dividend_yield=0.0):
        """
        Initialize Dupire model.
        
        Args:
            spot_price (float): Current spot price
            risk_free_rate (float): Risk-free rate
            dividend_yield (float): Dividend yield
        """
        self.S0 = spot_price
        self.r = risk_free_rate
        self.q = dividend_yield
        
        # Volatility surface data
        self.strikes = None
        self.times = None
        self.implied_vols = None
        self.local_vol_surface = None
        self.vol_interpolator = None
        
        # Default parameters for when no surface is available
        self.default_vol = 0.2
        self.vol_of_vol = 0.3
        self.mean_reversion = 1.0
        
    def calibrate_from_market_data(self, strikes, times, implied_vols, spot_price=None):
        """
        Calibrate the local volatility surface from market implied volatilities.
        
        Args:
            strikes (array): Array of strike prices
            times (array): Array of times to expiration
            implied_vols (2D array): Implied volatilities [time, strike]
            spot_price (float): Current spot price (optional)
        """
        if spot_price is not None:
            self.S0 = spot_price
            
        self.strikes = np.array(strikes)
        self.times = np.array(times)
        self.implied_vols = np.array(implied_vols)
        
        # Ensure we have valid data
        if len(self.strikes) < 3 or len(self.times) < 2:
            warnings.warn("Insufficient data for Dupire calibration, using simplified model")
            self._create_simple_surface()
            return
        
        try:
            # Calculate local volatility surface using Dupire formula
            self._calculate_local_volatility_surface()
            
            # Create interpolator for local volatility
            self._create_volatility_interpolator()
            
            print(f"✅ Dupire model calibrated with {len(self.strikes)} strikes and {len(self.times)} times")
            
        except Exception as e:
            warnings.warn(f"Dupire calibration failed: {e}, using simplified model")
            self._create_simple_surface()
    
    def _create_simple_surface(self):
        """Create a simple volatility surface when calibration fails."""
        # Create a simple grid
        self.strikes = np.linspace(self.S0 * 0.7, self.S0 * 1.3, 10)
        self.times = np.array([0.1, 0.25, 0.5, 1.0, 2.0])
        
        # Create a simple volatility surface with smile
        K_grid, T_grid = np.meshgrid(self.strikes, self.times)
        moneyness = K_grid / self.S0
        
        # Simple volatility smile: higher vol for OTM options
        base_vol = self.default_vol
        smile_factor = 0.1 * (moneyness - 1.0)**2
        time_factor = 0.05 * np.sqrt(T_grid)
        
        self.implied_vols = base_vol + smile_factor + time_factor
        self.local_vol_surface = self.implied_vols.copy()
        
        # Create interpolator
        self._create_volatility_interpolator()
    
    def _calculate_local_volatility_surface(self):
        """Calculate local volatility using the Dupire formula."""
        # This is a simplified implementation of the Dupire formula
        # In practice, this requires careful numerical differentiation

        self.local_vol_surface = np.zeros_like(self.implied_vols)

        # Optimization 23: Use enumerate for both indices and values
        for i, T in enumerate(self.times):
            for j, K in enumerate(self.strikes):
                try:
                    # Calculate local volatility using simplified Dupire formula
                    local_vol = self._dupire_local_vol(K, T, i, j)
                    self.local_vol_surface[i, j] = max(local_vol, 0.01)  # Floor at 1%
                except:
                    # Fallback to implied volatility
                    self.local_vol_surface[i, j] = self.implied_vols[i, j]
    
    def _dupire_local_vol(self, K, T, i, j):
        """
        Calculate local volatility at a specific point using Dupire formula.
        This is a simplified implementation.
        """
        try:
            # Get implied volatility at this point
            iv = self.implied_vols[i, j]
            
            # Calculate d1 and d2
            d1 = (math.log(self.S0 / K) + (self.r - self.q + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
            d2 = d1 - iv * math.sqrt(T)
            
            # Calculate call price and Greeks
            call_price = self.S0 * math.exp(-self.q * T) * norm.cdf(d1) - K * math.exp(-self.r * T) * norm.cdf(d2)
            
            # Numerical derivatives (simplified)
            dC_dT = self._estimate_time_derivative(K, T, i, j)
            dC_dK = self._estimate_strike_derivative(K, T, i, j)
            d2C_dK2 = self._estimate_second_strike_derivative(K, T, i, j)
            
            # Dupire formula
            numerator = dC_dT + self.r * K * dC_dK
            denominator = 0.5 * K**2 * d2C_dK2
            
            if denominator > 1e-10:
                local_vol_squared = numerator / denominator
                return math.sqrt(max(local_vol_squared, 0.0001))
            else:
                return iv
                
        except Exception as e:
            return self.implied_vols[i, j] if i < len(self.implied_vols) and j < len(self.implied_vols[0]) else self.default_vol
    
    def _estimate_time_derivative(self, K, T, i, j):
        """Estimate ∂C/∂T using finite differences."""
        if i == 0:
            return 0.0  # No time derivative at first time point
        
        # Use backward difference
        T_prev = self.times[i-1]
        iv_curr = self.implied_vols[i, j]
        iv_prev = self.implied_vols[i-1, j]
        
        call_curr = self._black_scholes_price(self.S0, K, T, self.r, iv_curr, 'call')
        call_prev = self._black_scholes_price(self.S0, K, T_prev, self.r, iv_prev, 'call')
        
        return (call_curr - call_prev) / (T - T_prev)
    
    def _estimate_strike_derivative(self, K, T, i, j):
        """Estimate ∂C/∂K using finite differences."""
        if j == 0 or j == len(self.strikes) - 1:
            # Use one-sided difference at boundaries
            if j == 0:
                K_next = self.strikes[j+1]
                iv_curr = self.implied_vols[i, j]
                iv_next = self.implied_vols[i, j+1]
                
                call_curr = self._black_scholes_price(self.S0, K, T, self.r, iv_curr, 'call')
                call_next = self._black_scholes_price(self.S0, K_next, T, self.r, iv_next, 'call')
                
                return (call_next - call_curr) / (K_next - K)
            else:
                K_prev = self.strikes[j-1]
                iv_curr = self.implied_vols[i, j]
                iv_prev = self.implied_vols[i, j-1]
                
                call_curr = self._black_scholes_price(self.S0, K, T, self.r, iv_curr, 'call')
                call_prev = self._black_scholes_price(self.S0, K_prev, T, self.r, iv_prev, 'call')
                
                return (call_curr - call_prev) / (K - K_prev)
        else:
            # Use central difference
            K_prev = self.strikes[j-1]
            K_next = self.strikes[j+1]
            iv_prev = self.implied_vols[i, j-1]
            iv_next = self.implied_vols[i, j+1]
            
            call_prev = self._black_scholes_price(self.S0, K_prev, T, self.r, iv_prev, 'call')
            call_next = self._black_scholes_price(self.S0, K_next, T, self.r, iv_next, 'call')
            
            return (call_next - call_prev) / (K_next - K_prev)
    
    def _estimate_second_strike_derivative(self, K, T, i, j):
        """Estimate ∂²C/∂K² using finite differences."""
        if j == 0 or j == len(self.strikes) - 1:
            # Use simplified estimate at boundaries
            iv = self.implied_vols[i, j]
            d1 = (math.log(self.S0 / K) + (self.r - self.q + 0.5 * iv**2) * T) / (iv * math.sqrt(T))
            return math.exp(-self.r * T) * norm.pdf(d1) / (K * iv * math.sqrt(T))
        else:
            # Use central difference
            K_prev = self.strikes[j-1]
            K_next = self.strikes[j+1]
            iv_prev = self.implied_vols[i, j-1]
            iv_curr = self.implied_vols[i, j]
            iv_next = self.implied_vols[i, j+1]
            
            call_prev = self._black_scholes_price(self.S0, K_prev, T, self.r, iv_prev, 'call')
            call_curr = self._black_scholes_price(self.S0, K, T, self.r, iv_curr, 'call')
            call_next = self._black_scholes_price(self.S0, K_next, T, self.r, iv_next, 'call')
            
            h1 = K - K_prev
            h2 = K_next - K
            
            return 2 * ((call_next - call_curr) / h2 - (call_curr - call_prev) / h1) / (h1 + h2)
    
    def _create_volatility_interpolator(self):
        """Create interpolator for local volatility surface."""
        try:
            # Create interpolator using RectBivariateSpline
            self.vol_interpolator = RectBivariateSpline(
                self.times, self.strikes, self.local_vol_surface,
                kx=min(3, len(self.times)-1), ky=min(3, len(self.strikes)-1)
            )
        except:
            # Fallback to simpler interpolation
            try:
                self.vol_interpolator = interp2d(
                    self.strikes, self.times, self.local_vol_surface,
                    kind='linear', bounds_error=False, fill_value=self.default_vol
                )
            except:
                self.vol_interpolator = None
    
    def get_local_volatility(self, S, T):
        """
        Get local volatility at given spot price and time.
        
        Args:
            S (float): Spot price
            T (float): Time to expiration
            
        Returns:
            float: Local volatility
        """
        if self.vol_interpolator is None:
            return self.default_vol
        
        try:
            if hasattr(self.vol_interpolator, '__call__'):
                # interp2d case
                vol = float(self.vol_interpolator(S, T))
            else:
                # RectBivariateSpline case
                vol = float(self.vol_interpolator(T, S))
            
            return max(vol, 0.01)  # Floor at 1%
        except:
            return self.default_vol
    
    def _black_scholes_price(self, S, K, T, r, sigma, option_type):
        """Calculate Black-Scholes price with given volatility."""
        if T <= 0 or sigma <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1 = (math.log(S / K) + (r - self.q + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        if option_type.lower() == 'call':
            price = S * math.exp(-self.q * T) * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * math.exp(-self.q * T) * norm.cdf(-d1)
        
        return max(price, 0)
    
    def option_price(self, S, K, T, r, option_type='call'):
        """
        Calculate option price using Dupire model.
        For simplicity, this uses the local volatility at current spot and time.
        A full implementation would use PDE methods or Monte Carlo.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            option_type (str): 'call' or 'put'
            
        Returns:
            float: Option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        # Get local volatility at current spot and time
        local_vol = self.get_local_volatility(S, T)
        
        # Use Black-Scholes with local volatility as approximation
        return self._black_scholes_price(S, K, T, r, local_vol, option_type)

    def delta(self, S, K, T, r, option_type='call'):
        """Calculate delta using Dupire model."""
        if T <= 0:
            if option_type.lower() == 'call':
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0

        local_vol = self.get_local_volatility(S, T)

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))

        if option_type.lower() == 'call':
            return math.exp(-self.q * T) * norm.cdf(d1)
        else:
            return math.exp(-self.q * T) * (norm.cdf(d1) - 1)

    def gamma(self, S, K, T, r, option_type='call'):
        """Calculate gamma using Dupire model."""
        if T <= 0 or S <= 0:
            return 0.0

        local_vol = self.get_local_volatility(S, T)

        if local_vol <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))

        return math.exp(-self.q * T) * norm.pdf(d1) / (S * local_vol * math.sqrt(T))

    def vega(self, S, K, T, r, option_type='call'):
        """Calculate vega using Dupire model."""
        if T <= 0 or S <= 0:
            return 0.0

        local_vol = self.get_local_volatility(S, T)

        if local_vol <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))

        return S * math.exp(-self.q * T) * norm.pdf(d1) * math.sqrt(T) / 100

    def theta(self, S, K, T, r, option_type='call'):
        """Calculate theta using Dupire model."""
        if T <= 0:
            return 0.0

        local_vol = self.get_local_volatility(S, T)

        if local_vol <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))
        d2 = d1 - local_vol * math.sqrt(T)

        if option_type.lower() == 'call':
            theta = (-S * math.exp(-self.q * T) * norm.pdf(d1) * local_vol / (2 * math.sqrt(T))
                    - r * K * math.exp(-r * T) * norm.cdf(d2)
                    + self.q * S * math.exp(-self.q * T) * norm.cdf(d1))
        else:
            theta = (-S * math.exp(-self.q * T) * norm.pdf(d1) * local_vol / (2 * math.sqrt(T))
                    + r * K * math.exp(-r * T) * norm.cdf(-d2)
                    - self.q * S * math.exp(-self.q * T) * norm.cdf(-d1))

        return theta / 365

    def vanna(self, S, K, T, r, option_type='call'):
        """Calculate vanna using Dupire model."""
        if T <= 0 or S <= 0:
            return 0.0

        local_vol = self.get_local_volatility(S, T)

        if local_vol <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))
        d2 = d1 - local_vol * math.sqrt(T)

        return -math.exp(-self.q * T) * norm.pdf(d1) * d2 / local_vol / 100

    def charm(self, S, K, T, r, option_type='call'):
        """Calculate charm using Dupire model."""
        if T <= 0 or S <= 0:
            return 0.0

        local_vol = self.get_local_volatility(S, T)

        if local_vol <= 0:
            return 0.0

        d1 = (math.log(S / K) + (r - self.q + 0.5 * local_vol**2) * T) / (local_vol * math.sqrt(T))
        d2 = d1 - local_vol * math.sqrt(T)

        if option_type.lower() == 'call':
            charm = (self.q * math.exp(-self.q * T) * norm.cdf(d1)
                    - math.exp(-self.q * T) * norm.pdf(d1) * (2 * (r - self.q) * T - d2 * local_vol * math.sqrt(T)) / (2 * T * local_vol * math.sqrt(T)))
        else:
            charm = (-self.q * math.exp(-self.q * T) * norm.cdf(-d1)
                    - math.exp(-self.q * T) * norm.pdf(d1) * (2 * (r - self.q) * T - d2 * local_vol * math.sqrt(T)) / (2 * T * local_vol * math.sqrt(T)))

        return charm / 365


# Default Dupire model instance
default_dupire = DupireModel()


def dupire_option_price(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire option pricing."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.option_price(S, K, T, r, option_type)


def dupire_delta(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire delta calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.delta(S, K, T, r, option_type)


def dupire_gamma(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire gamma calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.gamma(S, K, T, r, option_type)


def dupire_vega(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire vega calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.vega(S, K, T, r, option_type)


def dupire_theta(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire theta calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.theta(S, K, T, r, option_type)


def dupire_vanna(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire vanna calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.vanna(S, K, T, r, option_type)


def dupire_charm(flag, S, K, T, r, dupire_model=None):
    """Convenience function for Dupire charm calculation."""
    model = dupire_model or default_dupire
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return model.charm(S, K, T, r, option_type)
