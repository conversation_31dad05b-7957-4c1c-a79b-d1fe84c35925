#!/usr/bin/env python3
"""
Test script to verify Local Stochastic Volatility chart rendering
"""

import requests
import json
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_TICKER = "SPY"
TEST_EXPIRY = "2024-12-20"  # Adjust as needed

def test_local_stochastic_volatility_api():
    """Test the Local Stochastic Volatility API endpoint"""
    print("🔍 Testing Local Stochastic Volatility API...")
    
    url = f"{BASE_URL}/api/local-stochastic-volatility-surface/{TEST_TICKER}/{TEST_EXPIRY}"
    
    params = {
        'l_function_type': 'polynomial',
        'grid_resolution': 30,
        'time_grid_points': 10,
        'moneyness_grid_points': 15,
        'include_calibration': 'true'
    }
    
    try:
        print(f"   Making request to: {url}")
        print(f"   Parameters: {params}")
        
        response = requests.get(url, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("   ✅ API call successful")
                
                # Check surface data structure
                surface_data = data.get('surface_data', {})
                
                required_fields = [
                    'log_moneyness_grid',
                    'time_to_maturity_grid', 
                    'local_stochastic_vol_surface',
                    'l_function_values'
                ]
                
                missing_fields = [field for field in required_fields if field not in surface_data]
                
                if not missing_fields:
                    print("   ✅ All required surface data fields present")
                    
                    # Check data dimensions
                    log_m_grid = surface_data['log_moneyness_grid']
                    time_grid = surface_data['time_to_maturity_grid']
                    vol_surface = surface_data['local_stochastic_vol_surface']
                    l_function = surface_data['l_function_values']
                    
                    print(f"   📊 Log Moneyness Grid: {len(log_m_grid)} points")
                    print(f"   📊 Time Grid: {len(time_grid)} points")
                    print(f"   📊 Vol Surface: {len(vol_surface)} x {len(vol_surface[0]) if vol_surface else 0}")
                    print(f"   📊 L Function: {len(l_function)} x {len(l_function[0]) if l_function else 0}")
                    
                    # Check calibration metrics
                    calibration = surface_data.get('calibration_metrics', {})
                    if calibration:
                        print(f"   📈 RMSE: {calibration.get('rmse', 'N/A')}")
                        print(f"   📈 Quality Score: {calibration.get('quality_score', 'N/A')}")
                        print(f"   📈 Correlation: {calibration.get('correlation', 'N/A')}")
                    
                    return True, data
                else:
                    print(f"   ❌ Missing surface data fields: {missing_fields}")
                    return False, data
            else:
                print(f"   ❌ API returned error: {data.get('error', 'Unknown error')}")
                return False, data
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Response text: {response.text[:200]}...")
            return False, None
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection Error: Server not running")
        return False, None
    except Exception as e:
        print(f"   ❌ Request Error: {e}")
        return False, None

def test_enhanced_skew_analysis_api():
    """Test the Enhanced Skew Analysis API endpoint"""
    print("🔍 Testing Enhanced Skew Analysis API...")
    
    url = f"{BASE_URL}/api/advanced-volatility-analysis/{TEST_TICKER}/{TEST_EXPIRY}"
    
    params = {
        'analysis_type': 'enhanced_skew_analysis',
        'include_stochastic': 'true',
        'skew_model_type': 'local_stochastic',
        'show_log_moneyness': 'true',
        'show_time_evolution': 'true'
    }
    
    try:
        print(f"   Making request to: {url}")
        print(f"   Parameters: {params}")
        
        response = requests.get(url, params=params, timeout=45)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("   ✅ API call successful")
                
                # Check enhanced skew analysis structure
                enhanced_analysis = data.get('enhanced_skew_analysis', {})
                
                expected_components = [
                    'traditional_skew',
                    'log_moneyness_analysis',
                    'time_dependent_skew',
                    'l_function_skew',
                    'advanced_metrics'
                ]
                
                available_components = [comp for comp in expected_components if comp in enhanced_analysis]
                
                print(f"   📊 Available components: {len(available_components)}/{len(expected_components)}")
                print(f"   Components: {', '.join(available_components)}")
                
                # Check specific data
                traditional = enhanced_analysis.get('traditional_skew', {})
                if traditional:
                    print(f"   📈 ATM IV: {traditional.get('atm_iv', 'N/A')}")
                    print(f"   📈 Total Skew: {traditional.get('total_skew', 'N/A')}")
                
                log_analysis = enhanced_analysis.get('log_moneyness_analysis', {})
                if log_analysis:
                    bucket_count = len(log_analysis.get('bucket_analysis', []))
                    print(f"   📈 Log Moneyness Buckets: {bucket_count}")
                    print(f"   📈 Skew Slope: {log_analysis.get('skew_slope', 'N/A')}")
                
                return True, data
            else:
                print(f"   ❌ API returned error: {data.get('error', 'Unknown error')}")
                return False, data
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Request Error: {e}")
        return False, None

def test_chart_data_format():
    """Test that the data format is suitable for chart rendering"""
    print("🔍 Testing Chart Data Format...")
    
    # Test Local Stochastic Volatility Surface
    success, lsv_data = test_local_stochastic_volatility_api()
    
    if success and lsv_data:
        surface_data = lsv_data.get('surface_data', {})
        
        # Validate data for Plotly 3D surface
        log_m_grid = surface_data.get('log_moneyness_grid', [])
        time_grid = surface_data.get('time_to_maturity_grid', [])
        vol_surface = surface_data.get('local_stochastic_vol_surface', [])
        
        if log_m_grid and time_grid and vol_surface:
            # Check dimensions match
            expected_rows = len(time_grid)
            expected_cols = len(log_m_grid)
            actual_rows = len(vol_surface)
            actual_cols = len(vol_surface[0]) if vol_surface else 0
            
            if actual_rows == expected_rows and actual_cols == expected_cols:
                print("   ✅ LSV Surface data dimensions correct for Plotly")
                print(f"   Grid: {actual_rows} x {actual_cols}")
                
                # Check for valid numeric data
                try:
                    min_vol = min(min(row) for row in vol_surface)
                    max_vol = max(max(row) for row in vol_surface)
                    print(f"   📊 Volatility range: {min_vol:.4f} to {max_vol:.4f}")
                    
                    if 0 < min_vol < max_vol < 5:  # Reasonable volatility range
                        print("   ✅ Volatility values in reasonable range")
                    else:
                        print("   ⚠️ Volatility values may be outside normal range")
                        
                except Exception as e:
                    print(f"   ❌ Error analyzing volatility data: {e}")
                    return False
                    
            else:
                print(f"   ❌ Dimension mismatch: expected {expected_rows}x{expected_cols}, got {actual_rows}x{actual_cols}")
                return False
        else:
            print("   ❌ Missing required surface data arrays")
            return False
    
    # Test Enhanced Skew Analysis
    success, skew_data = test_enhanced_skew_analysis_api()
    
    if success and skew_data:
        enhanced_analysis = skew_data.get('enhanced_skew_analysis', {})
        
        # Check log moneyness bucket data for plotting
        log_analysis = enhanced_analysis.get('log_moneyness_analysis', {})
        bucket_analysis = log_analysis.get('bucket_analysis', [])
        
        if bucket_analysis:
            print("   ✅ Enhanced Skew bucket data available for plotting")
            print(f"   📊 Buckets: {len(bucket_analysis)}")
            
            # Check data structure
            if all('log_moneyness_center' in bucket and 'avg_iv' in bucket for bucket in bucket_analysis):
                print("   ✅ Bucket data has required fields for scatter plot")
            else:
                print("   ❌ Bucket data missing required fields")
                return False
        else:
            print("   ⚠️ No bucket analysis data for enhanced skew plotting")
    
    return True

def generate_sample_chart_html():
    """Generate sample HTML to test chart rendering"""
    print("🔍 Generating Sample Chart HTML...")
    
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>LSV Chart Test</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { background-color: #1a1a1a; color: white; font-family: Arial, sans-serif; }
        .chart-container { width: 100%; height: 600px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🌊 Local Stochastic Volatility 3D Surface Test</h1>
    <div id="lsv-chart" class="chart-container"></div>
    
    <h1>🔬 Enhanced Skew Analysis Test</h1>
    <div id="skew-chart" class="chart-container"></div>
    
    <script>
        // Test LSV Surface
        const logMoneynessGrid = [-0.3, -0.2, -0.1, 0, 0.1, 0.2, 0.3];
        const timeGrid = [0.02, 0.08, 0.25, 0.5, 1.0];
        const volSurface = [
            [0.25, 0.24, 0.23, 0.22, 0.23, 0.24, 0.25],
            [0.24, 0.23, 0.22, 0.21, 0.22, 0.23, 0.24],
            [0.23, 0.22, 0.21, 0.20, 0.21, 0.22, 0.23],
            [0.22, 0.21, 0.20, 0.19, 0.20, 0.21, 0.22],
            [0.21, 0.20, 0.19, 0.18, 0.19, 0.20, 0.21]
        ];
        
        const lsvTrace = {
            x: logMoneynessGrid,
            y: timeGrid,
            z: volSurface,
            type: 'surface',
            colorscale: 'Viridis'
        };
        
        const lsvLayout = {
            title: '🌊 Local Stochastic Volatility 3D Surface',
            scene: {
                xaxis: { title: 'Log Moneyness' },
                yaxis: { title: 'Time to Maturity' },
                zaxis: { title: 'Local Stochastic Volatility' }
            },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: 'white' }
        };
        
        Plotly.newPlot('lsv-chart', [lsvTrace], lsvLayout);
        
        // Test Enhanced Skew
        const skewTrace = {
            x: [-0.2, -0.1, 0, 0.1, 0.2],
            y: [0.25, 0.22, 0.20, 0.22, 0.24],
            type: 'scatter',
            mode: 'lines+markers',
            name: 'Log Moneyness IV'
        };
        
        const skewLayout = {
            title: '🔬 Enhanced Skew Analysis',
            xaxis: { title: 'Log Moneyness' },
            yaxis: { title: 'Implied Volatility' },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: 'white' }
        };
        
        Plotly.newPlot('skew-chart', [skewTrace], skewLayout);
        
        console.log('✅ Sample charts rendered successfully');
    </script>
</body>
</html>
    """
    
    with open('test_lsv_charts.html', 'w') as f:
        f.write(html_content)
    
    print("   ✅ Sample chart HTML generated: test_lsv_charts.html")
    print("   📝 Open this file in a browser to test chart rendering")
    
    return True

def main():
    """Run all chart rendering tests"""
    print("🧪 Testing Local Stochastic Volatility Chart Rendering")
    print("=" * 70)
    
    tests = [
        ("Local Stochastic Volatility API", lambda: test_local_stochastic_volatility_api()[0]),
        ("Enhanced Skew Analysis API", lambda: test_enhanced_skew_analysis_api()[0]),
        ("Chart Data Format", test_chart_data_format),
        ("Sample Chart HTML", generate_sample_chart_html)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All chart rendering tests passed!")
        print("✅ Local Stochastic Volatility API working")
        print("✅ Enhanced Skew Analysis API working") 
        print("✅ Data format suitable for chart rendering")
        print("✅ Sample charts generated for testing")
    else:
        print("⚠️ Some chart rendering issues found")
        if passed >= len(results) - 1:
            print("ℹ️ Most tests passed - check server status if API tests failed")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
