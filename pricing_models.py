#!/usr/bin/env python3
"""
Pricing Models Manager
This module provides a unified interface for different option pricing models.
"""

import os
from typing import Dict, Any, Optional
from py_vollib.black_scholes.greeks.analytical import delta as bs_delta
from py_vollib.black_scholes.greeks.analytical import gamma as bs_gamma
from py_vollib.black_scholes.greeks.analytical import vega as bs_vega
from py_vollib.black_scholes.greeks.analytical import theta as bs_theta
from sabr_model import SABRModel, sabr_delta, sabr_gamma, sabr_vega, sabr_theta, sabr_vanna, sabr_charm
import math
from scipy.stats import norm


class PricingModelManager:
    """
    Manager class for handling different pricing models.
    Provides a unified interface for Black-Scholes and SABR models.
    """
    
    def __init__(self):
        """Initialize the pricing model manager."""
        self.current_model = 'black_scholes'  # Default model
        self.sabr_params = {
            'alpha': 0.2,
            'beta': 0.5,
            'rho': -0.3,
            'nu': 0.4
        }
        self.sabr_instance = None
        self._update_sabr_instance()
    
    def set_model(self, model_name: str):
        """
        Set the current pricing model.
        
        Args:
            model_name (str): 'black_scholes' or 'sabr'
        """
        if model_name not in ['black_scholes', 'sabr']:
            raise ValueError(f"Unknown model: {model_name}. Must be 'black_scholes' or 'sabr'")
        
        self.current_model = model_name
        print(f"Pricing model set to: {model_name}")
    
    def get_current_model(self) -> str:
        """Get the current pricing model name."""
        return self.current_model
    
    def set_sabr_params(self, alpha: float = None, beta: float = None, 
                       rho: float = None, nu: float = None):
        """
        Set SABR model parameters.
        
        Args:
            alpha (float): Alpha parameter
            beta (float): Beta parameter
            rho (float): Rho parameter
            nu (float): Nu parameter
        """
        if alpha is not None:
            self.sabr_params['alpha'] = alpha
        if beta is not None:
            self.sabr_params['beta'] = beta
        if rho is not None:
            self.sabr_params['rho'] = rho
        if nu is not None:
            self.sabr_params['nu'] = nu
        
        self._update_sabr_instance()
        print(f"SABR parameters updated: {self.sabr_params}")
    
    def get_sabr_params(self) -> Dict[str, float]:
        """Get current SABR parameters."""
        return self.sabr_params.copy()
    
    def _update_sabr_instance(self):
        """Update the SABR model instance with current parameters."""
        try:
            self.sabr_instance = SABRModel(
                alpha=self.sabr_params['alpha'],
                beta=self.sabr_params['beta'],
                rho=self.sabr_params['rho'],
                nu=self.sabr_params['nu']
            )
        except Exception as e:
            print(f"Error creating SABR instance: {e}")
            # Fallback to default parameters
            self.sabr_instance = SABRModel()
    
    def calculate_delta(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """
        Calculate delta using the current pricing model.
        
        Args:
            flag (str): 'c' for call, 'p' for put
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            sigma (float): Volatility (used for Black-Scholes, ignored for SABR)
            
        Returns:
            float: Delta value
        """
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2  # Default volatility
            return bs_delta(flag, S, K, T, r, sigma)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.delta(S, K, T, r, option_type)
    
    def calculate_gamma(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate gamma using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_gamma(flag, S, K, T, r, sigma)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.gamma(S, K, T, r, option_type)
    
    def calculate_vega(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate vega using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_vega(flag, S, K, T, r, sigma)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.vega(S, K, T, r, option_type)
    
    def calculate_theta(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate theta using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_theta(flag, S, K, T, r, sigma)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.theta(S, K, T, r, option_type)
    
    def calculate_vanna(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate vanna using the current pricing model."""
        if self.current_model == 'black_scholes':
            # Use existing Black-Scholes vanna calculation
            return self._calculate_bs_vanna(flag, S, K, T, r, sigma or 0.2)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.vanna(S, K, T, r, option_type)
    
    def calculate_charm(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate charm using the current pricing model."""
        if self.current_model == 'black_scholes':
            # Use existing Black-Scholes charm calculation
            return self._calculate_bs_charm(flag, S, K, T, r, sigma or 0.2)
        else:  # SABR
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.charm(S, K, T, r, option_type)
    
    def _calculate_bs_vanna(self, flag: str, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes vanna."""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            # Vanna = ∂²V/∂S∂σ = -e^(-rT) * φ(d1) * d2 / σ
            vanna = -norm.pdf(d1) * d2 / sigma
            
            return vanna / 100  # Convert to percentage point change
        except Exception as e:
            print(f"Error calculating BS vanna: {e}")
            return 0
    
    def _calculate_bs_charm(self, flag: str, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes charm."""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            if flag.lower() == 'c':  # Call option
                charm = -norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))
            else:  # Put option
                charm = -norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))
            
            return charm / 365  # Convert to daily charm
        except Exception as e:
            print(f"Error calculating BS charm: {e}")
            return 0
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model and its parameters.
        
        Returns:
            dict: Model information
        """
        info = {
            'current_model': self.current_model,
            'available_models': ['black_scholes', 'sabr']
        }
        
        if self.current_model == 'sabr':
            info['sabr_parameters'] = self.sabr_params
        
        return info


# Global pricing model manager instance
pricing_manager = PricingModelManager()


def get_pricing_manager() -> PricingModelManager:
    """Get the global pricing model manager instance."""
    return pricing_manager


def set_pricing_model(model_name: str):
    """Set the global pricing model."""
    pricing_manager.set_model(model_name)


def get_current_pricing_model() -> str:
    """Get the current pricing model name."""
    return pricing_manager.get_current_model()


def set_sabr_parameters(alpha: float = None, beta: float = None, 
                       rho: float = None, nu: float = None):
    """Set SABR model parameters."""
    pricing_manager.set_sabr_params(alpha, beta, rho, nu)


def get_sabr_parameters() -> Dict[str, float]:
    """Get current SABR parameters."""
    return pricing_manager.get_sabr_params()
