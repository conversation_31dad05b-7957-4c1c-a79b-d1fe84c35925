#!/usr/bin/env python3
"""
Pricing Models Manager
This module provides a unified interface for different option pricing models.
"""

import os
from typing import Dict, Any, Optional
from py_vollib.black_scholes.greeks.analytical import delta as bs_delta
from py_vollib.black_scholes.greeks.analytical import gamma as bs_gamma
from py_vollib.black_scholes.greeks.analytical import vega as bs_vega
from py_vollib.black_scholes.greeks.analytical import theta as bs_theta
from sabr_model import SABRModel, sabr_delta, sabr_gamma, sabr_vega, sabr_theta, sabr_vanna, sabr_charm
from dupire_model import DupireModel, dupire_delta, dupire_gamma, dupire_vega, dupire_theta, dupire_vanna, dupire_charm
from heston_model import HestonModel, heston_delta, heston_gamma, heston_vega, heston_theta, heston_vanna, heston_charm
from dupire_heston_model import DupireHestonModel, dupire_heston_delta, dupire_heston_gamma, dupire_heston_vega, dupire_heston_theta, dupire_heston_vanna, dupire_heston_charm
import math
from scipy.stats import norm


class PricingModelManager:
    """
    Manager class for handling different pricing models.
    Provides a unified interface for Black-Scholes and SABR models.
    """
    
    def __init__(self):
        """Initialize the pricing model manager."""
        self.current_model = 'black_scholes'  # Default model

        # SABR parameters
        self.sabr_params = {
            'alpha': 0.2,
            'beta': 0.5,
            'rho': -0.3,
            'nu': 0.4
        }

        # Heston parameters
        self.heston_params = {
            'kappa': 2.0,
            'theta': 0.04,
            'sigma': 0.3,
            'rho': -0.7,
            'v0': 0.04
        }

        # Dupire parameters
        self.dupire_params = {
            'spot_price': 100.0,
            'risk_free_rate': 0.05,
            'dividend_yield': 0.0
        }

        # Dupire-Heston hybrid parameters
        self.dupire_heston_params = {
            'dupire_weight': 0.5,
            'correlation_adjustment': 0.1
        }

        # Model instances
        self.sabr_instance = None
        self.heston_instance = None
        self.dupire_instance = None
        self.dupire_heston_instance = None

        self._update_model_instances()
    
    def set_model(self, model_name: str):
        """
        Set the current pricing model.

        Args:
            model_name (str): 'black_scholes', 'sabr', 'dupire', 'heston', or 'dupire_heston'
        """
        valid_models = ['black_scholes', 'sabr', 'dupire', 'heston', 'dupire_heston']
        if model_name not in valid_models:
            raise ValueError(f"Unknown model: {model_name}. Must be one of {valid_models}")

        self.current_model = model_name
        print(f"Pricing model set to: {model_name}")
    
    def get_current_model(self) -> str:
        """Get the current pricing model name."""
        return self.current_model
    
    def set_sabr_params(self, alpha: float = None, beta: float = None,
                       rho: float = None, nu: float = None):
        """Set SABR model parameters."""
        if alpha is not None:
            self.sabr_params['alpha'] = alpha
        if beta is not None:
            self.sabr_params['beta'] = beta
        if rho is not None:
            self.sabr_params['rho'] = rho
        if nu is not None:
            self.sabr_params['nu'] = nu

        self._update_model_instances()
        print(f"SABR parameters updated: {self.sabr_params}")

    def set_heston_params(self, kappa: float = None, theta: float = None,
                         sigma: float = None, rho: float = None, v0: float = None):
        """Set Heston model parameters."""
        if kappa is not None:
            self.heston_params['kappa'] = kappa
        if theta is not None:
            self.heston_params['theta'] = theta
        if sigma is not None:
            self.heston_params['sigma'] = sigma
        if rho is not None:
            self.heston_params['rho'] = rho
        if v0 is not None:
            self.heston_params['v0'] = v0

        self._update_model_instances()
        print(f"Heston parameters updated: {self.heston_params}")

    def set_dupire_params(self, spot_price: float = None, risk_free_rate: float = None,
                         dividend_yield: float = None):
        """Set Dupire model parameters."""
        if spot_price is not None:
            self.dupire_params['spot_price'] = spot_price
        if risk_free_rate is not None:
            self.dupire_params['risk_free_rate'] = risk_free_rate
        if dividend_yield is not None:
            self.dupire_params['dividend_yield'] = dividend_yield

        self._update_model_instances()
        print(f"Dupire parameters updated: {self.dupire_params}")

    def set_dupire_heston_params(self, dupire_weight: float = None,
                                correlation_adjustment: float = None):
        """Set Dupire-Heston hybrid model parameters."""
        if dupire_weight is not None:
            self.dupire_heston_params['dupire_weight'] = dupire_weight
        if correlation_adjustment is not None:
            self.dupire_heston_params['correlation_adjustment'] = correlation_adjustment

        self._update_model_instances()
        print(f"Dupire-Heston parameters updated: {self.dupire_heston_params}")

    def get_sabr_params(self) -> Dict[str, float]:
        """Get current SABR parameters."""
        return self.sabr_params.copy()

    def get_heston_params(self) -> Dict[str, float]:
        """Get current Heston parameters."""
        return self.heston_params.copy()

    def get_dupire_params(self) -> Dict[str, float]:
        """Get current Dupire parameters."""
        return self.dupire_params.copy()

    def get_dupire_heston_params(self) -> Dict[str, float]:
        """Get current Dupire-Heston parameters."""
        return self.dupire_heston_params.copy()

    def _update_model_instances(self):
        """Update all model instances with current parameters."""
        try:
            # Update SABR instance
            self.sabr_instance = SABRModel(
                alpha=self.sabr_params['alpha'],
                beta=self.sabr_params['beta'],
                rho=self.sabr_params['rho'],
                nu=self.sabr_params['nu']
            )
        except Exception as e:
            print(f"Error creating SABR instance: {e}")
            self.sabr_instance = SABRModel()

        try:
            # Update Heston instance
            self.heston_instance = HestonModel(
                kappa=self.heston_params['kappa'],
                theta=self.heston_params['theta'],
                sigma=self.heston_params['sigma'],
                rho=self.heston_params['rho'],
                v0=self.heston_params['v0']
            )
        except Exception as e:
            print(f"Error creating Heston instance: {e}")
            self.heston_instance = HestonModel()

        try:
            # Update Dupire instance
            self.dupire_instance = DupireModel(
                spot_price=self.dupire_params['spot_price'],
                risk_free_rate=self.dupire_params['risk_free_rate'],
                dividend_yield=self.dupire_params['dividend_yield']
            )
        except Exception as e:
            print(f"Error creating Dupire instance: {e}")
            self.dupire_instance = DupireModel()

        try:
            # Update Dupire-Heston instance
            self.dupire_heston_instance = DupireHestonModel(
                dupire_weight=self.dupire_heston_params['dupire_weight'],
                heston_params=self.heston_params,
                dupire_params=self.dupire_params
            )
            self.dupire_heston_instance.correlation_adjustment = self.dupire_heston_params['correlation_adjustment']
        except Exception as e:
            print(f"Error creating Dupire-Heston instance: {e}")
            self.dupire_heston_instance = DupireHestonModel()
    
    def calculate_delta(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate delta using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2  # Default volatility
            return bs_delta(flag, S, K, T, r, sigma)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.delta(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.delta(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.delta(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.delta(S, K, T, r, option_type)
        else:
            # Fallback to Black-Scholes
            if sigma is None:
                sigma = 0.2
            return bs_delta(flag, S, K, T, r, sigma)
    
    def calculate_gamma(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate gamma using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_gamma(flag, S, K, T, r, sigma)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.gamma(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.gamma(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.gamma(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.gamma(S, K, T, r, option_type)
        else:
            if sigma is None:
                sigma = 0.2
            return bs_gamma(flag, S, K, T, r, sigma)
    
    def calculate_vega(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate vega using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_vega(flag, S, K, T, r, sigma)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.vega(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.vega(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.vega(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.vega(S, K, T, r, option_type)
        else:
            if sigma is None:
                sigma = 0.2
            return bs_vega(flag, S, K, T, r, sigma)

    def calculate_theta(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate theta using the current pricing model."""
        if self.current_model == 'black_scholes':
            if sigma is None:
                sigma = 0.2
            return bs_theta(flag, S, K, T, r, sigma)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.theta(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.theta(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.theta(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.theta(S, K, T, r, option_type)
        else:
            if sigma is None:
                sigma = 0.2
            return bs_theta(flag, S, K, T, r, sigma)

    def calculate_vanna(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate vanna using the current pricing model."""
        if self.current_model == 'black_scholes':
            return self._calculate_bs_vanna(flag, S, K, T, r, sigma or 0.2)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.vanna(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.vanna(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.vanna(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.vanna(S, K, T, r, option_type)
        else:
            return self._calculate_bs_vanna(flag, S, K, T, r, sigma or 0.2)

    def calculate_charm(self, flag: str, S: float, K: float, T: float, r: float, sigma: float = None) -> float:
        """Calculate charm using the current pricing model."""
        if self.current_model == 'black_scholes':
            return self._calculate_bs_charm(flag, S, K, T, r, sigma or 0.2)
        elif self.current_model == 'sabr':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.sabr_instance.charm(S, K, T, r, option_type)
        elif self.current_model == 'dupire':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_instance.charm(S, K, T, r, option_type)
        elif self.current_model == 'heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.heston_instance.charm(S, K, T, r, option_type)
        elif self.current_model == 'dupire_heston':
            option_type = 'call' if flag.lower() == 'c' else 'put'
            return self.dupire_heston_instance.charm(S, K, T, r, option_type)
        else:
            return self._calculate_bs_charm(flag, S, K, T, r, sigma or 0.2)
    
    def _calculate_bs_vanna(self, flag: str, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes vanna."""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            # Vanna = ∂²V/∂S∂σ = -e^(-rT) * φ(d1) * d2 / σ
            vanna = -norm.pdf(d1) * d2 / sigma
            
            return vanna / 100  # Convert to percentage point change
        except Exception as e:
            print(f"Error calculating BS vanna: {e}")
            return 0
    
    def _calculate_bs_charm(self, flag: str, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes charm."""
        try:
            if T <= 0 or sigma <= 0:
                return 0
            
            d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
            d2 = d1 - sigma * math.sqrt(T)
            
            if flag.lower() == 'c':  # Call option
                charm = -norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))
            else:  # Put option
                charm = -norm.pdf(d1) * (2 * r * T - d2 * sigma * math.sqrt(T)) / (2 * T * sigma * math.sqrt(T))
            
            return charm / 365  # Convert to daily charm
        except Exception as e:
            print(f"Error calculating BS charm: {e}")
            return 0
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model and its parameters."""
        info = {
            'current_model': self.current_model,
            'available_models': ['black_scholes', 'sabr', 'dupire', 'heston', 'dupire_heston']
        }

        # Add parameters for current model
        if self.current_model == 'sabr':
            info['sabr_parameters'] = self.sabr_params
        elif self.current_model == 'heston':
            info['heston_parameters'] = self.heston_params
        elif self.current_model == 'dupire':
            info['dupire_parameters'] = self.dupire_params
        elif self.current_model == 'dupire_heston':
            info['dupire_heston_parameters'] = self.dupire_heston_params
            info['heston_parameters'] = self.heston_params
            info['dupire_parameters'] = self.dupire_params

        return info


# Global pricing model manager instance
pricing_manager = PricingModelManager()


def get_pricing_manager() -> PricingModelManager:
    """Get the global pricing model manager instance."""
    return pricing_manager


def set_pricing_model(model_name: str):
    """Set the global pricing model."""
    pricing_manager.set_model(model_name)


def get_current_pricing_model() -> str:
    """Get the current pricing model name."""
    return pricing_manager.get_current_model()


def set_sabr_parameters(alpha: float = None, beta: float = None, 
                       rho: float = None, nu: float = None):
    """Set SABR model parameters."""
    pricing_manager.set_sabr_params(alpha, beta, rho, nu)


def get_sabr_parameters() -> Dict[str, float]:
    """Get current SABR parameters."""
    return pricing_manager.get_sabr_params()


def set_heston_parameters(kappa: float = None, theta: float = None,
                         sigma: float = None, rho: float = None, v0: float = None):
    """Set Heston model parameters."""
    pricing_manager.set_heston_params(kappa, theta, sigma, rho, v0)


def get_heston_parameters() -> Dict[str, float]:
    """Get current Heston parameters."""
    return pricing_manager.get_heston_params()


def set_dupire_parameters(spot_price: float = None, risk_free_rate: float = None,
                         dividend_yield: float = None):
    """Set Dupire model parameters."""
    pricing_manager.set_dupire_params(spot_price, risk_free_rate, dividend_yield)


def get_dupire_parameters() -> Dict[str, float]:
    """Get current Dupire parameters."""
    return pricing_manager.get_dupire_params()


def set_dupire_heston_parameters(dupire_weight: float = None,
                                correlation_adjustment: float = None):
    """Set Dupire-Heston hybrid model parameters."""
    pricing_manager.set_dupire_heston_params(dupire_weight, correlation_adjustment)


def get_dupire_heston_parameters() -> Dict[str, float]:
    """Get current Dupire-Heston parameters."""
    return pricing_manager.get_dupire_heston_params()
