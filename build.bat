@echo off
echo ========================================
echo Greek Terminal Executable Builder
echo ========================================
echo.

:menu
echo Choose build type:
echo 1. Admin Version (Never expires)
echo 2. License Version (Requires license key)
echo 3. Custom Build
echo 4. Exit
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto admin
if "%choice%"=="2" goto license
if "%choice%"=="3" goto custom
if "%choice%"=="4" goto exit
echo Invalid choice. Please try again.
goto menu

:admin
echo Building Admin Version...
python build_executable.py admin --output-name "GreekTerminal-Admin"
goto done

:license
echo Building License Version...
python build_executable.py license --output-name "GreekTerminal-License"
goto done

:custom
echo.
set /p name="Enter output name (without .exe): "
set /p desc="Enter description: "
echo Building Custom Version...
python build_executable.py custom --output-name "%name%" --description "%desc%"
goto done

:done
echo.
echo Build completed! Check the 'dist' folder for your executable.
pause
goto menu

:exit
echo Goodbye!
pause
