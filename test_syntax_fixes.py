#!/usr/bin/env python3
"""
Test script to verify syntax fixes are working
"""

import sys
import ast

def test_syntax():
    """Test that app.py has valid Python syntax"""
    print("🧪 Testing Python syntax...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Parse the AST to check for syntax errors
        ast.parse(source_code)
        print("✅ app.py syntax is valid!")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error in app.py:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ Error reading app.py: {e}")
        return False

def test_imports():
    """Test that critical imports work"""
    print("🧪 Testing critical imports...")
    
    try:
        # Test basic imports
        import numpy as np
        import pandas as pd
        from datetime import datetime
        print("✅ NumPy, Pandas, datetime imports work")
        
        # Test Flask imports
        from flask import Flask, jsonify
        print("✅ Flask imports work")
        
        # Test optional SciPy
        try:
            from scipy.stats import norm
            print("✅ SciPy imports work")
        except ImportError:
            print("⚠️ SciPy not available (optional)")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_function_definitions():
    """Test that key functions are defined correctly"""
    print("🧪 Testing function definitions...")
    
    try:
        # Import the module to check function definitions
        sys.path.insert(0, '.')
        
        # Test that we can import key functions without syntax errors
        from app import (
            calculate_implied_volatility_surface,
            calculate_local_volatility_surface,
            calculate_skew_metrics,
            calculate_volatility_metrics
        )
        
        print("✅ Key surface generation functions are defined correctly")
        return True
        
    except SyntaxError as e:
        print(f"❌ Function definition error: {e}")
        return False
    except ImportError as e:
        print(f"⚠️ Import warning (may be due to dependencies): {e}")
        return True  # Don't fail on import warnings
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run all syntax tests"""
    print("🔧 Testing Surface Generation Syntax Fixes")
    print("=" * 50)
    
    tests = [
        ("Python Syntax", test_syntax),
        ("Critical Imports", test_imports),
        ("Function Definitions", test_function_definitions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All syntax fixes are working correctly!")
        print("✅ Surface generation should now load without errors")
    else:
        print("⚠️ Some issues remain - check the errors above")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
