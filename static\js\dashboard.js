/**
 * Greek Terminal - Main JavaScript
 * Handles UI interactions, API calls, and chart rendering
 */

class GreekTerminal {
    constructor() {
        this.socket = null;
        this.socketIOAvailable = false;
        this.currentTicker = 'SPY';
        this.currentExpiry = null;
        this.currentPrice = null;
        this.autoRefreshInterval = null;
        this.isLoading = false;
        this.chartData = {}; // Store chart data for settings updates

        // TradingView Lightweight Charts
        this.candlestickChart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.zoneFillSeries = []; // Store zone fill series for cleanup
        this.zoneOverlays = []; // Store CSS zone overlays for cleanup
        this.priceLines = []; // Store price lines for cleanup

        // Store candlestick data for fullscreen rendering
        this.candlestickData = null;
        this.volumeData = null;

        // Store gamma zone data for fullscreen rendering
        this.gammaZoneData = null;
        this.fullscreenZoneFillSeries = [];

        // GEX Visualizer data
        this.gexHistory = {
            timestamps: [],
            prices: [], // Add price history tracking
            oi_data: {
                min_strikes: [],
                max_strikes: [],
                min_gex: [],
                max_gex: [],
                ratios: []
            },
            vol_data: {
                min_strikes: [],
                max_strikes: [],
                min_gex: [],
                max_gex: [],
                ratios: []
            }
        };

        // Track if volatility tab has been auto-loaded
        this.volatilityTabLoaded = false;

        // Track if exposure surface tab has been auto-loaded
        this.exposureSurfaceTabLoaded = false;

        // Store exposure surface data to persist across tab switches
        this.exposureSurfaceData = null;
        this.exposureSurfaceCurrentPrice = null;

        // Exposure heatmap session data (for showing price development throughout session)
        this.exposureSessionData = {
            timestamps: [],
            prices: [],
            candlestickData: []
        };

        this.init();
    }

    // Helper function to get CSS color variables with fallbacks
    getCSSColors() {
        return {
            callColor: getComputedStyle(document.documentElement).getPropertyValue('--call-color').trim() || '#ffffff',
            putColor: getComputedStyle(document.documentElement).getPropertyValue('--put-color').trim() || '#666666',
            textPrimary: getComputedStyle(document.documentElement).getPropertyValue('--text-primary').trim() || '#ffffff',
            textAxis: getComputedStyle(document.documentElement).getPropertyValue('--text-axis').trim() || '#999999',
            gridColor: getComputedStyle(document.documentElement).getPropertyValue('--grid-color').trim() || '#666666',
            bgChart: getComputedStyle(document.documentElement).getPropertyValue('--bg-chart').trim() || '#000000',
            bgChartAlt: getComputedStyle(document.documentElement).getPropertyValue('--bg-chart-alt').trim() || '#000000',
            glassBg: getComputedStyle(document.documentElement).getPropertyValue('--glass-bg').trim() || 'rgba(255, 255, 255, 0.08)',
            glassBorder: getComputedStyle(document.documentElement).getPropertyValue('--glass-border').trim() || 'rgba(255, 255, 255, 0.15)',
            borderColor: getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim() || 'rgba(255, 255, 255, 0.15)',
            accentWarning: getComputedStyle(document.documentElement).getPropertyValue('--accent-warning').trim() || '#cccccc',
            accentInfo: getComputedStyle(document.documentElement).getPropertyValue('--accent-info').trim() || '#ffffff',
            zoneAlpha1: parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--zone-alpha-1').trim()) || 0.4,
            zoneAlpha2: parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--zone-alpha-2').trim()) || 0.3,
            zoneAlpha3: parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--zone-alpha-3').trim()) || 0.2
        };
    }

    // Helper function to create consistent chart layout
    createChartLayout(title, yAxisTitle, currentPrice) {
        const colors = this.getCSSColors();

        return {
            title: {
                text: `${title} - Current: $${currentPrice?.toFixed(2) || 'N/A'}`,
                font: {
                    color: colors.textPrimary,
                    size: 18,
                    family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                }
            },
            xaxis: {
                title: {
                    text: 'Strike Price',
                    font: {
                        color: colors.textAxis,
                        family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                    }
                },
                tickfont: {
                    color: colors.textAxis,
                    family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                },
                gridcolor: colors.gridColor,
                gridwidth: 0.8,
                showgrid: true,
                zeroline: false
            },
            yaxis: {
                title: {
                    text: yAxisTitle,
                    font: {
                        color: colors.textAxis,
                        family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                    }
                },
                tickfont: {
                    color: colors.textAxis,
                    family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                },
                gridcolor: colors.gridColor,
                gridwidth: 0.8,
                showgrid: true,
                zeroline: true,
                zerolinecolor: colors.borderColor,
                zerolinewidth: 1
            },
            paper_bgcolor: colors.bgChart,
            plot_bgcolor: colors.bgChartAlt,
            font: {
                color: colors.textPrimary,
                family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
            },
            showlegend: false,
            legend: {
                font: {
                    color: colors.textPrimary,
                    family: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif'
                },
                bgcolor: colors.glassBg,
                bordercolor: colors.glassBorder,
                borderwidth: 1
            },
            margin: { t: 70, b: 60, l: 90, r: 60 }
        };
    }

    init() {
        console.log('Dashboard init() called');

        // Check if Plotly is loaded
        if (typeof Plotly === 'undefined') {
            console.error('❌ Plotly.js is not loaded! Charts will not work.');
            setTimeout(() => this.init(), 100); // Retry after 100ms
            return;
        }
        console.log('✅ Plotly.js is loaded and ready');

        this.showLoadingOverlay();
        this.checkSocketIOAvailability().then(() => {
            this.initializeSocketIO();
            this.bindEventListeners();
            this.initializeChartSettings();
            this.initializeFullscreenHandlers();
            this.initializeChartHelp();
            this.loadInitialData().finally(() => {
                this.hideLoadingOverlay();
            });
        });
        console.log('Dashboard initialization complete');
    }

    showLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 500);
        }
    }

    async checkSocketIOAvailability() {
        try {
            const response = await fetch('/api/socketio-status');
            const data = await response.json();
            this.socketIOAvailable = data.socketio_available;

            if (!this.socketIOAvailable) {
                console.log('🔌 SocketIO not available - running in executable mode');
                console.log('📡 Real-time updates disabled, using polling mode');
            } else {
                console.log('🔌 SocketIO available - real-time updates enabled');
            }
        } catch (error) {
            console.warn('Failed to check SocketIO status:', error);
            this.socketIOAvailable = false;
        }
    }

    initializeSocketIO() {
        if (!this.socketIOAvailable) {
            console.log('🔌 Skipping SocketIO initialization - not available');
            this.updateConnectionStatus(false);
            return;
        }

        // Check if io is defined (Socket.IO library loaded)
        if (typeof io === 'undefined') {
            console.warn('🔌 Socket.IO library not loaded - disabling real-time features');
            this.socketIOAvailable = false;
            this.updateConnectionStatus(false);
            return;
        }

        try {
            this.socket = io();

            this.socket.on('connect', () => {
                console.log('Connected to server');
                this.updateConnectionStatus(true);
            });

            this.socket.on('disconnect', () => {
                console.log('Disconnected from server');
                this.updateConnectionStatus(false);
            });

            this.socket.on('status', (data) => {
                console.log('Server status:', data.msg);
            });

            // Listen for exposure surface progress updates
            this.socket.on('gamma_surface_progress', (data) => {
                this.updateExposureSurfaceProgress(data);
            });

            // Listen for exposure surface completion
            this.socket.on('gamma_surface_complete', (data) => {
                this.handleExposureSurfaceComplete(data);
            });

            this.socket.on('ticker_subscribed', (data) => {
                console.log('Subscribed to ticker updates:', data);
            });
        } catch (error) {
            console.error('Failed to initialize SocketIO:', error);
            this.socketIOAvailable = false;
            this.socket = null;
            this.updateConnectionStatus(false);
        }
    }
    
    bindEventListeners() {
        console.log('Binding event listeners...');

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.handleLogout();
            });
            console.log('✅ Logout button listener bound');
        } else {
            console.error('❌ logout-btn element not found');
        }

        // Disconnect data button
        const disconnectBtn = document.getElementById('disconnect-data-btn');
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                this.handleDisconnectData();
            });
            console.log('✅ Disconnect data button listener bound');
        } else {
            console.error('❌ disconnect-data-btn element not found');
        }

        // Ticker input change
        const tickerInput = document.getElementById('ticker-input');
        if (tickerInput) {
            tickerInput.addEventListener('change', (e) => {
                console.log('Ticker changed to:', e.target.value);
                this.currentTicker = e.target.value.toUpperCase();
                this.volatilityTabLoaded = false; // Reset volatility tab auto-load flag
                this.exposureSurfaceTabLoaded = false; // Reset exposure surface tab auto-load flag
                // Clear exposure surface data when ticker changes
                this.exposureSurfaceData = null;
                this.exposureSurfaceCurrentPrice = null;
                // Clear exposure session data when ticker changes
                this.clearExposureSessionData();
                this.loadExpiryDates();
            });
            console.log('✅ Ticker input listener bound');
        } else {
            console.error('❌ ticker-input element not found');
        }

        // Expiry date change
        const expirySelect = document.getElementById('expiry-select');
        if (expirySelect) {
            expirySelect.addEventListener('change', (e) => {
                this.currentExpiry = e.target.value;
                console.log('Expiry changed to:', this.currentExpiry);

                // Reset volatility tab auto-load flag when expiry changes
                this.volatilityTabLoaded = false;

                // Reset exposure surface tab auto-load flag when expiry changes
                this.exposureSurfaceTabLoaded = false;

                // Clear exposure surface data when expiry changes
                this.exposureSurfaceData = null;
                this.exposureSurfaceCurrentPrice = null;
                // Clear exposure session data when expiry changes
                this.clearExposureSessionData();

                // Update gamma regime display when expiry changes
                this.setGammaRegimeDisplay('Ready', 'info');
                console.log('✅ Expiry changed. Click "Fetch Data" to load options data.');
            });
            console.log('✅ Expiry select listener bound');
        } else {
            console.error('❌ expiry-select element not found');
        }

        // Fetch data button
        const fetchBtn = document.getElementById('fetch-data-btn');
        if (fetchBtn) {
            fetchBtn.addEventListener('click', () => {
                console.log('🔄 Fetch data button clicked - refreshing all charts!');
                // Make sure we have the current expiry selected
                const expirySelect = document.getElementById('expiry-select');
                if (expirySelect.value) {
                    this.currentExpiry = expirySelect.value;
                    console.log('Refreshing all charts for:', this.currentTicker, this.currentExpiry);
                    this.fetchOptionsData(); // This now refreshes all charts
                } else {
                    console.log('No expiry selected');
                    this.showError('Please select an expiry date first');
                }
            });
            console.log('✅ Fetch button listener bound');
        } else {
            console.error('❌ fetch-data-btn element not found');
        }
        
        // Auto refresh toggle
        document.getElementById('auto-refresh').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
        


        // Chart timeframe controls - update chart button removed

        // Refresh zones button
        const refreshZonesBtn = document.getElementById('refresh-zones-btn');
        if (refreshZonesBtn) {
            refreshZonesBtn.addEventListener('click', () => {
                console.log('Refresh zones button clicked');
                this.addGammaZonesToChart();
            });
            console.log('✅ Refresh zones button listener bound');
        } else {
            console.error('❌ refresh-zones-btn element not found');
        }

        // Refresh signals button
        const refreshSignalsBtn = document.getElementById('refresh-signals-btn');
        if (refreshSignalsBtn) {
            refreshSignalsBtn.addEventListener('click', () => {
                console.log('Refresh signals button clicked');
                this.fetchSignals();
            });
            console.log('✅ Refresh signals button listener bound');
        } else {
            console.error('❌ refresh-signals-btn element not found');
        }

        // Signals auto-refresh toggle
        const signalsAutoRefresh = document.getElementById('signals-auto-refresh');
        if (signalsAutoRefresh) {
            signalsAutoRefresh.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.startSignalsAutoRefresh();
                } else {
                    this.stopSignalsAutoRefresh();
                }
            });
        }

        // Quick timeframe buttons
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const period = e.target.getAttribute('data-period');
                const interval = e.target.getAttribute('data-interval');
                document.getElementById('chart-period').value = period;
                document.getElementById('chart-interval').value = interval;
                this.renderCandlestickChart();
            });
        });

        // Tab change events
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                this.handleTabChange(e.target.getAttribute('data-bs-target'));
            });
        });

        // GEX Visualizer controls
        const gexVisualizerRefresh = document.getElementById('gex-visualizer-refresh');
        if (gexVisualizerRefresh) {
            gexVisualizerRefresh.addEventListener('click', () => {
                this.collectGexData();
            });
        }

        const gexVisualizerClear = document.getElementById('gex-visualizer-clear');
        if (gexVisualizerClear) {
            gexVisualizerClear.addEventListener('click', () => {
                this.clearGexHistory();
            });
        }

        const gexVisualizerType = document.getElementById('gex-visualizer-type');
        if (gexVisualizerType) {
            gexVisualizerType.addEventListener('change', () => {
                this.updateGexVisualizer();
            });
        }

        // Volatility controls
        const volatilityViewType = document.getElementById('volatility-view-type');
        if (volatilityViewType) {
            volatilityViewType.addEventListener('change', () => {
                // Auto-refresh data when view type changes
                if (this.currentTicker && this.currentExpiry) {
                    console.log('✅ Volatility view type changed - auto-refreshing data...');
                    this.fetchVolatilityData();
                } else {
                    console.log('✅ Volatility view type changed - please select ticker and expiry first');
                    this.handleVolatilityViewChange();
                }
            });
            console.log('✅ Volatility view type listener bound');
        }

        const volatilityRefresh = document.getElementById('volatility-refresh-btn');
        if (volatilityRefresh) {
            volatilityRefresh.addEventListener('click', () => {
                this.fetchVolatilityData();
            });
            console.log('✅ Volatility refresh listener bound');
        }

        // Exposure surface controls
        const exposureSurfaceRefresh = document.getElementById('exposure-surface-refresh-btn');
        if (exposureSurfaceRefresh) {
            exposureSurfaceRefresh.addEventListener('click', () => {
                this.fetchExposureSurface();
            });
            console.log('✅ Exposure surface refresh listener bound');
        }

        // Exposure surface view type change
        const exposureSurfaceViewType = document.getElementById('exposure-surface-view-type');
        if (exposureSurfaceViewType) {
            exposureSurfaceViewType.addEventListener('change', () => {
                // Re-render existing data with new view type if data exists
                if (this.exposureSurfaceData && this.exposureSurfaceCurrentPrice) {
                    console.log('✅ Exposure surface view type changed - re-rendering with existing data...');
                    this.renderExposureSurface(this.exposureSurfaceData, this.exposureSurfaceCurrentPrice);
                } else {
                    console.log('✅ Exposure surface view type changed - calculate data first');
                }
            });
            console.log('✅ Exposure surface view type listener bound');
        }

        // Exposure surface GEX type change
        const exposureSurfaceGexType = document.getElementById('exposure-surface-gex-type');
        if (exposureSurfaceGexType) {
            exposureSurfaceGexType.addEventListener('change', () => {
                // Clear existing data when GEX type changes since we need to recalculate
                this.exposureSurfaceData = null;
                this.exposureSurfaceCurrentPrice = null;
                console.log('✅ Exposure surface GEX type changed - data cleared, recalculation needed');

                // Show instructions to recalculate
                this.showExposureSurfaceInstructions();
            });
            console.log('✅ Exposure surface GEX type listener bound');
        }

        // Exposure surface show options change
        const exposureSurfaceShowOptions = [
            'exposure-surface-show-calls',
            'exposure-surface-show-puts',
            'exposure-surface-show-net'
        ];

        exposureSurfaceShowOptions.forEach(optionId => {
            const element = document.getElementById(optionId);
            if (element) {
                element.addEventListener('change', () => {
                    // Re-render existing data with new show options if data exists
                    if (this.exposureSurfaceData && this.exposureSurfaceCurrentPrice) {
                        console.log('✅ Exposure surface show options changed - re-rendering with existing data...');
                        this.renderExposureSurface(this.exposureSurfaceData, this.exposureSurfaceCurrentPrice);
                    } else {
                        console.log('✅ Exposure surface show options changed - calculate data first');
                    }
                });
                console.log(`✅ Exposure surface ${optionId} listener bound`);
            }
        });



        // Volatility settings change listeners
        const volatilitySettings = [
            'volatility-show-calls',
            'volatility-show-puts'
        ];

        volatilitySettings.forEach(settingId => {
            const element = document.getElementById(settingId);
            if (element) {
                element.addEventListener('change', () => {
                    // Re-render charts with new settings (only if data is already loaded)
                    const activeTab = document.querySelector('.nav-link.active')?.getAttribute('data-bs-target');
                    if (activeTab === '#volatility-pane') {
                        // Only re-render if we already have data, don't fetch new data
                        this.handleVolatilityViewChange();
                        console.log('✅ Volatility settings changed - click refresh to update data');
                    }
                });
                console.log(`✅ ${settingId} listener bound`);
            }
        });



        // Export to TradingView button
        const exportTradingViewBtn = document.getElementById('export-tradingview-btn');
        if (exportTradingViewBtn) {
            exportTradingViewBtn.addEventListener('click', () => {
                this.exportToTradingView();
            });
        }

        // Copy Pine Script button
        const copyPineScriptBtn = document.getElementById('copy-pine-script-btn');
        if (copyPineScriptBtn) {
            copyPineScriptBtn.addEventListener('click', () => {
                this.copyPineScriptToClipboard();
            });
        }

        // TradingView modal close event - reset button state when modal is closed
        const tradingViewModal = document.getElementById('tradingview-export-modal');
        if (tradingViewModal) {
            tradingViewModal.addEventListener('hidden.bs.modal', () => {
                console.log('TradingView modal closed, resetting button state');
                this.resetExportButtonState();
            });

            // Also handle when modal is being hidden (before fully closed)
            tradingViewModal.addEventListener('hide.bs.modal', () => {
                console.log('TradingView modal closing...');
            });
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanupTradingViewChart();
        });
    }

    initializeChartSettings() {
        // Set default candlestick chart timeframe with multiple attempts to ensure it sticks
        const setDefaults = () => {
            const periodSelect = document.getElementById('chart-period');
            const intervalSelect = document.getElementById('chart-interval');

            console.log('🔍 Chart elements found:', {
                periodSelect: !!periodSelect,
                intervalSelect: !!intervalSelect,
                periodValue: periodSelect?.value,
                intervalValue: intervalSelect?.value
            });

            if (periodSelect) {
                periodSelect.value = '5d';
                console.log(`✅ Set period to: ${periodSelect.value}`);
            } else {
                console.error('❌ chart-period element not found');
            }

            if (intervalSelect) {
                // Force remove any selected attribute from 1h option
                const options = intervalSelect.querySelectorAll('option');
                options.forEach(option => {
                    if (option.value === '1h') {
                        option.removeAttribute('selected');
                    } else if (option.value === '1m') {
                        option.setAttribute('selected', 'selected');
                    }
                });

                intervalSelect.value = '1m';
                console.log(`✅ Set interval to: ${intervalSelect.value}`);
            } else {
                console.error('❌ chart-interval element not found');
            }

            console.log('✅ Default chart timeframe set: 5d/1m');
        };

        // Try immediately and then with delays
        setDefaults();
        setTimeout(setDefaults, 100);
        setTimeout(setDefaults, 500);

        // Set checkbox defaults with delays to ensure DOM is ready
        const setCheckboxDefaults = () => {
            const chartTypes = ['gex', 'vgex', 'dex', 'dgex', 'tex', 'vegx', 'vex', 'cex', 'oi'];

            chartTypes.forEach(chartType => {
                const callsCheckbox = document.getElementById(`${chartType}-show-calls`);
                const putsCheckbox = document.getElementById(`${chartType}-show-puts`);
                const netCheckbox = document.getElementById(`${chartType}-show-net`);

                if (callsCheckbox) callsCheckbox.checked = false;
                if (putsCheckbox) putsCheckbox.checked = false;
                if (netCheckbox) netCheckbox.checked = true;
            });

            console.log('✅ Set default checkbox states: only net enabled');
        };

        setTimeout(setCheckboxDefaults, 100);
        setTimeout(setCheckboxDefaults, 500);
        setTimeout(setCheckboxDefaults, 1000);

        // Initialize chart settings event listeners
        const chartTypes = ['gex', 'vgex', 'dex', 'dgex', 'tex', 'vegx', 'vex', 'cex', 'oi', 'premium'];

        chartTypes.forEach(chartType => {
            // Set default checkbox states: only net should be checked
            const callsCheckbox = document.getElementById(`${chartType}-show-calls`);
            const putsCheckbox = document.getElementById(`${chartType}-show-puts`);
            const netCheckbox = document.getElementById(`${chartType}-show-net`);

            if (callsCheckbox) callsCheckbox.checked = false;
            if (putsCheckbox) putsCheckbox.checked = false;
            if (netCheckbox) netCheckbox.checked = true;

            console.log(`✅ Set default checkboxes for ${chartType}: calls=false, puts=false, net=true`);

            // Chart type dropdown
            const chartTypeSelect = document.getElementById(`${chartType}-chart-type`);
            if (chartTypeSelect) {
                chartTypeSelect.addEventListener('change', () => {
                    this.updateChartDisplay(chartType);
                });
            }

            // Strike range input
            const strikeRangeInput = document.getElementById(`${chartType}-strike-range`);
            if (strikeRangeInput) {
                strikeRangeInput.addEventListener('input', () => {
                    this.updateChartDisplay(chartType);
                });
            }

            // Display option checkboxes
            ['calls', 'puts', 'net'].forEach(option => {
                const checkbox = document.getElementById(`${chartType}-show-${option}`);
                if (checkbox) {
                    checkbox.addEventListener('change', () => {
                        this.updateChartDisplay(chartType);
                    });
                }
            });

            // Special settings for specific charts
            // (Removed volume and delta filters as they are not necessary)
        });

        // Special event listener for premium view mode dropdown
        const premiumViewMode = document.getElementById('premium-view-mode');
        if (premiumViewMode) {
            premiumViewMode.addEventListener('change', () => {
                // Re-fetch premium data with new view mode
                this.fetchPremiumAnalysis();
            });
        }
    }

    updateChartDisplay(chartType) {
        // Re-render the chart with current settings
        if (!this.chartData[chartType]) return;

        switch (chartType) {
            case 'gex':
                this.renderGammaExposureChart(this.chartData.gex, this.currentPrice);
                break;
            case 'vgex':
                this.renderVolumeGammaChart(this.chartData.vgex, this.currentPrice);
                break;
            case 'dex':
                this.renderDeltaExposureChart(this.chartData.dex, this.currentPrice);
                break;
            case 'dgex':
                this.renderDeltaGammaChart(this.chartData.dgex, this.currentPrice);
                break;
            case 'tex':
                this.renderThetaExposureChart(this.chartData.tex, this.currentPrice);
                break;
            case 'vegx':
                this.renderVegaExposureChart(this.chartData.vegx, this.currentPrice);
                break;
            case 'vex':
                this.renderVannaExposureChart(this.chartData.vex, this.currentPrice);
                break;
            case 'cex':
                this.renderCharmExposureChart(this.chartData.cex, this.currentPrice);
                break;
            case 'oi':
                this.renderOpenInterestChart(this.chartData.oi, this.currentPrice);
                break;
            case 'premium':
                // For premium chart, re-fetch data since view mode might have changed
                this.fetchPremiumAnalysis();
                break;
        }
    }

    async loadInitialData() {
        await this.loadExpiryDates();
        await this.updateCurrentPrice();
        // Initialize gamma regime indicator
        this.setGammaRegimeDisplay('Loading...', 'loading');
        // Load initial candlestick chart
        await this.renderCandlestickChart();
    }
    
    /**
     * Find 0DTE (0 Days to Expiration) expiration date from the list
     * @param {Array} expiryDates - Array of expiry date strings
     * @returns {string|null} - 0DTE expiry date or null if not found
     */
    find0DTEExpiration(expiryDates) {
        try {
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD

            // Look for exact match with today's date
            const todayExpiry = expiryDates.find(date => date === todayStr);
            if (todayExpiry) {
                console.log(`Found exact 0DTE match: ${todayExpiry}`);
                return todayExpiry;
            }

            // If no exact match, look for dates that are today (accounting for different formats)
            for (const date of expiryDates) {
                try {
                    const expiryDate = new Date(date);
                    const expiryStr = expiryDate.toISOString().split('T')[0];

                    if (expiryStr === todayStr) {
                        console.log(`Found 0DTE match with date parsing: ${date} -> ${expiryStr}`);
                        return date;
                    }
                } catch (e) {
                    // Skip invalid dates
                    continue;
                }
            }

            console.log('No 0DTE expiration found for today:', todayStr);
            return null;
        } catch (error) {
            console.error('Error finding 0DTE expiration:', error);
            return null;
        }
    }

    async loadExpiryDates() {
        try {
            console.log(`Loading expiry dates for ${this.currentTicker}`);
            const response = await fetch(`/api/expiry-dates/${this.currentTicker}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Expiry dates response:', data);

            const select = document.getElementById('expiry-select');
            select.innerHTML = '';

            if (data.success && data.expiry_dates && data.expiry_dates.length > 0) {
                console.log(`Found ${data.expiry_dates.length} expiry dates`);

                // Add all expiry date options (no default "Select" option)
                data.expiry_dates.forEach(date => {
                    const option = document.createElement('option');
                    option.value = date;
                    try {
                        option.textContent = this.formatDate(date);
                    } catch (e) {
                        console.warn('Date formatting failed, using raw date:', e);
                        option.textContent = date; // Fallback to raw date
                    }
                    select.appendChild(option);
                });

                // Try to find and select 0DTE expiration first
                const zerodte = this.find0DTEExpiration(data.expiry_dates);
                if (zerodte) {
                    this.currentExpiry = zerodte;
                    select.value = this.currentExpiry;
                    console.log(`✅ Auto-selected 0DTE expiry: ${this.currentExpiry}`);
                } else {
                    // Fallback to first expiry date if no 0DTE found
                    this.currentExpiry = data.expiry_dates[0];
                    select.value = this.currentExpiry;
                    console.log(`⚠️ No 0DTE found, selected first expiry: ${this.currentExpiry}`);
                }

                // Don't auto-fetch - let user click "Fetch Data" button
                console.log('✅ Expiry selected. Click "Fetch Data" to load options data.');
            } else {
                console.error('No expiry dates found:', data.error || 'Unknown error');
                const option = document.createElement('option');
                option.value = '';
                option.textContent = data.error || 'No expiry dates available';
                select.appendChild(option);
                this.showError(data.error || 'No expiry dates available for this ticker');
            }
        } catch (error) {
            console.error('Error loading expiry dates:', error);

            // Show error in dropdown
            const select = document.getElementById('expiry-select');
            select.innerHTML = '';
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'Error loading dates';
            select.appendChild(option);

            this.showError('Failed to load expiry dates: ' + error.message);
        }
    }
    
    async updateCurrentPrice() {
        try {
            const response = await fetch(`/api/current-price/${this.currentTicker}`);
            const data = await response.json();

            if (data.success && data.price) {
                this.currentPrice = data.price;
                // Current price display elements removed from UI
            }
        } catch (error) {
            console.error('Error updating current price:', error);
        }
    }
    
    async fetchOptionsData() {
        if (!this.currentExpiry) {
            this.showError('Please select an expiry date');
            return;
        }

        this.showLoading(true);

        try {
            console.log(`Fetching options data for ${this.currentTicker} ${this.currentExpiry}`);
            const response = await fetch(`/api/options-data/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            console.log('Options data response:', data);

            if (data.success) {
                this.currentPrice = data.current_price;
                await this.updateCurrentPrice();

                // Refresh ALL charts regardless of active tab
                await this.refreshAllCharts();

                // Update gamma regime indicator
                console.log('🎯 About to update gamma regime...');
                await this.updateGammaRegime();
                console.log('✅ Gamma regime update completed');

                // Subscribe to real-time updates
                if (this.socket) {
                    this.socket.emit('subscribe_ticker', {
                        ticker: this.currentTicker,
                        expiry_date: this.currentExpiry
                    });
                }

                this.showSuccess('All charts refreshed successfully');
            } else {
                this.showError(data.error || 'Failed to fetch options data');
            }
        } catch (error) {
            console.error('Error fetching options data:', error);
            this.showError('Failed to fetch options data: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }
    
    async fetchGammaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/gamma-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderGammaExposureChart(data.gex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching gamma exposure:', error);
        }
    }

    async fetchVolumeGamma() {
        if (!this.currentExpiry) {
            console.log('No expiry date selected for VGEX');
            return;
        }

        try {
            console.log(`Fetching VGEX for ${this.currentTicker} ${this.currentExpiry}`);
            const response = await fetch(`/api/volume-gamma/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            console.log('VGEX response:', data);

            if (data.success) {
                this.renderVolumeGammaChart(data.vgex_data, data.current_price);
            } else {
                console.error('VGEX fetch failed:', data.error);
                this.showError('Failed to load Volume Gamma data: ' + data.error);
            }
        } catch (error) {
            console.error('Error fetching volume gamma:', error);
            this.showError('Error fetching Volume Gamma: ' + error.message);
        }
    }

    async fetchDeltaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/delta-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderDeltaExposureChart(data.dex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching delta exposure:', error);
        }
    }

    async fetchDeltaGammaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/delta-gamma-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderDeltaGammaChart(data.dgex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching delta-gamma exposure:', error);
        }
    }

    async fetchThetaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/theta-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderThetaExposureChart(data.tex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching theta exposure:', error);
        }
    }

    async fetchVegaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/vega-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderVegaExposureChart(data.vegx_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching vega exposure:', error);
        }
    }

    async fetchVannaExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/vanna-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderVannaExposureChart(data.vex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching vanna exposure:', error);
        }
    }

    async fetchCharmExposure() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/charm-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderCharmExposureChart(data.cex_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching charm exposure:', error);
        }
    }

    async fetchOpenInterest() {
        if (!this.currentExpiry) return;

        try {
            const response = await fetch(`/api/open-interest/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                this.renderOpenInterestChart(data.oi_data, data.current_price);
            }
        } catch (error) {
            console.error('Error fetching open interest:', error);
        }
    }

    async fetchPremiumAnalysis() {
        if (!this.currentExpiry) return;

        try {
            // Get view mode from dropdown
            const viewMode = document.getElementById('premium-view-mode')?.value || 'top10';
            const response = await fetch(`/api/premium-analysis/${this.currentTicker}/${this.currentExpiry}?view_mode=${viewMode}`);
            const data = await response.json();

            if (data.success) {
                this.renderPremiumChart(data.premium_data, data.current_price, data.view_mode);
            }
        } catch (error) {
            console.error('Error fetching premium analysis:', error);
        }
    }
    

    
    updateCharts(data) {
        // Update all charts based on current active tab
        const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
        this.handleTabChange(activeTab);

        // Also update candlestick chart since it doesn't depend on options data
        this.renderCandlestickChart();
    }

    async refreshAllCharts() {
        console.log('🔄 Refreshing all charts...');

        try {
            // Always refresh candlestick chart first
            await this.renderCandlestickChart();

            // Refresh all options-based charts
            const chartPromises = [
                this.fetchGammaExposure(),
                this.fetchVolumeGamma(),
                this.fetchDeltaExposure(),
                this.fetchDeltaGammaExposure(),
                this.fetchThetaExposure(),
                this.fetchVegaExposure(),
                this.fetchVannaExposure(),
                this.fetchCharmExposure(),
                this.fetchOpenInterest(),
                this.fetchPremiumAnalysis()
                // Note: Volatility data is only fetched when user visits volatility tab
            ];

            // Wait for all charts to update
            await Promise.all(chartPromises);

            // Update GEX visualizer data
            this.collectGexData();
            this.updateGexVisualizer();

            console.log('✅ All charts refreshed successfully');
        } catch (error) {
            console.error('❌ Error refreshing charts:', error);
        }
    }
    
    handleTabChange(tabTarget) {
        switch (tabTarget) {
            case '#candlestick-pane':
                this.renderCandlestickChart();
                break;
            case '#gex-pane':
                this.fetchGammaExposure();
                // Collect GEX data for visualizer
                this.collectGexData();
                break;
            case '#vgex-pane':
                this.fetchVolumeGamma();
                // Collect GEX data for visualizer
                this.collectGexData();
                break;
            case '#dex-pane':
                this.fetchDeltaExposure();
                break;
            case '#dgex-pane':
                this.fetchDeltaGammaExposure();
                break;
            case '#tex-pane':
                this.fetchThetaExposure();
                break;
            case '#vegx-pane':
                this.fetchVegaExposure();
                break;
            case '#vex-pane':
                this.fetchVannaExposure();
                break;
            case '#cex-pane':
                this.fetchCharmExposure();
                break;
            case '#oi-pane':
                this.fetchOpenInterest();
                break;
            case '#gex-visualizer-pane':
                this.updateGexVisualizer();
                break;
            case '#volatility-pane':
                // Auto-fetch volatility data once when first opened
                if (!this.volatilityTabLoaded && this.currentTicker && this.currentExpiry) {
                    console.log('✅ Volatility tab opened for first time - auto-loading data...');
                    this.volatilityTabLoaded = true;
                    this.fetchVolatilityData();
                } else if (!this.currentTicker || !this.currentExpiry) {
                    console.log('✅ Volatility tab opened - please select ticker and expiry first');
                } else {
                    console.log('✅ Volatility tab opened - data already loaded or click refresh to reload');
                }
                break;
            case '#exposure-surface-pane':
                // Only show instructions if no exposure surface data exists
                if (!this.exposureSurfaceData || this.exposureSurfaceData.length === 0) {
                    console.log('✅ Exposure surface tab opened - click refresh button to calculate');
                    this.showExposureSurfaceInstructions();
                } else {
                    console.log('✅ Exposure surface tab opened - existing data preserved');
                }
                break;
            case '#top-player-positioning-pane':
                this.fetchPremiumAnalysis();
                break;
            case '#signals-pane':
                this.fetchSignals();
                this.initializeSignalsSubtabs();
                break;

        }
    }
    
    renderGammaExposureChart(gexData, currentPrice) {
        // Store data for settings updates
        this.chartData.gex = gexData;

        // Get current settings
        const chartType = document.getElementById('gex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('gex-strike-range')?.value || 20);
        const showCalls = document.getElementById('gex-show-calls')?.checked || false;
        const showPuts = document.getElementById('gex-show-puts')?.checked || false;
        const showNet = document.getElementById('gex-show-net')?.checked !== false;  // Default to true
        const showHighest = document.getElementById('gex-show-highest')?.checked !== false;

        // Filter data by strike range around current price
        let filteredData = gexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = gexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callGex = filteredData.map(d => d.call_gex);
        const putGex = filteredData.map(d => d.put_gex);
        const netGex = filteredData.map(d => d.net_gex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call GEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callGex,
                type: chartType,
                name: 'Call GEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put GEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putGex,
                type: chartType,
                name: 'Put GEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net GEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netGex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netGex,
                type: 'bar',
                name: 'Net GEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netGex), Math.max(...callGex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Gamma Exposure (GEX)', 'Gamma Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('gex-chart', traces, layout, config);
    }
    
    renderVolumeGammaChart(vgexData, currentPrice) {
        // Store data for settings updates
        this.chartData.vgex = vgexData;

        // Get current settings
        const chartType = document.getElementById('vgex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('vgex-strike-range')?.value || 20);
        const showCalls = document.getElementById('vgex-show-calls')?.checked || false;
        const showPuts = document.getElementById('vgex-show-puts')?.checked || false;
        const showNet = document.getElementById('vgex-show-net')?.checked !== false;  // Default to true


        // Filter data by strike range around current price
        let filteredData = vgexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = vgexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }



        const strikes = filteredData.map(d => d.strike);
        const callVgex = filteredData.map(d => d.call_vgex);
        const putVgex = filteredData.map(d => d.put_vgex);
        const netVgex = filteredData.map(d => d.net_vgex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call VGEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callVgex,
                type: chartType,
                name: 'Call VGEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put VGEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putVgex,
                type: chartType,
                name: 'Put VGEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net VGEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netVgex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netVgex,
                type: 'bar',
                name: 'Net VGEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netVgex), Math.max(...callVgex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Volume Gamma Exposure (VGEX)', 'Volume Gamma Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('vgex-chart', traces, layout, config);
    }

    renderDeltaExposureChart(dexData, currentPrice) {
        // Store data for settings updates
        this.chartData.dex = dexData;

        // Get current settings
        const chartType = document.getElementById('dex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('dex-strike-range')?.value || 20);
        const showCalls = document.getElementById('dex-show-calls')?.checked || false;
        const showPuts = document.getElementById('dex-show-puts')?.checked || false;
        const showNet = document.getElementById('dex-show-net')?.checked !== false;  // Default to true


        // Filter data by strike range around current price
        let filteredData = dexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = dexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }



        const strikes = filteredData.map(d => d.strike);
        const callDex = filteredData.map(d => d.call_dex);
        const putDex = filteredData.map(d => d.put_dex);
        const netDex = filteredData.map(d => d.net_dex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call DEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callDex,
                type: chartType,
                name: 'Call DEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put DEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putDex,
                type: chartType,
                name: 'Put DEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net DEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netDex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netDex,
                type: 'bar',
                name: 'Net DEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netDex), Math.max(...callDex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Delta Exposure (DEX)', 'Delta Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('dex-chart', traces, layout, config);
    }

    renderDeltaGammaChart(dgexData, currentPrice) {
        // Store data for settings updates
        this.chartData.dgex = dgexData;

        // Get current settings
        const chartType = document.getElementById('dgex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('dgex-strike-range')?.value || 20);
        const showCalls = document.getElementById('dgex-show-calls')?.checked || false;
        const showPuts = document.getElementById('dgex-show-puts')?.checked || false;
        const showNet = document.getElementById('dgex-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = dgexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = dgexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callDgex = filteredData.map(d => d.call_dgex);
        const putDgex = filteredData.map(d => d.put_dgex);
        const netDgex = filteredData.map(d => d.net_dgex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call D_GEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callDgex,
                type: chartType,
                name: 'Call D_GEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put D_GEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putDgex,
                type: chartType,
                name: 'Put D_GEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net D_GEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netDgex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netDgex,
                type: 'bar',
                name: 'Net D_GEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netDgex), Math.max(...callDgex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Delta-Adjusted Gamma Exposure (D_GEX)', 'Delta-Gamma Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('dgex-chart', traces, layout, config);
    }

    renderThetaExposureChart(texData, currentPrice) {
        // Store data for settings updates
        this.chartData.tex = texData;

        // Get current settings
        const chartType = document.getElementById('tex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('tex-strike-range')?.value || 20);
        const showCalls = document.getElementById('tex-show-calls')?.checked || false;
        const showPuts = document.getElementById('tex-show-puts')?.checked || false;
        const showNet = document.getElementById('tex-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = texData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = texData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callTex = filteredData.map(d => d.call_tex);
        const putTex = filteredData.map(d => d.put_tex);
        const netTex = filteredData.map(d => d.net_tex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call TEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callTex,
                type: chartType,
                name: 'Call TEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put TEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putTex,
                type: chartType,
                name: 'Put TEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net TEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netTex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netTex,
                type: 'bar',
                name: 'Net TEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netTex), Math.max(...callTex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Theta Exposure (TEX)', 'Theta Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('tex-chart', traces, layout, config);
    }

    renderVegaExposureChart(vegxData, currentPrice) {
        // Store data for settings updates
        this.chartData.vegx = vegxData;

        // Get current settings
        const chartType = document.getElementById('vegx-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('vegx-strike-range')?.value || 20);
        const showCalls = document.getElementById('vegx-show-calls')?.checked || false;
        const showPuts = document.getElementById('vegx-show-puts')?.checked || false;
        const showNet = document.getElementById('vegx-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = vegxData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = vegxData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callVegx = filteredData.map(d => d.call_vegx);
        const putVegx = filteredData.map(d => d.put_vegx);
        const netVegx = filteredData.map(d => d.net_vegx);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call VEGX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callVegx,
                type: chartType,
                name: 'Call VEGX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put VEGX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putVegx,
                type: chartType,
                name: 'Put VEGX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net VEGX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netVegx.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netVegx,
                type: 'bar',
                name: 'Net VEGX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netVegx), Math.max(...callVegx)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Vega Exposure (VEGX)', 'Vega Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('vegx-chart', traces, layout, config);
    }

    renderVannaExposureChart(vexData, currentPrice) {
        // Store data for settings updates
        this.chartData.vex = vexData;

        // Get current settings
        const chartType = document.getElementById('vex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('vex-strike-range')?.value || 20);
        const showCalls = document.getElementById('vex-show-calls')?.checked || false;
        const showPuts = document.getElementById('vex-show-puts')?.checked || false;
        const showNet = document.getElementById('vex-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = vexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = vexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callVex = filteredData.map(d => d.call_vex);
        const putVex = filteredData.map(d => d.put_vex);
        const netVex = filteredData.map(d => d.net_vex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call VEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callVex,
                type: chartType,
                name: 'Call VEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put VEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putVex,
                type: chartType,
                name: 'Put VEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net VEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netVex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netVex,
                type: 'bar',
                name: 'Net VEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netVex), Math.max(...callVex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Vanna Exposure (VEX)', 'Vanna Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('vex-chart', traces, layout, config);
    }

    renderCharmExposureChart(cexData, currentPrice) {
        // Store data for settings updates
        this.chartData.cex = cexData;

        // Get current settings
        const chartType = document.getElementById('cex-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('cex-strike-range')?.value || 20);
        const showCalls = document.getElementById('cex-show-calls')?.checked || false;
        const showPuts = document.getElementById('cex-show-puts')?.checked || false;
        const showNet = document.getElementById('cex-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = cexData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = cexData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callCex = filteredData.map(d => d.call_cex);
        const putCex = filteredData.map(d => d.put_cex);
        const netCex = filteredData.map(d => d.net_cex);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call CEX trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callCex,
                type: chartType,
                name: 'Call CEX',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put CEX trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putCex,
                type: chartType,
                name: 'Put CEX',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net CEX trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netCex.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netCex,
                type: 'bar',
                name: 'Net CEX',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...netCex), Math.max(...callCex)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Charm Exposure (CEX)', 'Charm Exposure', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('cex-chart', traces, layout, config);
    }

    renderOpenInterestChart(oiData, currentPrice) {
        // Store data for settings updates
        this.chartData.oi = oiData;

        // Get current settings
        const chartType = document.getElementById('oi-chart-type')?.value || 'bar';
        const strikeRange = parseInt(document.getElementById('oi-strike-range')?.value || 20);
        const showCalls = document.getElementById('oi-show-calls')?.checked || false;
        const showPuts = document.getElementById('oi-show-puts')?.checked || false;
        const showNet = document.getElementById('oi-show-net')?.checked !== false;  // Default to true

        // Filter data by strike range around current price
        let filteredData = oiData;
        if (currentPrice && strikeRange > 0) {
            const minStrike = currentPrice - strikeRange;
            const maxStrike = currentPrice + strikeRange;
            filteredData = oiData.filter(d => d.strike >= minStrike && d.strike <= maxStrike);
        }

        const strikes = filteredData.map(d => d.strike);
        const callOi = filteredData.map(d => d.call_oi);
        const putOi = filteredData.map(d => d.put_oi);
        const netOi = filteredData.map(d => d.net_oi);

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Call OI trace
        if (showCalls) {
            traces.push({
                x: strikes,
                y: callOi,
                type: chartType,
                name: 'Call OI',
                marker: {
                    color: colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.callColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.callColor, width: 2.5 } : undefined
            });
        }

        // Add Put OI trace
        if (showPuts) {
            traces.push({
                x: strikes,
                y: putOi,
                type: chartType,
                name: 'Put OI',
                marker: {
                    color: colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
                    line: { color: colors.putColor, width: 1.2 }
                },
                line: chartType === 'line' ? { color: colors.putColor, width: 2.5 } : undefined
            });
        }

        // Add Net OI trace
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netOi.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netOi,
                type: 'bar',
                name: 'Net OI',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price line
        if (currentPrice) {
            traces.push({
                x: [currentPrice, currentPrice],
                y: [Math.min(...putOi), Math.max(...callOi)],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: {
                    color: colors.accentWarning,
                    width: 2.5,
                    dash: 'dash'
                }
            });
        }

        const layout = this.createChartLayout('Open Interest Distribution', 'Open Interest', currentPrice);

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('oi-chart', traces, layout, config);
    }

    renderPremiumChart(premiumData, currentPrice, viewMode = 'top10') {
        // Store data for settings updates
        this.chartData.premium = premiumData;

        // Get current settings
        const showNet = document.getElementById('premium-show-net')?.checked !== false;  // Default to true

        // Use all data (already filtered in backend based on view mode)
        const filteredData = premiumData;

        // Convert strikes to strings for categorical x-axis
        const strikes = filteredData.map(d => d.strike.toString());
        const netPremium = filteredData.map(d => d.net_premium / 1e6); // Convert to millions

        const traces = [];

        // Get CSS color variables
        const colors = this.getCSSColors();

        // Add Net Premium trace (always show as bar chart, but respect the checkbox)
        if (showNet) {
            // Create color array based on positive/negative values
            const netColors = netPremium.map(value =>
                value >= 0 ? colors.callColor.replace('rgb', 'rgba').replace(')', ', 0.8)') : colors.putColor.replace('rgb', 'rgba').replace(')', ', 0.8)')
            );

            traces.push({
                x: strikes,
                y: netPremium,
                type: 'bar', // Always use bar chart
                name: 'Net Premium',
                marker: {
                    color: netColors,  // Green for positive, red for negative
                    line: { color: colors.textPrimary, width: 0.8 }
                }
            });
        }

        // Add current price marker (find closest strike to current price)
        if (currentPrice && showNet) {
            // Find the closest strike to current price
            let closestStrike = null;
            let minDiff = Infinity;

            filteredData.forEach(d => {
                const diff = Math.abs(d.strike - currentPrice);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestStrike = d.strike.toString();
                }
            });

            if (closestStrike) {
                // Add a marker at the closest strike
                traces.push({
                    x: [closestStrike],
                    y: [0],
                    type: 'scatter',
                    mode: 'markers',
                    name: `Current Price (~${currentPrice})`,
                    marker: {
                        color: colors.accentWarning,
                        size: 12,
                        symbol: 'diamond',
                        line: {
                            color: colors.textPrimary,
                            width: 2
                        }
                    }
                });
            }
        }

        // Create custom layout for categorical x-axis
        const chartTitle = viewMode === 'top10' ? 'Top 10 Net Premium Strikes' : 'All Net Premium Strikes';
        const layout = {
            title: {
                text: `${chartTitle} (Top Player Positioning)`,
                font: { color: this.getCSSColors().textPrimary, size: 16 }
            },
            xaxis: {
                title: 'Strike Price',
                color: this.getCSSColors().textAxis,
                gridcolor: this.getCSSColors().gridColor,
                type: 'category', // Categorical x-axis
                categoryorder: 'array',
                categoryarray: strikes // Explicit order
            },
            yaxis: {
                title: 'Premium (Millions USD)',
                color: this.getCSSColors().textAxis,
                gridcolor: this.getCSSColors().gridColor
            },
            plot_bgcolor: this.getCSSColors().bgChart,
            paper_bgcolor: this.getCSSColors().bgChart,
            font: { color: this.getCSSColors().textPrimary },
            showlegend: true,
            legend: {
                font: { color: this.getCSSColors().textPrimary }
            }
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('premium-chart', traces, layout, config);
    }

    async renderCandlestickChart() {
        try {
            // Force set default values and ensure 1m interval
            const periodElement = document.getElementById('chart-period');
            const intervalElement = document.getElementById('chart-interval');

            // Force set to defaults if not set or if still showing 1h
            if (periodElement && (!periodElement.value || periodElement.value === '')) {
                periodElement.value = '5d';
            }
            if (intervalElement && (!intervalElement.value || intervalElement.value === '' || intervalElement.value === '1h')) {
                console.log('🔧 Forcing interval to 1m (was:', intervalElement.value, ')');
                intervalElement.value = '1m';
            }

            const period = periodElement?.value || '5d';
            const interval = intervalElement?.value || '1m';

            console.log(`🕐 Chart timeframe debug:`);
            console.log(`  Period element value: ${periodElement?.value}`);
            console.log(`  Interval element value: ${intervalElement?.value}`);
            console.log(`  Final period: ${period}`);
            console.log(`  Final interval: ${interval}`);
            console.log(`📊 Fetching candlestick data: ${this.currentTicker}, period: ${period}, interval: ${interval}`);

            const response = await fetch(`/api/historical-price/${this.currentTicker}?period=${period}&interval=${interval}`);
            const data = await response.json();

            console.log('Candlestick response:', data);

            if (data.success && data.candlestick_data.length > 0) {
                this.initializeTradingViewChart();
                this.updateTradingViewChart(data.candlestick_data, data.ticker, period, interval);

                // Add gamma zones if we have options data
                await this.addGammaZonesToChart();
            } else {
                this.showChartError('No historical data available for this timeframe');
            }
        } catch (error) {
            console.error('Error rendering candlestick chart:', error);
            this.showChartError(`Error: ${error.message}`);
        }
    }

    initializeTradingViewChart() {
        console.log('Initializing TradingView chart...');
        const chartContainer = document.getElementById('candlestick-chart');

        if (!chartContainer) {
            console.error('Chart container not found!');
            return;
        }

        // Check if LightweightCharts is available
        if (typeof LightweightCharts === 'undefined') {
            console.error('LightweightCharts library not loaded!');
            this.showChartError('TradingView library not loaded. Please refresh the page.');
            return;
        }

        // Clear existing chart
        if (this.candlestickChart) {
            console.log('Removing existing chart...');
            this.candlestickChart.remove();
            this.candlestickChart = null;
            this.candlestickSeries = null;
            this.volumeSeries = null;
            this.zoneFillSeries = []; // Clear zone fill series
            this.zoneOverlays = []; // Clear zone overlays
            this.priceLines = []; // Clear price lines
            this.candlestickData = null; // Clear stored data
            this.volumeData = null; // Clear stored data
            this.gammaZoneData = null; // Clear gamma zone data
        }

        // Clear container
        chartContainer.innerHTML = '';

        console.log('Creating new TradingView chart...');
        console.log('LightweightCharts object:', LightweightCharts);
        try {
            // Get CSS color variables for consistent theming
            const colors = this.getCSSColors();

            // Create new chart
            this.candlestickChart = LightweightCharts.createChart(chartContainer, {
                width: chartContainer.clientWidth,
                height: 1000,
                layout: {
                    background: { color: colors.bgChartAlt },
                    textColor: colors.textPrimary,
                    fontFamily: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
                },
                grid: {
                    vertLines: {
                        color: colors.gridColor,
                        style: LightweightCharts.LineStyle.Solid,
                        visible: true
                    },
                    horzLines: {
                        color: colors.gridColor,
                        style: LightweightCharts.LineStyle.Solid,
                        visible: true
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                    vertLine: {
                        color: colors.textAxis,
                        width: 0.8,
                        style: LightweightCharts.LineStyle.Dashed,
                        labelBackgroundColor: colors.glassBorder,
                    },
                    horzLine: {
                        color: colors.textAxis,
                        width: 0.8,
                        style: LightweightCharts.LineStyle.Dashed,
                        labelBackgroundColor: colors.glassBorder,
                    },
                },
                rightPriceScale: {
                    borderColor: colors.glassBorder,
                    textColor: colors.textAxis,
                },
                timeScale: {
                    borderColor: colors.glassBorder,
                    textColor: colors.textAxis,
                    timeVisible: true,
                    secondsVisible: false,
                },
            });

            console.log('Chart created successfully. Available methods:', Object.getOwnPropertyNames(this.candlestickChart));

            // Add candlestick series (v3.8.0 API)
            console.log('Adding candlestick series...');
            this.candlestickSeries = this.candlestickChart.addCandlestickSeries({
                upColor: colors.callColor,
                downColor: colors.putColor,
                borderDownColor: colors.putColor,
                borderUpColor: colors.callColor,
                wickDownColor: colors.putColor,
                wickUpColor: colors.callColor,
                borderVisible: true,
                wickVisible: true,
            });

            // Add volume series (v3.8.0 API)
            console.log('Adding volume series...');
            this.volumeSeries = this.candlestickChart.addHistogramSeries({
                color: colors.accentInfo,
                priceFormat: {
                    type: 'volume',
                },
                priceScaleId: 'volume',
                scaleMargins: {
                    top: 0.8,
                    bottom: 0,
                },
            });

            // Configure volume price scale (v3.8.0 API)
            this.candlestickChart.priceScale('volume').applyOptions({
                scaleMargins: {
                    top: 0.8,
                    bottom: 0,
                },
            });

            // Handle resize
            const resizeObserver = new ResizeObserver(entries => {
                if (this.candlestickChart) {
                    this.candlestickChart.applyOptions({
                        width: chartContainer.clientWidth,
                    });
                }
            });
            resizeObserver.observe(chartContainer);

            console.log('TradingView chart initialized successfully');
        } catch (error) {
            console.error('Error initializing TradingView chart:', error);
            this.showChartError(`Failed to initialize chart: ${error.message}`);
        }
    }

    updateTradingViewChart(candlestickData, ticker, period, interval) {
        if (!this.candlestickSeries || !this.volumeSeries) {
            console.error('Chart series not initialized');
            return;
        }

        // Convert data to TradingView format
        const candleData = candlestickData.map(d => ({
            time: Math.floor(new Date(d.timestamp).getTime() / 1000), // Convert to Unix timestamp
            open: d.open,
            high: d.high,
            low: d.low,
            close: d.close,
        }));

        // Get CSS color variables for volume bars
        const colors = this.getCSSColors();

        const volumeData = candlestickData.map(d => ({
            time: Math.floor(new Date(d.timestamp).getTime() / 1000),
            value: d.volume,
            color: d.close >= d.open ? colors.callColor : colors.putColor, // Green for up, red for down
        }));

        // Store data for fullscreen rendering
        this.candlestickData = candleData;
        this.volumeData = volumeData;

        // Set data
        this.candlestickSeries.setData(candleData);
        this.volumeSeries.setData(volumeData);

        // Current price line removed - TradingView shows it natively

        // Fit content
        this.candlestickChart.timeScale().fitContent();

        console.log(`TradingView chart updated with ${candleData.length} candles`);
    }

    showChartError(message) {
        const chartContainer = document.getElementById('candlestick-chart');

        // Clean up existing chart first
        this.cleanupTradingViewChart();

        chartContainer.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #ffffff; font-size: 16px; background-color: #1e1e1e; border-radius: 8px;">
                <div style="text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #ff6b6b; margin-bottom: 16px;"></i>
                    <div>${message}</div>
                </div>
            </div>
        `;
    }

    cleanupTradingViewChart() {
        if (this.candlestickChart) {
            this.candlestickChart.remove();
            this.candlestickChart = null;
            this.candlestickSeries = null;
            this.volumeSeries = null;
        }
    }

    async addGammaZonesToChart() {
        try {
            console.log('🔍 CHECKING GAMMA ZONES PREREQUISITES...');
            console.log(`Current expiry: ${this.currentExpiry}`);
            console.log(`Chart exists: ${!!this.candlestickChart}`);
            console.log(`Series exists: ${!!this.candlestickSeries}`);

            // Only add zones if we have an expiry selected and a chart
            if (!this.currentExpiry || !this.candlestickChart || !this.candlestickSeries) {
                console.log('❌ Skipping gamma zones: no expiry selected or chart not ready');
                return;
            }

            // Clear existing zone fills
            this.clearZoneFills();

            console.log(`📡 Fetching gamma exposure for zones: ${this.currentTicker}/${this.currentExpiry}`);

            // Fetch gamma exposure data
            const response = await fetch(`/api/gamma-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            console.log('📊 Gamma exposure response:', data);

            if (!data.success || !data.gex_data || data.gex_data.length === 0) {
                console.log('❌ No gamma exposure data available for zones');
                return;
            }

            console.log('🎯 Adding gamma zones to TradingView chart...');

            // Store gamma zone data for fullscreen rendering
            this.gammaZoneData = {
                gexData: data.gex_data,
                currentPrice: data.current_price
            };

            this.addGammaZones(data.gex_data, data.current_price);

        } catch (error) {
            console.error('❌ Error adding gamma zones to chart:', error);
        }
    }

    clearZoneFills() {
        try {
            // Remove all existing zone fill series
            this.zoneFillSeries.forEach(series => {
                if (this.candlestickChart && series) {
                    this.candlestickChart.removeSeries(series);
                }
            });
            this.zoneFillSeries = [];

            // Remove all price lines (gamma zones, zero GEX lines, etc.)
            this.priceLines.forEach(priceLine => {
                if (this.candlestickSeries && priceLine) {
                    try {
                        this.candlestickSeries.removePriceLine(priceLine);
                    } catch (e) {
                        console.warn('Could not remove price line:', e);
                    }
                }
            });
            this.priceLines = [];

            // Remove all CSS zone overlays
            if (this.zoneOverlays) {
                this.zoneOverlays.forEach(overlay => {
                    if (overlay && overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                });
                this.zoneOverlays = [];
            }

            console.log('✅ Cleared existing zone fills, price lines, and overlays');
        } catch (error) {
            console.error('❌ Error clearing zone fills:', error);
        }
    }

    addGammaZones(gexData, currentPrice) {
        try {
            console.log(`🚀 ADDING GAMMA ZONES - Data length: ${gexData.length}, Current price: ${currentPrice}`);

            // Calculate net gamma exposure by strike
            const netExposureByStrike = {};

            gexData.forEach(item => {
                const strike = item.strike;
                if (!netExposureByStrike[strike]) {
                    netExposureByStrike[strike] = 0;
                }
                netExposureByStrike[strike] += item.net_gex || 0;
            });

            // Convert to array and sort by absolute exposure
            const strikeExposures = Object.entries(netExposureByStrike)
                .map(([strike, exposure]) => ({
                    strike: parseFloat(strike),
                    exposure: exposure
                }))
                .sort((a, b) => Math.abs(b.exposure) - Math.abs(a.exposure));

            // Get top 3 positive and top 3 negative strikes
            const positiveStrikes = strikeExposures
                .filter(item => item.exposure > 0)
                .slice(0, 3)
                .map(item => item.strike);

            const negativeStrikes = strikeExposures
                .filter(item => item.exposure < 0)
                .slice(0, 3)
                .map(item => item.strike);

            console.log('📈 Top positive strikes (resistance):', positiveStrikes);
            console.log('📉 Top negative strikes (support):', negativeStrikes);

            // Add positive zones (green - resistance levels)
            console.log('🟢 Adding positive zones...');
            positiveStrikes.forEach((strike, index) => {
                this.addGammaZone(strike, 'green', index + 1, 'Resistance');
            });

            // Add negative zones (red - support levels)
            console.log('🔴 Adding negative zones...');
            negativeStrikes.forEach((strike, index) => {
                this.addGammaZone(strike, 'red', index + 1, 'Support');
            });

            // Calculate and add zero GEX level
            const zeroGexLevel = this.calculateZeroGexLevel(strikeExposures, currentPrice);
            if (zeroGexLevel) {
                console.log('🟡 Adding zero GEX line...');
                this.addZeroGexLine(zeroGexLevel);
            }

            console.log(`✅ Finished adding gamma zones. Total zone fills created: ${this.zoneFillSeries.length}`);

        } catch (error) {
            console.error('❌ Error adding gamma zones:', error);
        }
    }

    addGammaZone(strike, color, rank, type) {
        try {
            console.log(`🎯 ADDING GAMMA ZONE: ${type} ${rank} at strike ${strike} with color ${color}`);

            // Calculate zone boundaries (±0.08% around strike)
            const upperLevel = strike * 1.0008;  // +0.08%
            const lowerLevel = strike * 0.9992;  // -0.08%

            // Set transparency levels to match CSS variables: --zone-alpha-1, --zone-alpha-2, --zone-alpha-3
            const colors = this.getCSSColors();
            const transparencyLevels = [colors.zoneAlpha1, colors.zoneAlpha2, colors.zoneAlpha3];
            const alpha = transparencyLevels[rank - 1] || colors.zoneAlpha3;

            console.log(`Zone boundaries: ${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)}, alpha: ${alpha}`);

            // Create zone fill using area series
            this.addZoneFill(upperLevel, lowerLevel, color, alpha, rank, type);

            // Main strike line (solid)
            const mainLine = this.candlestickSeries.createPriceLine({
                price: strike,
                color: color,
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: false,
                title: '',
            });
            this.priceLines.push(mainLine);

            // Upper zone boundary (dashed)
            const upperLine = this.candlestickSeries.createPriceLine({
                price: upperLevel,
                color: color,
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: false,
                title: '',
            });
            this.priceLines.push(upperLine);

            // Lower zone boundary (dashed)
            const lowerLine = this.candlestickSeries.createPriceLine({
                price: lowerLevel,
                color: color,
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: false,
                title: '',
            });
            this.priceLines.push(lowerLine);

            console.log(`✅ Successfully added ${color} gamma zone ${rank} with fill: ${strike.toFixed(2)} (${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)})`);

        } catch (error) {
            console.error(`❌ Error adding gamma zone for strike ${strike}:`, error);
        }
    }

    addZoneFill(upperLevel, lowerLevel, color, alpha, rank, type) {
        try {
            // For now, let's skip the zone fill and just add a visible marker
            console.log(`Zone fill requested for ${type} ${rank}: ${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)}`);

            // Add a simple line series to test visibility
            this.addVisibleZoneMarker(upperLevel, lowerLevel, color, alpha, rank, type);

        } catch (error) {
            console.error('Error adding zone fill:', error);
        }
    }

    addVisibleZoneMarker(upperLevel, lowerLevel, color, alpha, rank, type) {
        try {
            console.log(`🎨 Creating SOLID zone fill for ${type} ${rank}: ${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)}`);

            // Use a proper area series approach for solid fills
            this.createAreaSeriesZoneFill(upperLevel, lowerLevel, color, alpha, rank, type);

        } catch (error) {
            console.error('❌ Error adding solid zone fill:', error);
        }
    }

    createAreaSeriesZoneFill(upperLevel, lowerLevel, color, alpha, rank, type) {
        try {
            // Use CSS color variables for consistent theming
            const colors = this.getCSSColors();

            // Convert hex to RGB for zone fills
            const hexToRgb = (hex) => {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16)
                } : { r: 128, g: 128, b: 128 };
            };

            const colorMap = {
                'green': hexToRgb(colors.callColor),    // Call color for positive zones
                'red': hexToRgb(colors.putColor),       // Put color for negative zones
                'yellow': hexToRgb(colors.accentWarning) // Warning color for zero GEX
            };

            const rgbColor = colorMap[color] || { r: 128, g: 128, b: 128 };
            const fillColor = `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, ${alpha})`;

            console.log(`Creating solid zone fill with color: ${fillColor} (alpha: ${alpha} for rank ${rank})`);

            // Create a baseline series that fills between the upper and lower levels
            // This creates a solid color fill matching the desktop dashboard aesthetics
            const zoneFillSeries = this.candlestickChart.addBaselineSeries({
                baseValue: { type: 'price', price: lowerLevel },
                topLineColor: 'transparent',
                bottomLineColor: 'transparent',
                topFillColor1: fillColor,
                topFillColor2: fillColor,
                bottomFillColor1: 'transparent',
                bottomFillColor2: 'transparent',
                lineWidth: 0,
                priceLineVisible: false,
                lastValueVisible: false,
                crosshairMarkerVisible: false,
                title: ''
            });

            // Create time data that spans the chart
            const currentTime = Math.floor(Date.now() / 1000);
            const timePoints = [];

            // Create data points every hour for 30 days
            for (let i = -30; i <= 30; i++) {
                for (let h = 0; h < 24; h++) {
                    timePoints.push(currentTime + (i * 24 * 3600) + (h * 3600));
                }
            }

            // Create data at the upper level (baseline will fill down to lowerLevel)
            const fillData = timePoints.map(time => ({
                time: time,
                value: upperLevel
            }));

            zoneFillSeries.setData(fillData);
            this.zoneFillSeries.push(zoneFillSeries);

            console.log(`✅ Created baseline zone fill with ${fillData.length} data points`);

        } catch (error) {
            console.error('❌ Error creating area series zone fill:', error);
        }
    }

    createSolidZoneFill(upperLevel, lowerLevel, color, alpha, rank, type) {
        try {
            const colorMap = {
                'green': { r: 0, g: 255, b: 0 },
                'red': { r: 255, g: 0, b: 0 },
                'yellow': { r: 255, g: 255, b: 0 }
            };

            const rgbColor = colorMap[color] || { r: 128, g: 128, b: 128 };
            const fillColor = `rgba(${rgbColor.r}, ${rgbColor.g}, ${rgbColor.b}, 0.3)`; // 30% opacity for visibility

            // Get the chart container
            const chartContainer = document.getElementById('candlestick-chart');
            if (!chartContainer) {
                console.error('Chart container not found');
                return;
            }

            // Create a div overlay for the zone fill
            const zoneOverlay = document.createElement('div');
            zoneOverlay.className = `gamma-zone-fill zone-${rank}`;
            zoneOverlay.style.cssText = `
                position: absolute;
                left: 0;
                right: 0;
                background-color: ${fillColor};
                pointer-events: none;
                z-index: 1;
                border-top: 1px dashed ${color};
                border-bottom: 1px dashed ${color};
            `;

            // Calculate the position based on price levels
            this.positionZoneOverlay(zoneOverlay, upperLevel, lowerLevel, chartContainer);

            // Add to container
            chartContainer.style.position = 'relative';
            chartContainer.appendChild(zoneOverlay);

            // Store reference for cleanup
            if (!this.zoneOverlays) {
                this.zoneOverlays = [];
            }
            this.zoneOverlays.push(zoneOverlay);

            console.log(`✅ Created solid zone overlay for ${type} ${rank} with color ${fillColor}`);

        } catch (error) {
            console.error('❌ Error creating solid zone fill:', error);
        }
    }

    positionZoneOverlay(overlay, upperLevel, lowerLevel, chartContainer) {
        try {
            // Get chart dimensions
            const chartHeight = chartContainer.clientHeight;
            const chartWidth = chartContainer.clientWidth;

            // Get price scale to convert price to pixel coordinates
            const priceScale = this.candlestickChart.priceScale('right');

            // Convert price levels to pixel coordinates
            const upperPixel = priceScale.priceToCoordinate(upperLevel);
            const lowerPixel = priceScale.priceToCoordinate(lowerLevel);

            if (upperPixel !== null && lowerPixel !== null) {
                const topPosition = Math.min(upperPixel, lowerPixel);
                const height = Math.abs(lowerPixel - upperPixel);

                overlay.style.top = `${topPosition}px`;
                overlay.style.height = `${height}px`;
                overlay.style.width = '100%';

                console.log(`Zone positioned: top=${topPosition}px, height=${height}px`);
            } else {
                // Fallback: use percentage-based positioning
                const priceRange = this.getCurrentPriceRange();
                if (priceRange) {
                    const totalRange = priceRange.max - priceRange.min;
                    const upperPercent = ((priceRange.max - upperLevel) / totalRange) * 100;
                    const lowerPercent = ((priceRange.max - lowerLevel) / totalRange) * 100;

                    overlay.style.top = `${upperPercent}%`;
                    overlay.style.height = `${Math.abs(lowerPercent - upperPercent)}%`;
                    overlay.style.width = '100%';

                    console.log(`Zone positioned (fallback): top=${upperPercent}%, height=${Math.abs(lowerPercent - upperPercent)}%`);
                }
            }

        } catch (error) {
            console.error('❌ Error positioning zone overlay:', error);
        }
    }

    getCurrentPriceRange() {
        try {
            const priceScale = this.candlestickChart.priceScale('right');
            const visibleRange = priceScale.getVisibleLogicalRange();

            if (visibleRange) {
                return {
                    min: visibleRange.from,
                    max: visibleRange.to
                };
            }
            return null;
        } catch (error) {
            console.error('Error getting price range:', error);
            return null;
        }
    }

    addTestElement() {
        try {
            console.log('🧪 Adding test element to chart...');

            if (!this.candlestickChart) {
                console.error('❌ No chart available for test element');
                return;
            }

            // Add a very obvious bright yellow line
            const testSeries = this.candlestickChart.addLineSeries({
                color: '#FFFF00',
                lineWidth: 8,
                lineStyle: LightweightCharts.LineStyle.Solid,
                priceLineVisible: false,
                lastValueVisible: false,
                crosshairMarkerVisible: true,
                title: 'TEST ELEMENT'
            });

            // Use current price as reference
            const testPrice = this.currentPrice || 500;
            const currentTime = Math.floor(Date.now() / 1000);

            const testData = [
                { time: currentTime - 86400, value: testPrice + 5 }, // 1 day ago, $5 above current
                { time: currentTime + 86400, value: testPrice + 5 }  // 1 day from now, $5 above current
            ];

            testSeries.setData(testData);
            this.zoneFillSeries.push(testSeries);

            console.log(`✅ Test element added at price ${testPrice + 5} with ${testData.length} data points`);

        } catch (error) {
            console.error('❌ Error adding test element:', error);
        }
    }

    calculateZeroGexLevel(strikeExposures, currentPrice) {
        try {
            // Find the strike closest to current price where net GEX changes sign
            // This is a simplified calculation - in reality it would interpolate between strikes

            if (!currentPrice || strikeExposures.length === 0) {
                return null;
            }

            // Sort strikes by proximity to current price
            const sortedByPrice = strikeExposures
                .slice()
                .sort((a, b) => Math.abs(a.strike - currentPrice) - Math.abs(b.strike - currentPrice));

            // Look for sign changes around current price
            for (let i = 0; i < sortedByPrice.length - 1; i++) {
                const current = sortedByPrice[i];
                const next = sortedByPrice[i + 1];

                // Check if there's a sign change
                if ((current.exposure > 0 && next.exposure < 0) ||
                    (current.exposure < 0 && next.exposure > 0)) {

                    // Simple interpolation between the two strikes
                    const zeroLevel = (current.strike + next.strike) / 2;
                    console.log(`Calculated zero GEX level: ${zeroLevel.toFixed(2)}`);
                    return zeroLevel;
                }
            }

            // If no clear zero crossing found, estimate based on current price
            const estimatedZero = currentPrice * 0.998; // Slightly below current price
            console.log(`Estimated zero GEX level: ${estimatedZero.toFixed(2)}`);
            return estimatedZero;

        } catch (error) {
            console.error('Error calculating zero GEX level:', error);
            return null;
        }
    }

    addZeroGexLine(zeroGexLevel) {
        try {
            // Add zero GEX line (purple dashed)
            const zeroLine = this.candlestickSeries.createPriceLine({
                price: zeroGexLevel,
                color: '#8b5cf6',
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: false,
                title: '',
            });
            this.priceLines.push(zeroLine);

            console.log(`Added zero GEX line at: ${zeroGexLevel.toFixed(2)}`);

        } catch (error) {
            console.error('Error adding zero GEX line:', error);
        }
    }

    async updateGammaRegime() {
        console.log('🔍 updateGammaRegime called');
        console.log('Current ticker:', this.currentTicker);
        console.log('Current expiry:', this.currentExpiry);
        console.log('Current price:', this.currentPrice);

        if (!this.currentTicker || !this.currentExpiry || !this.currentPrice) {
            console.log('❌ Missing required data for gamma regime');
            this.setGammaRegimeDisplay('Loading...', 'loading');
            return;
        }

        try {
            // Fetch gamma exposure data
            const response = await fetch(`/api/gamma-exposure/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success && data.gex_data && data.gex_data.length > 0) {
                const regime = this.calculateGammaRegime(data.gex_data, data.current_price);
                this.setGammaRegimeDisplay(regime.text, regime.class);
            } else {
                this.setGammaRegimeDisplay('No Data', 'loading');
            }
        } catch (error) {
            console.error('Error updating gamma regime:', error);
            this.setGammaRegimeDisplay('Error', 'loading');
        }
    }

    calculateGammaRegime(gexData, currentPrice) {
        try {
            // Calculate net gamma exposure by strike
            const netExposureByStrike = {};
            gexData.forEach(item => {
                const strike = item.strike;
                if (!netExposureByStrike[strike]) {
                    netExposureByStrike[strike] = 0;
                }
                netExposureByStrike[strike] += item.net_gex || 0;
            });

            // Convert to array and sort by strike
            const strikeExposures = Object.entries(netExposureByStrike)
                .map(([strike, exposure]) => ({
                    strike: parseFloat(strike),
                    exposure: exposure
                }))
                .sort((a, b) => a.strike - b.strike);

            // Calculate zero GEX level
            const zeroGexLevel = this.calculateZeroGexLevel(strikeExposures, currentPrice);

            // Calculate total positive and negative gamma
            const totalPositiveGamma = strikeExposures
                .filter(item => item.exposure > 0)
                .reduce((sum, item) => sum + item.exposure, 0);

            const totalNegativeGamma = Math.abs(strikeExposures
                .filter(item => item.exposure < 0)
                .reduce((sum, item) => sum + item.exposure, 0));

            // Calculate gamma strength ratio
            const gammaRatio = totalPositiveGamma / (totalNegativeGamma || 1);

            // Determine regime based on current price relative to zero GEX and gamma strength
            if (!zeroGexLevel) {
                return { text: 'Unknown', class: 'loading' };
            }

            const priceDistanceFromZero = ((currentPrice - zeroGexLevel) / zeroGexLevel) * 100;

            // Define thresholds
            const deepThreshold = 2.0; // 2% from zero GEX level
            const strongGammaThreshold = 2.0; // Gamma ratio threshold for "deep" regimes

            if (currentPrice > zeroGexLevel) {
                // Above zero GEX = Positive gamma regime
                if (priceDistanceFromZero > deepThreshold && gammaRatio > strongGammaThreshold) {
                    return { text: 'Deep Positive Gamma', class: 'deep-positive' };
                } else {
                    return { text: 'Positive Gamma', class: 'positive' };
                }
            } else {
                // Below zero GEX = Negative gamma regime
                if (Math.abs(priceDistanceFromZero) > deepThreshold && (1/gammaRatio) > strongGammaThreshold) {
                    return { text: 'Deep Negative Gamma', class: 'deep-negative' };
                } else {
                    return { text: 'Negative Gamma', class: 'negative' };
                }
            }

        } catch (error) {
            console.error('Error calculating gamma regime:', error);
            return { text: 'Error', class: 'loading' };
        }
    }

    setGammaRegimeDisplay(text, regimeClass) {
        const regimeElement = document.getElementById('gamma-regime-text');
        if (regimeElement) {
            regimeElement.textContent = text;

            // Remove all regime classes
            regimeElement.classList.remove(
                'gamma-regime-loading',
                'gamma-regime-deep-positive',
                'gamma-regime-positive',
                'gamma-regime-negative',
                'gamma-regime-deep-negative'
            );

            // Add the appropriate class
            regimeElement.classList.add(`gamma-regime-${regimeClass}`);

            console.log(`Gamma regime updated: ${text} (${regimeClass})`);
        }
    }



    startAutoRefresh() {
        this.autoRefreshInterval = setInterval(() => {
            console.log('🔄 Auto-refresh triggered - refreshing all charts...');
            this.fetchOptionsData(); // This now calls refreshAllCharts()
        }, 15000); // 15 seconds
    }
    
    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    startSignalsAutoRefresh() {
        this.stopSignalsAutoRefresh(); // Clear any existing interval
        this.signalsAutoRefreshInterval = setInterval(() => {
            this.fetchSignals();
        }, 15000); // 15 seconds
        console.log('Signals auto refresh started (15s interval)');
    }

    stopSignalsAutoRefresh() {
        if (this.signalsAutoRefreshInterval) {
            clearInterval(this.signalsAutoRefreshInterval);
            this.signalsAutoRefreshInterval = null;
            console.log('Signals auto refresh stopped');
        }
    }

    async fetchSignals() {
        if (!this.currentTicker || !this.currentExpiry) {
            console.log('No ticker or expiry selected for signals');
            this.showSignalsPlaceholder();
            return;
        }

        try {
            console.log(`Fetching signals for ${this.currentTicker} ${this.currentExpiry}`);

            // Show loading state
            this.showSignalsLoading();

            const response = await fetch(`/api/signals/${this.currentTicker}/${this.currentExpiry}`);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                console.log('✅ Signals data received:', data);
                this.renderSignals(data);

                // Update market regime display
                this.updateMarketRegime(data);

                // Clear any previous error state
                this.signalsErrorCount = 0;

                // Start auto-refresh if enabled
                const autoRefreshEnabled = document.getElementById('signals-auto-refresh')?.checked;
                if (autoRefreshEnabled) {
                    this.startSignalsAutoRefresh();
                }
            } else {
                console.error('❌ Failed to fetch signals:', data.error);
                this.handleSignalsError(data.error || 'Unknown server error');
            }
        } catch (error) {
            console.error('❌ Error fetching signals:', error);
            this.handleSignalsError(error.message || 'Failed to fetch signals data');
        }
    }

    handleSignalsError(errorMessage) {
        // Track consecutive errors
        this.signalsErrorCount = (this.signalsErrorCount || 0) + 1;

        // Stop auto-refresh after multiple consecutive errors
        if (this.signalsErrorCount >= 3) {
            console.warn('Multiple signal fetch errors, stopping auto-refresh');
            this.stopSignalsAutoRefresh();
            const autoRefreshCheckbox = document.getElementById('signals-auto-refresh');
            if (autoRefreshCheckbox) {
                autoRefreshCheckbox.checked = false;
            }
        }

        this.showSignalsError(errorMessage);
    }

    renderSignals(data) {
        const signalsList = document.getElementById('signals-list');
        const activeSignalsCount = document.getElementById('active-signals-count');

        if (!signalsList) {
            console.error('signals-list element not found');
            return;
        }

        // Validate input data structure
        if (!data || typeof data !== 'object') {
            console.error('Invalid signals data received:', data);
            this.showSignalsError('Invalid data format received');
            return;
        }

        // Add comprehensive safety checks for data structure
        const signals = data.signals || {};
        const currentPrice = data.current_price || 0;

        // Validate current price
        if (typeof currentPrice !== 'number' || currentPrice <= 0) {
            console.warn('Invalid current price:', currentPrice);
        }

        // Ensure required properties exist with robust defaults
        const bias = this.validateBiasData(signals.bias);
        const confidence = this.validateConfidenceData(signals.confidence);
        const signal = this.validateSignalData(signals.signal);

        // Count active signals safely
        let activeCount = 0;
        if (signal.action && signal.action !== 'HOLD') activeCount = 1;

        // Update active signals count
        if (activeSignalsCount) {
            activeSignalsCount.textContent = activeCount;
        }

        // Build signals HTML with error handling
        let signalsHtml = '';

        try {
            // Main signal section
            signalsHtml += this.renderMainSignalSection(signal, currentPrice);

            // Bias section
            signalsHtml += this.renderBiasSection(bias, confidence);

            // Market context section
            signalsHtml += this.renderMarketContextSection(signals, currentPrice);

            signalsList.innerHTML = signalsHtml;
        } catch (error) {
            console.error('Error rendering signals:', error);
            this.showSignalsError('Error displaying signals data: ' + error.message);
        }
    }

    renderBiasSection(bias, confidence) {
        const biasColor = bias.direction === 'BULLISH' ? 'success' :
                         bias.direction === 'BEARISH' ? 'danger' : 'warning';

        const confidenceColor = confidence.level === 'HIGH' ? 'success' :
                               confidence.level === 'MEDIUM' ? 'warning' : 'danger';

        return `
            <div class="signal-section mb-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="fas fa-compass me-2"></i>Market Bias
                    </h6>
                    <span class="badge bg-${confidenceColor}">${confidence.level} Confidence (${confidence.score}%)</span>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="signal-card bg-${biasColor} bg-opacity-10 border border-${biasColor} p-3 rounded">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-arrow-${bias.direction === 'BULLISH' ? 'up' : bias.direction === 'BEARISH' ? 'down' : 'right'} text-${biasColor} me-2"></i>
                                <strong class="text-${biasColor}">${bias.direction}</strong>
                                <span class="ms-2 text-muted">(${bias.strength}% strength)</span>
                            </div>
                            <small class="text-muted">${bias.reason}</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="signal-factors">
                            <small class="text-muted d-block mb-1">Supporting Factors:</small>
                            ${(bias.factors || []).map(factor => `<small class="d-block text-light">• ${factor}</small>`).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPriceTargetsSection(targets, currentPrice) {
        let targetsHtml = `
            <div class="signal-section mb-4">
                <h6 class="mb-3">
                    <i class="fas fa-bullseye me-2"></i>Price Targets
                </h6>
                <div class="row">
        `;

        // Long target
        if (targets.long_target) {
            const longDistance = ((targets.long_target - currentPrice) / currentPrice * 100).toFixed(2);
            const longHit = currentPrice >= targets.long_target;

            targetsHtml += `
                <div class="col-md-6 mb-3">
                    <div class="signal-card bg-success bg-opacity-10 border border-success p-3 rounded">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <strong class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>Long Target
                            </strong>
                            <span class="badge bg-${longHit ? 'success' : 'secondary'}">${longHit ? 'HIT' : 'ACTIVE'}</span>
                        </div>
                        <div class="target-price mb-2">
                            <span class="h5 text-success">$${targets.long_target.toFixed(2)}</span>
                            <small class="text-muted ms-2">(+${longDistance}%)</small>
                        </div>
                        ${targets.long_stop ? `<small class="text-muted">Stop: $${targets.long_stop.toFixed(2)}</small>` : ''}
                    </div>
                </div>
            `;
        }

        // Short target
        if (targets.short_target) {
            const shortDistance = ((targets.short_target - currentPrice) / currentPrice * 100).toFixed(2);
            const shortHit = currentPrice <= targets.short_target;

            targetsHtml += `
                <div class="col-md-6 mb-3">
                    <div class="signal-card bg-danger bg-opacity-10 border border-danger p-3 rounded">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <strong class="text-danger">
                                <i class="fas fa-arrow-down me-1"></i>Short Target
                            </strong>
                            <span class="badge bg-${shortHit ? 'success' : 'secondary'}">${shortHit ? 'HIT' : 'ACTIVE'}</span>
                        </div>
                        <div class="target-price mb-2">
                            <span class="h5 text-danger">$${targets.short_target.toFixed(2)}</span>
                            <small class="text-muted ms-2">(${shortDistance}%)</small>
                        </div>
                        ${targets.short_stop ? `<small class="text-muted">Stop: $${targets.short_stop.toFixed(2)}</small>` : ''}
                    </div>
                </div>
            `;
        }

        targetsHtml += `
                </div>
                ${(targets.target_reasoning || []).length > 0 ? `
                    <div class="mt-2">
                        <small class="text-muted d-block mb-1">Target Reasoning:</small>
                        ${(targets.target_reasoning || []).map(reason => `<small class="d-block text-light">• ${reason}</small>`).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        return targetsHtml;
    }

    renderMarketContextSection(signals, currentPrice) {
        // Add safety checks for nested properties
        const signalStrength = signals.signal_strength || 0;
        const marketRegime = signals.market_regime || 'UNKNOWN';
        const gammaLevels = signals.gamma_levels || {};
        const zeroGex = gammaLevels.zero_gex;

        return `
            <div class="signal-section mb-4">
                <h6 class="mb-3">
                    <i class="fas fa-chart-area me-2"></i>Market Context
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="context-item">
                            <label class="text-muted">Signal Strength:</label>
                            <div class="progress mt-1" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: ${signalStrength}%"></div>
                            </div>
                            <small class="text-muted">${signalStrength.toFixed(0)}%</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="context-item">
                            <label class="text-muted">Market Regime:</label>
                            <span class="d-block text-light">${this.formatMarketRegime(marketRegime)}</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="context-item">
                            <label class="text-muted">Zero GEX Level:</label>
                            <span class="d-block text-light">
                                ${zeroGex ? `$${zeroGex.toFixed(2)}` : 'N/A'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    formatMarketRegime(regime) {
        const regimeMap = {
            'STRONG_POSITIVE_GAMMA': 'Strong Positive Gamma',
            'POSITIVE_GAMMA': 'Positive Gamma',
            'NEGATIVE_GAMMA': 'Negative Gamma',
            'STRONG_NEGATIVE_GAMMA': 'Strong Negative Gamma',
            'UNKNOWN': 'Unknown'
        };
        return regimeMap[regime] || regime;
    }

    // Data validation helper functions
    validateBiasData(bias) {
        if (!bias || typeof bias !== 'object') {
            return { direction: 'NEUTRAL', strength: 0, factors: [], reason: 'No data available' };
        }

        return {
            direction: bias.direction || 'NEUTRAL',
            strength: typeof bias.strength === 'number' ? bias.strength : 0,
            factors: Array.isArray(bias.factors) ? bias.factors : [],
            reason: bias.reason || 'No analysis available'
        };
    }

    validateConfidenceData(confidence) {
        if (!confidence || typeof confidence !== 'object') {
            return { level: 'LOW', score: 0, factors: [] };
        }

        return {
            level: confidence.level || 'LOW',
            score: typeof confidence.score === 'number' ? confidence.score : 0,
            factors: Array.isArray(confidence.factors) ? confidence.factors : []
        };
    }

    validatePriceTargetsData(priceTargets) {
        if (!priceTargets || typeof priceTargets !== 'object') {
            return { long_target: null, short_target: null, target_reasoning: [] };
        }

        return {
            long_target: typeof priceTargets.long_target === 'number' ? priceTargets.long_target : null,
            short_target: typeof priceTargets.short_target === 'number' ? priceTargets.short_target : null,
            long_stop: typeof priceTargets.long_stop === 'number' ? priceTargets.long_stop : null,
            short_stop: typeof priceTargets.short_stop === 'number' ? priceTargets.short_stop : null,
            target_reasoning: Array.isArray(priceTargets.target_reasoning) ? priceTargets.target_reasoning : []
        };
    }

    validateSignalData(signal) {
        if (!signal || typeof signal !== 'object') {
            return { action: 'HOLD', reason: 'No signal data available', confidence_level: 'LOW' };
        }

        return {
            action: signal.action || 'HOLD',
            reason: signal.reason || 'No analysis available',
            confidence_level: signal.confidence_level || 'LOW',
            bias_strength: typeof signal.bias_strength === 'number' ? signal.bias_strength : 0,
            confidence_score: typeof signal.confidence_score === 'number' ? signal.confidence_score : 0
        };
    }

    renderMainSignalSection(signal, currentPrice) {
        const actionColor = this.getSignalActionColor(signal.action);
        const actionIcon = this.getSignalActionIcon(signal.action);

        return `
            <div class="signal-card mb-3">
                <div class="signal-header">
                    <h5 class="signal-title">
                        <i class="fas ${actionIcon} me-2"></i>
                        Gamma Signal
                    </h5>
                    <span class="badge signal-action-badge bg-${actionColor} fs-6">
                        ${signal.action}
                    </span>
                </div>
                <div class="signal-content">
                    <div class="signal-main-action">
                        <div class="action-display ${actionColor}">
                            <i class="fas ${actionIcon} fa-2x"></i>
                            <div class="action-text">
                                <div class="action-label">${signal.action}</div>
                                <div class="action-price">@ $${currentPrice.toFixed(2)}</div>
                            </div>
                        </div>
                    </div>
                    <div class="signal-details mt-3">
                        <div class="signal-metric">
                            <div class="signal-metric-label">Confidence</div>
                            <div class="signal-metric-value">${signal.confidence_level}</div>
                        </div>
                        <div class="signal-metric">
                            <div class="signal-metric-label">Bias Strength</div>
                            <div class="signal-metric-value">${signal.bias_strength}%</div>
                        </div>
                    </div>
                    <div class="signal-reasoning mt-2">
                        <small class="text-muted">${signal.reason}</small>
                    </div>
                </div>
            </div>
        `;
    }

    getSignalActionColor(action) {
        switch(action) {
            case 'BUY': return 'success';
            case 'SELL': return 'danger';
            case 'HOLD': return 'secondary';
            default: return 'secondary';
        }
    }

    getSignalActionIcon(action) {
        switch(action) {
            case 'BUY': return 'fa-arrow-up';
            case 'SELL': return 'fa-arrow-down';
            case 'HOLD': return 'fa-pause';
            default: return 'fa-question';
        }
    }

    updateMarketRegime(data) {
        // Update the market regime display in the signals tab
        const gammaRegimeElement = document.getElementById('signal-gamma-regime');
        const zeroGexElement = document.getElementById('signal-zero-gex');
        const currentPriceElement = document.getElementById('signal-current-price');

        // Add comprehensive safety checks for data structure
        if (!data || typeof data !== 'object') {
            console.error('Invalid data for market regime update:', data);
            return;
        }

        const signals = data.signals || {};
        const gammaLevels = signals.gamma_levels || {};
        const marketRegime = signals.market_regime || 'UNKNOWN';
        const zeroGex = gammaLevels.zero_gex;
        const currentPrice = data.current_price || 0;

        if (gammaRegimeElement) {
            try {
                gammaRegimeElement.textContent = this.formatMarketRegime(marketRegime);
            } catch (error) {
                console.error('Error updating gamma regime:', error);
                gammaRegimeElement.textContent = 'Error';
            }
        }

        if (zeroGexElement) {
            try {
                zeroGexElement.textContent = (zeroGex && typeof zeroGex === 'number') ? `$${zeroGex.toFixed(2)}` : 'N/A';
            } catch (error) {
                console.error('Error updating zero GEX:', error);
                zeroGexElement.textContent = 'N/A';
            }
        }

        if (currentPriceElement) {
            try {
                currentPriceElement.textContent = (currentPrice && typeof currentPrice === 'number') ?
                    `$${currentPrice.toFixed(2)}` : 'N/A';
            } catch (error) {
                console.error('Error updating current price:', error);
                currentPriceElement.textContent = 'N/A';
            }
        }
    }

    showSignalsLoading() {
        const signalsList = document.getElementById('signals-list');
        if (signalsList) {
            signalsList.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Calculating signals...</p>
                </div>
            `;
        }
    }

    showSignalsError(error) {
        const signalsList = document.getElementById('signals-list');
        if (signalsList) {
            // Sanitize error message to prevent XSS
            const sanitizedError = String(error || 'Unknown error').replace(/[<>]/g, '');
            signalsList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <p class="text-warning">Error loading signals</p>
                    <small class="text-muted">${sanitizedError}</small>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="dashboard.fetchSignals()">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    </div>
                </div>
            `;
        }
    }

    showSignalsPlaceholder() {
        const signalsList = document.getElementById('signals-list');
        if (signalsList) {
            signalsList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p>Select a ticker and expiry to monitor entry signals</p>
                </div>
            `;
        }
    }

    initializeSignalsSubtabs() {
        // Initialize subtab event listeners
        const subtabButtons = document.querySelectorAll('#signals-subtabs .nav-link');
        subtabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const targetTab = e.target.getAttribute('data-bs-target');
                this.handleSignalsSubtabChange(targetTab);
            });
        });
    }

    handleSignalsSubtabChange(targetTab) {
        console.log('Signals subtab changed to:', targetTab);

        switch(targetTab) {
            case '#intraday-signals':
                this.fetchSignals();
                break;
            case '#eod-vanna-signals':
                this.fetchEODVannaSignals();
                break;
            case '#eod-charm-signals':
                this.fetchEODCharmSignals();
                break;
        }
    }

    async fetchEODVannaSignals() {
        if (!this.currentTicker || !this.currentExpiry) {
            console.log('No ticker or expiry selected for EOD vanna signals');
            this.showVannaSignalsPlaceholder();
            return;
        }

        try {
            console.log(`Fetching EOD vanna signals for ${this.currentTicker} ${this.currentExpiry}`);

            // Show loading state
            this.showVannaSignalsLoading();

            const response = await fetch(`/api/eod-vanna-signals/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                console.log('✅ EOD Vanna signals data received:', data);
                this.renderVannaSignals(data);
            } else {
                console.error('❌ Failed to fetch EOD vanna signals:', data.error);
                this.showVannaSignalsError(data.error);
            }
        } catch (error) {
            console.error('❌ Error fetching EOD vanna signals:', error);
            this.showVannaSignalsError('Failed to fetch EOD vanna signals data');
        }
    }

    async fetchEODCharmSignals() {
        if (!this.currentTicker || !this.currentExpiry) {
            console.log('No ticker or expiry selected for EOD charm signals');
            this.showCharmSignalsPlaceholder();
            return;
        }

        try {
            console.log(`Fetching EOD charm signals for ${this.currentTicker} ${this.currentExpiry}`);

            // Show loading state
            this.showCharmSignalsLoading();

            const response = await fetch(`/api/eod-charm-signals/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                console.log('✅ EOD Charm signals data received:', data);
                this.renderCharmSignals(data);
            } else {
                console.error('❌ Failed to fetch EOD charm signals:', data.error);
                this.showCharmSignalsError(data.error);
            }
        } catch (error) {
            console.error('❌ Error fetching EOD charm signals:', error);
            this.showCharmSignalsError('Failed to fetch EOD charm signals data');
        }
    }

    // Vanna Signals Methods
    renderVannaSignals(data) {
        const vannaSignalsList = document.getElementById('vanna-signals-list');
        const activeVannaCount = document.getElementById('active-vanna-signals-count');

        if (!vannaSignalsList) return;

        const signals = data.vanna_signals || {};
        const currentPrice = data.current_price || 0;

        // Update active signals count
        const activeCount = (signals.entry_signals || []).length;
        if (activeVannaCount) {
            activeVannaCount.textContent = activeCount;
        }

        // Update vanna regime display
        this.updateVannaRegimeDisplay(signals);

        // Build vanna signals HTML
        let signalsHtml = this.renderVannaAnalysisSection(signals, currentPrice);
        signalsHtml += this.renderVannaEntrySignals(signals.entry_signals || [], currentPrice);

        vannaSignalsList.innerHTML = signalsHtml;
    }

    renderVannaAnalysisSection(signals, currentPrice) {
        return `
            <div class="eod-signal-card mb-3">
                <div class="eod-signal-header">
                    <span class="eod-signal-title">Vanna Exposure Analysis</span>
                    <span class="badge eod-signal-badge bg-warning">${signals.regime || 'UNKNOWN'}</span>
                </div>
                <div class="eod-signal-content">
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Vol Sensitivity</div>
                        <div class="eod-signal-metric-value">${(signals.volatility_sensitivity || 0).toFixed(1)}%</div>
                    </div>
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Signal Strength</div>
                        <div class="eod-signal-metric-value">${(signals.signal_strength || 0).toFixed(0)}%</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderVannaEntrySignals(entrySignals, currentPrice) {
        if (!entrySignals || entrySignals.length === 0) {
            return `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-wave-square fa-2x mb-2"></i>
                    <p>No EOD vanna signals detected</p>
                </div>
            `;
        }

        return entrySignals.map(signal => `
            <div class="eod-signal-card mb-3">
                <div class="eod-signal-header">
                    <span class="eod-signal-title">${this.formatSignalType(signal.type)}</span>
                    <span class="badge eod-signal-badge bg-${this.getSignalColor(signal.direction)}">${signal.direction}</span>
                </div>
                <div class="eod-signal-content">
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Confidence</div>
                        <div class="eod-signal-metric-value">${signal.confidence}%</div>
                    </div>
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Time Horizon</div>
                        <div class="eod-signal-metric-value">${this.formatTimeHorizon(signal.time_horizon)}</div>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">${signal.reasoning}</small>
                </div>
            </div>
        `).join('');
    }

    updateVannaRegimeDisplay(signals) {
        const vannaRegime = document.getElementById('vanna-regime');
        const vannaVolSensitivity = document.getElementById('vanna-vol-sensitivity');
        const vannaCrossRisk = document.getElementById('vanna-cross-risk');
        const vannaOvernightRisk = document.getElementById('vanna-overnight-risk');

        if (vannaRegime) vannaRegime.textContent = signals.regime || 'Unknown';
        if (vannaVolSensitivity) vannaVolSensitivity.textContent = `${(signals.volatility_sensitivity || 0).toFixed(1)}%`;
        if (vannaCrossRisk) vannaCrossRisk.textContent = signals.cross_effect_risk || 'Low';
        if (vannaOvernightRisk) vannaOvernightRisk.textContent = signals.overnight_risk || 'Low';
    }

    showVannaSignalsLoading() {
        const vannaSignalsList = document.getElementById('vanna-signals-list');
        if (vannaSignalsList) {
            vannaSignalsList.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Calculating EOD vanna signals...</p>
                </div>
            `;
        }
    }

    showVannaSignalsError(error) {
        const vannaSignalsList = document.getElementById('vanna-signals-list');
        if (vannaSignalsList) {
            vannaSignalsList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <p class="text-warning">Error loading EOD vanna signals</p>
                    <small class="text-muted">${error}</small>
                </div>
            `;
        }
    }

    showVannaSignalsPlaceholder() {
        const vannaSignalsList = document.getElementById('vanna-signals-list');
        if (vannaSignalsList) {
            vannaSignalsList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-wave-square fa-2x mb-2"></i>
                    <p>Select a ticker and expiry to monitor EOD vanna signals</p>
                </div>
            `;
        }
    }

    // Charm Signals Methods
    renderCharmSignals(data) {
        const charmSignalsList = document.getElementById('charm-signals-list');
        const activeCharmCount = document.getElementById('active-charm-signals-count');

        if (!charmSignalsList) return;

        const signals = data.charm_signals || {};
        const currentPrice = data.current_price || 0;

        // Update active signals count (1 if there's a signal, 0 if HOLD)
        const hasActiveSignal = signals.signal && signals.signal.action !== 'HOLD' ? 1 : 0;
        if (activeCharmCount) {
            activeCharmCount.textContent = hasActiveSignal;
        }

        // Update charm regime display
        this.updateCharmRegimeDisplay(signals);

        // Build charm signals HTML
        let signalsHtml = this.renderCharmAnalysisSection(signals, currentPrice);
        signalsHtml += this.renderSimpleCharmSignal(signals, currentPrice);

        charmSignalsList.innerHTML = signalsHtml;
    }

    renderCharmAnalysisSection(signals, currentPrice) {
        return `
            <div class="eod-signal-card mb-3">
                <div class="eod-signal-header">
                    <span class="eod-signal-title">Charm Exposure Analysis</span>
                    <span class="badge eod-signal-badge bg-info">${signals.regime || 'UNKNOWN'}</span>
                </div>
                <div class="eod-signal-content">
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Decay Rate</div>
                        <div class="eod-signal-metric-value">${(signals.time_decay_rate || 0).toFixed(1)}%</div>
                    </div>
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Signal Strength</div>
                        <div class="eod-signal-metric-value">${(signals.signal_strength || 0).toFixed(0)}%</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderCharmEntrySignals(entrySignals, currentPrice) {
        if (!entrySignals || entrySignals.length === 0) {
            return `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <p>No EOD charm signals detected</p>
                </div>
            `;
        }

        return entrySignals.map(signal => `
            <div class="eod-signal-card mb-3">
                <div class="eod-signal-header">
                    <span class="eod-signal-title">${this.formatSignalType(signal.type)}</span>
                    <span class="badge eod-signal-badge bg-${this.getSignalColor(signal.direction)}">${signal.direction}</span>
                </div>
                <div class="eod-signal-content">
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Confidence</div>
                        <div class="eod-signal-metric-value">${signal.confidence}%</div>
                    </div>
                    <div class="eod-signal-metric">
                        <div class="eod-signal-metric-label">Time Horizon</div>
                        <div class="eod-signal-metric-value">${this.formatTimeHorizon(signal.time_horizon)}</div>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">${signal.reasoning}</small>
                </div>
            </div>
        `).join('');
    }

    renderSimpleCharmSignal(signals, currentPrice) {
        if (!signals.signal) {
            return `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <p>No charm signal data available</p>
                </div>
            `;
        }

        const signal = signals.signal;
        const bias = signals.bias || {};
        const confidence = signals.confidence || {};

        // Determine signal color based on action
        let signalColor = 'secondary';
        let signalIcon = 'fas fa-minus';

        if (signal.action === 'BUY') {
            signalColor = 'success';
            signalIcon = 'fas fa-arrow-up';
        } else if (signal.action === 'SELL') {
            signalColor = 'danger';
            signalIcon = 'fas fa-arrow-down';
        }

        return `
            <div class="signal-card mb-3">
                <div class="signal-header">
                    <div class="signal-action">
                        <i class="${signalIcon} me-2"></i>
                        <span class="signal-action-text">${signal.action}</span>
                    </div>
                    <span class="badge bg-${signalColor}">${signal.confidence_level || 'LOW'}</span>
                </div>
                <div class="signal-content">
                    <div class="signal-reason mb-2">
                        <strong>Reasoning:</strong> ${signal.reason || 'No reason provided'}
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="signal-metric">
                                <label>Market Bias:</label>
                                <span class="signal-value ${bias.direction === 'BULLISH' ? 'text-success' : bias.direction === 'BEARISH' ? 'text-danger' : 'text-muted'}">
                                    ${bias.direction || 'NEUTRAL'} (${(bias.strength || 0).toFixed(1)}%)
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signal-metric">
                                <label>Confidence:</label>
                                <span class="signal-value">${confidence.level || 'LOW'} (${(confidence.score || 0).toFixed(0)}%)</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="signal-metric">
                                <label>Signal Strength:</label>
                                <span class="signal-value">${(signals.signal_strength || 0).toFixed(0)}%</span>
                            </div>
                        </div>
                    </div>
                    ${confidence.factors && confidence.factors.length > 0 ? `
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Supporting Factors:</strong> ${confidence.factors.join(', ')}
                            </small>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    updateCharmRegimeDisplay(signals) {
        const charmRegime = document.getElementById('charm-regime');
        const charmDecayRate = document.getElementById('charm-decay-rate');
        const charmDeltaSensitivity = document.getElementById('charm-delta-sensitivity');
        const charmEodRisk = document.getElementById('charm-eod-risk');

        if (charmRegime) charmRegime.textContent = signals.regime || 'Unknown';
        if (charmDecayRate) charmDecayRate.textContent = `${(signals.time_decay_rate || 0).toFixed(1)}%`;
        if (charmDeltaSensitivity) charmDeltaSensitivity.textContent = `${(signals.delta_sensitivity || 0).toFixed(1)}%`;
        if (charmEodRisk) charmEodRisk.textContent = signals.eod_risk_level || 'Low';
    }

    showCharmSignalsLoading() {
        const charmSignalsList = document.getElementById('charm-signals-list');
        if (charmSignalsList) {
            charmSignalsList.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Calculating EOD charm signals...</p>
                </div>
            `;
        }
    }

    showCharmSignalsError(error) {
        const charmSignalsList = document.getElementById('charm-signals-list');
        if (charmSignalsList) {
            charmSignalsList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <p class="text-warning">Error loading EOD charm signals</p>
                    <small class="text-muted">${error}</small>
                </div>
            `;
        }
    }

    showCharmSignalsPlaceholder() {
        const charmSignalsList = document.getElementById('charm-signals-list');
        if (charmSignalsList) {
            charmSignalsList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <p>Select a ticker and expiry to monitor EOD charm signals</p>
                </div>
            `;
        }
    }

    // Helper methods for signal formatting
    formatSignalType(type) {
        const typeMap = {
            'VOL_EXPANSION_LONG': 'Vol Expansion Long',
            'VOL_CONTRACTION_SHORT': 'Vol Contraction Short',
            'VANNA_SQUEEZE': 'Vanna Squeeze',
            'TIME_DECAY_LONG': 'Time Decay Long',
            'TIME_DECAY_SHORT': 'Time Decay Short',
            'CHARM_REVERSAL': 'Charm Reversal',
            'DELTA_STABILITY': 'Delta Stability'
        };
        return typeMap[type] || type;
    }

    getSignalColor(direction) {
        const colorMap = {
            'LONG': 'success',
            'SHORT': 'danger',
            'NEUTRAL': 'warning'
        };
        return colorMap[direction] || 'secondary';
    }

    formatTimeHorizon(horizon) {
        const horizonMap = {
            'EOD_TO_NEXT_DAY': 'EOD → Next Day',
            'EOD_TO_OPEN': 'EOD → Open',
            'EOD_CLOSE': 'EOD Close',
            'OVERNIGHT': 'Overnight',
            'RANGE_PLAY': 'Range Play',
            'VOLATILITY_PLAY': 'Vol Play',
            'TIME_DECAY_PLAY': 'Decay Play',
            'REVERSAL_PLAY': 'Reversal',
            'STABILITY_PLAY': 'Stability'
        };
        return horizonMap[horizon] || horizon;
    }
    
    updateConnectionStatus(connected) {
        const statusEl = document.getElementById('connection-status');
        const indicatorEl = document.querySelector('.connection-indicator i');

        // Check if elements exist before trying to access them
        if (statusEl) {
            // Remove all status classes
            statusEl.classList.remove('status-connected', 'status-disconnected', 'status-loading');

            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.classList.add('status-connected');
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.classList.add('status-disconnected');
            }
        }

        if (indicatorEl) {
            indicatorEl.classList.remove('status-connected', 'status-disconnected', 'status-loading');

            if (connected) {
                indicatorEl.classList.add('status-connected');
                indicatorEl.className = 'fas fa-circle status-connected';
            } else {
                indicatorEl.classList.add('status-disconnected');
                indicatorEl.className = 'fas fa-circle status-disconnected';
            }
        }
    }
    
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('d-none');
            this.isLoading = true;
        } else {
            overlay.classList.add('d-none');
            this.isLoading = false;
        }
    }
    
    showError(message) {
        console.error(message);
        // You can implement a toast notification system here
        alert('Error: ' + message);
    }
    
    showSuccess(message) {
        console.log(message);
        // You can implement a toast notification system here
    }

    async handleLogout() {
        try {
            // Show confirmation dialog
            if (!confirm('Are you sure you want to logout?')) {
                return;
            }

            // Show loading state on logout button
            const logoutBtn = document.getElementById('logout-btn');
            const originalText = logoutBtn.innerHTML;
            logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Logging out...';
            logoutBtn.disabled = true;

            // Get CSRF token from meta tag or cookie
            let csrfToken = '';
            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
            if (csrfMeta) {
                csrfToken = csrfMeta.getAttribute('content');
            }

            // Call logout API
            const response = await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                // Successful logout - redirect to login page
                console.log('Logout successful, redirecting...');
                window.location.href = '/login';
            } else {
                // If API fails, try the GET logout endpoint
                console.warn('API logout failed, trying GET logout');
                window.location.href = '/logout';
            }
        } catch (error) {
            console.error('Logout error:', error);
            // If there's an error, try the GET logout endpoint
            console.log('Using fallback GET logout');
            window.location.href = '/logout';
        }
    }

    async handleDisconnectData() {
        try {
            // Show confirmation dialog
            if (!confirm('Are you sure you want to disconnect and stop the server?\n\nThis will close the Greek Terminal application and stop all background processes.')) {
                return;
            }

            // Show loading state on disconnect button
            const disconnectBtn = document.getElementById('disconnect-data-btn');
            const originalText = disconnectBtn.innerHTML;
            disconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Stopping...';
            disconnectBtn.disabled = true;

            // Show user feedback
            this.showError('Stopping server and all processes... The application will close shortly.');

            // Call shutdown API
            const response = await fetch('/api/shutdown', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Shutdown response:', data.message);

                // Show final message with countdown
                let countdown = 3;
                const showFinalMessage = () => {
                    document.body.innerHTML = `
                        <div class="d-flex justify-content-center align-items-center vh-100 bg-dark text-light">
                            <div class="text-center">
                                <i class="fas fa-power-off fa-3x mb-3 text-danger"></i>
                                <h3>Greek Terminal Shutdown</h3>
                                <p class="text-muted">Server and all processes have been stopped successfully.</p>
                                <p class="text-muted">Application will close in ${countdown} seconds...</p>
                                <p class="text-muted">You can close this browser tab.</p>
                            </div>
                        </div>
                    `;
                };

                // Start countdown
                showFinalMessage();
                const countdownInterval = setInterval(() => {
                    countdown--;
                    if (countdown > 0) {
                        showFinalMessage();
                    } else {
                        clearInterval(countdownInterval);
                        document.body.innerHTML = `
                            <div class="d-flex justify-content-center align-items-center vh-100 bg-dark text-light">
                                <div class="text-center">
                                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                    <h3>Complete Shutdown</h3>
                                    <p class="text-muted">Greek Terminal and all processes have been terminated.</p>
                                    <p class="text-muted">You can close this browser tab.</p>
                                </div>
                            </div>
                        `;
                    }
                }, 1000);
            } else {
                // Reset button if request failed
                disconnectBtn.innerHTML = originalText;
                disconnectBtn.disabled = false;
                this.showError('Failed to disconnect server. Please try again.');
            }
        } catch (error) {
            console.error('Disconnect error:', error);
            // Reset button if there's an error
            const disconnectBtn = document.getElementById('disconnect-data-btn');
            if (disconnectBtn) {
                disconnectBtn.innerHTML = '<i class="fas fa-power-off me-1"></i>Disconnect';
                disconnectBtn.disabled = false;
            }
            this.showError('Error stopping server and processes. Please try again.');
        }
    }

    formatDate(dateString) {
        // Parse date string directly to avoid timezone conversion issues
        // Expected format: YYYY-MM-DD
        const parts = dateString.split('-');
        if (parts.length !== 3) {
            // Fallback to original method if format is unexpected
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1; // Month is 0-indexed in JavaScript
        const day = parseInt(parts[2]);

        // Create date in local timezone to avoid UTC conversion issues
        const date = new Date(year, month, day);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    async collectGexData() {
        // Collect current GEX data and add to history
        if (!this.currentTicker || !this.currentExpiry) {
            this.updateGexVisualizerStatus('Please select ticker and expiry first', 'warning');
            return;
        }

        try {
            this.updateGexVisualizerStatus('Collecting GEX data...', 'info');

            const response = await fetch(`/api/gex-history/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            if (data.success) {
                // Add to history
                const timestamp = new Date(data.timestamp);
                this.gexHistory.timestamps.push(timestamp);

                // Add current price to history
                this.gexHistory.prices.push(data.current_price);

                // OI data
                this.gexHistory.oi_data.min_strikes.push(data.oi_data.min_strike);
                this.gexHistory.oi_data.max_strikes.push(data.oi_data.max_strike);
                this.gexHistory.oi_data.min_gex.push(data.oi_data.min_gex);
                this.gexHistory.oi_data.max_gex.push(data.oi_data.max_gex);
                this.gexHistory.oi_data.ratios.push(data.oi_data.ratio);

                // Volume data
                this.gexHistory.vol_data.min_strikes.push(data.vol_data.min_strike);
                this.gexHistory.vol_data.max_strikes.push(data.vol_data.max_strike);
                this.gexHistory.vol_data.min_gex.push(data.vol_data.min_gex);
                this.gexHistory.vol_data.max_gex.push(data.vol_data.max_gex);
                this.gexHistory.vol_data.ratios.push(data.vol_data.ratio);

                // Keep only last 1000 data points
                const maxLength = 1000;
                if (this.gexHistory.timestamps.length > maxLength) {
                    this.gexHistory.timestamps = this.gexHistory.timestamps.slice(-maxLength);
                    this.gexHistory.prices = this.gexHistory.prices.slice(-maxLength);
                    Object.keys(this.gexHistory.oi_data).forEach(key => {
                        this.gexHistory.oi_data[key] = this.gexHistory.oi_data[key].slice(-maxLength);
                    });
                    Object.keys(this.gexHistory.vol_data).forEach(key => {
                        this.gexHistory.vol_data[key] = this.gexHistory.vol_data[key].slice(-maxLength);
                    });
                }

                this.updateGexVisualizer();
                this.updateGexVisualizerStatus(`Data collected: ${this.gexHistory.timestamps.length} points`, 'success');
            } else {
                this.updateGexVisualizerStatus(`Error: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('Error collecting GEX data:', error);
            this.updateGexVisualizerStatus('Error collecting data', 'error');
        }
    }

    clearGexHistory() {
        // Clear all GEX history data
        this.gexHistory = {
            timestamps: [],
            prices: [], // Clear price history as well
            oi_data: {
                min_strikes: [],
                max_strikes: [],
                min_gex: [],
                max_gex: [],
                ratios: []
            },
            vol_data: {
                min_strikes: [],
                max_strikes: [],
                min_gex: [],
                max_gex: [],
                ratios: []
            }
        };
        this.updateGexVisualizer();
        this.updateGexVisualizerStatus('History cleared', 'info');
    }

    // Exposure Session Data Management
    async loadExposureSessionData() {
        // Load session price data to show price development throughout the session
        if (!this.currentTicker || !this.currentExpiry) {
            console.log('Cannot load exposure session data: no ticker or expiry selected');
            return;
        }

        try {
            console.log('📊 Loading exposure session data...');

            // Get historical price data for the current session (last 6 hours with 1-minute intervals)
            const priceResponse = await fetch(`/api/candlestick/${this.currentTicker}/6h/1m`);
            const priceData = await priceResponse.json();

            if (!priceData.success || !priceData.candlestick_data || priceData.candlestick_data.length === 0) {
                console.warn('No price data available for exposure session');
                return;
            }

            // Filter to only include data from today's trading session
            const today = new Date();
            const marketOpen = new Date(today);
            marketOpen.setHours(9, 30, 0, 0); // 9:30 AM market open

            const sessionData = priceData.candlestick_data.filter(candle => {
                const candleTime = new Date(candle.timestamp);
                return candleTime >= marketOpen && candleTime <= today;
            });

            if (sessionData.length === 0) {
                console.log('📊 No session data available');
                return;
            }

            // Sample the data to avoid overwhelming the system (take every 5th data point)
            const sampledData = sessionData.filter((_, index) => index % 5 === 0);

            console.log(`📊 Loading ${sampledData.length} session data points`);

            // Clear and populate session data
            this.exposureSessionData = {
                timestamps: [],
                prices: [],
                candlestickData: []
            };

            // Add session data
            for (const candle of sampledData) {
                const timestamp = new Date(candle.timestamp);

                this.exposureSessionData.timestamps.push(timestamp);
                this.exposureSessionData.prices.push(candle.close);
                this.exposureSessionData.candlestickData.push({
                    timestamp: candle.timestamp,
                    open: candle.open,
                    high: candle.high,
                    low: candle.low,
                    close: candle.close,
                    volume: candle.volume
                });
            }

            console.log(`📊 Session data loaded: ${this.exposureSessionData.timestamps.length} points`);
            this.updateExposureSessionStatus();
        } catch (error) {
            console.error('Error loading exposure session data:', error);
        }
    }

    clearExposureSessionData() {
        // Clear all exposure session data
        this.exposureSessionData = {
            timestamps: [],
            prices: [],
            candlestickData: []
        };
        console.log('📊 Exposure session data cleared');
        this.updateExposureSessionStatus();
    }

    updateExposureSessionStatus() {
        // Update the session data status display
        const countElement = document.getElementById('exposure-session-count');
        const priceElement = document.getElementById('exposure-session-price');

        if (countElement) {
            countElement.innerHTML = `<i class="fas fa-database me-1"></i>${this.exposureSessionData.timestamps.length} points loaded`;
        }

        if (priceElement) {
            const latestPrice = this.exposureSessionData.prices[this.exposureSessionData.prices.length - 1];
            priceElement.innerHTML = latestPrice
                ? `<i class="fas fa-dollar-sign me-1"></i>$${latestPrice.toFixed(2)}`
                : '<i class="fas fa-dollar-sign me-1"></i>--';
        }
    }

    updateGexVisualizerStatus(message, type = 'info') {
        // Update the status message in the GEX Visualizer
        const statusElement = document.getElementById('gex-visualizer-status');
        if (statusElement) {
            const icons = {
                info: 'fas fa-info-circle',
                success: 'fas fa-check-circle',
                warning: 'fas fa-exclamation-triangle',
                error: 'fas fa-times-circle'
            };

            const colors = {
                info: 'text-muted',
                success: 'text-success',
                warning: 'text-warning',
                error: 'text-danger'
            };

            statusElement.innerHTML = `<i class="${icons[type]} me-1"></i>${message}`;
            statusElement.className = `text-muted small ${colors[type]}`;
        }
    }

    updateGexVisualizer() {
        // Update the GEX visualizer chart
        if (this.gexHistory.timestamps.length === 0) {
            this.renderEmptyGexChart();
            return;
        }

        const gexType = document.getElementById('gex-visualizer-type')?.value || 'Open Interest';
        this.renderGexVisualizerChart(gexType);
    }

    renderEmptyGexChart() {
        // Render empty chart with instructions
        const layout = {
            title: {
                text: 'GEX Visualizer - No Data',
                font: { color: '#ffffff', size: 16 }
            },
            paper_bgcolor: '#1e1e1e',
            plot_bgcolor: '#1e1e1e',
            font: { color: '#ffffff' },
            xaxis: {
                title: 'Time',
                gridcolor: '#444444',
                color: '#ffffff'
            },
            yaxis: {
                title: 'Price ($)',
                gridcolor: '#444444',
                color: '#ffffff'
            },
            annotations: [{
                text: 'Click "Refresh" to start collecting GEX data',
                x: 0.5,
                y: 0.5,
                xref: 'paper',
                yref: 'paper',
                showarrow: false,
                font: { size: 14, color: '#888888' }
            }]
        };

        const config = {
            responsive: true,
            displayModeBar: false
        };

        Plotly.newPlot('gex-visualizer-chart', [], layout, config);
    }

    renderGexVisualizerChart(gexType) {
        // Render the GEX visualizer time-series chart
        const traces = [];
        const colors = this.getCSSColors();

        // Price line chart over time
        if (this.gexHistory.timestamps.length > 0 && this.gexHistory.prices.length > 0) {
            // Filter out null/undefined prices and create valid data points
            const validPriceData = this.gexHistory.timestamps.map((timestamp, i) => ({
                x: timestamp,
                y: this.gexHistory.prices[i]
            })).filter(point => point.y !== null && point.y !== undefined);

            if (validPriceData.length > 0) {
                const currentPriceValue = validPriceData[validPriceData.length - 1].y;
                traces.push({
                    x: validPriceData.map(point => point.x),
                    y: validPriceData.map(point => point.y),
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: `Price: $${currentPriceValue.toFixed(2)}`,
                    line: {
                        color: '#ffffff',
                        width: 3
                    },
                    marker: {
                        color: '#ffffff',
                        size: 4
                    }
                });
            }
        }

        // Add GEX dots based on selected type
        if (gexType === 'Open Interest' || gexType === 'Both') {
            // Min GEX (Support) - Red dots
            const validMinOI = this.gexHistory.oi_data.min_strikes.map((strike, i) => ({
                x: this.gexHistory.timestamps[i],
                y: strike
            })).filter(point => point.y !== null && point.y !== undefined);

            if (validMinOI.length > 0) {
                traces.push({
                    x: validMinOI.map(p => p.x),
                    y: validMinOI.map(p => p.y),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Min GEX (OI) - Support',
                    marker: {
                        color: '#dc3545',
                        size: 6,
                        symbol: 'circle'
                    }
                });
            }

            // Max GEX (Resistance) - Green dots
            const validMaxOI = this.gexHistory.oi_data.max_strikes.map((strike, i) => ({
                x: this.gexHistory.timestamps[i],
                y: strike
            })).filter(point => point.y !== null && point.y !== undefined);

            if (validMaxOI.length > 0) {
                traces.push({
                    x: validMaxOI.map(p => p.x),
                    y: validMaxOI.map(p => p.y),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Max GEX (OI) - Resistance',
                    marker: {
                        color: '#28a745',
                        size: 6,
                        symbol: 'circle'
                    }
                });
            }
        }

        if (gexType === 'Volume' || gexType === 'Both') {
            // Min GEX Volume (Support) - Dark red squares
            const validMinVol = this.gexHistory.vol_data.min_strikes.map((strike, i) => ({
                x: this.gexHistory.timestamps[i],
                y: strike
            })).filter(point => point.y !== null && point.y !== undefined);

            if (validMinVol.length > 0) {
                traces.push({
                    x: validMinVol.map(p => p.x),
                    y: validMinVol.map(p => p.y),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Min GEX (Vol) - Support',
                    marker: {
                        color: '#8B0000',
                        size: 6,
                        symbol: 'square'
                    }
                });
            }

            // Max GEX Volume (Resistance) - Dark green squares
            const validMaxVol = this.gexHistory.vol_data.max_strikes.map((strike, i) => ({
                x: this.gexHistory.timestamps[i],
                y: strike
            })).filter(point => point.y !== null && point.y !== undefined);

            if (validMaxVol.length > 0) {
                traces.push({
                    x: validMaxVol.map(p => p.x),
                    y: validMaxVol.map(p => p.y),
                    type: 'scatter',
                    mode: 'markers',
                    name: 'Max GEX (Vol) - Resistance',
                    marker: {
                        color: '#006400',
                        size: 6,
                        symbol: 'square'
                    }
                });
            }
        }

        // Calculate current ratio for title
        let ratioText = '';
        if (this.gexHistory.timestamps.length > 0) {
            const lastIndex = this.gexHistory.timestamps.length - 1;
            if (gexType === 'Open Interest') {
                const ratio = this.gexHistory.oi_data.ratios[lastIndex];
                if (ratio !== null && ratio !== undefined) {
                    const ratioColor = ratio > 1 ? '🟢' : '🔴';
                    ratioText = ` | P/N Ratio: ${ratio.toFixed(2)} ${ratioColor}`;
                }
            } else if (gexType === 'Volume') {
                const ratio = this.gexHistory.vol_data.ratios[lastIndex];
                if (ratio !== null && ratio !== undefined) {
                    const ratioColor = ratio > 1 ? '🟢' : '🔴';
                    ratioText = ` | P/N Ratio: ${ratio.toFixed(2)} ${ratioColor}`;
                }
            } else if (gexType === 'Both') {
                const oiRatio = this.gexHistory.oi_data.ratios[lastIndex];
                const volRatio = this.gexHistory.vol_data.ratios[lastIndex];
                if (oiRatio !== null && volRatio !== null) {
                    const oiColor = oiRatio > 1 ? '🟢' : '🔴';
                    const volColor = volRatio > 1 ? '🟢' : '🔴';
                    ratioText = ` | OI P/N: ${oiRatio.toFixed(2)} ${oiColor} | Vol P/N: ${volRatio.toFixed(2)} ${volColor}`;
                }
            }
        }

        const title = `${this.currentTicker} - GEX Visualizer (${gexType})${ratioText}`;

        const layout = {
            title: {
                text: title,
                font: { color: '#ffffff', size: 16 }
            },
            paper_bgcolor: '#1e1e1e',
            plot_bgcolor: '#1e1e1e',
            font: { color: '#ffffff' },
            xaxis: {
                title: 'Time',
                gridcolor: '#444444',
                color: '#ffffff',
                type: 'date'
            },
            yaxis: {
                title: 'Price ($)',
                gridcolor: '#444444',
                color: '#ffffff'
            },
            legend: {
                font: { color: '#ffffff' },
                bgcolor: 'rgba(0,0,0,0.5)'
            },
            margin: { t: 50, r: 50, b: 50, l: 50 }
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot('gex-visualizer-chart', traces, layout, config);
    }

    async exportToTradingView() {
        // Export current gamma exposure levels to TradingView Pine Script
        if (!this.currentTicker || !this.currentExpiry) {
            alert('Please select a ticker and expiry date first.');
            return;
        }

        // Store original button state for restoration
        const exportBtn = document.getElementById('export-tradingview-btn');
        const originalText = exportBtn.innerHTML;
        const originalDisabled = exportBtn.disabled;

        try {
            // Show loading state
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
            exportBtn.disabled = true;

            // Store button state for cleanup
            this.exportButtonState = {
                element: exportBtn,
                originalText: originalText,
                originalDisabled: originalDisabled
            };

            // Set a timeout to reset button state if request takes too long
            const timeoutId = setTimeout(() => {
                console.warn('Export request timed out, resetting button state');
                this.resetExportButtonState();
            }, 30000); // 30 second timeout

            const response = await fetch(`/api/export-tradingview/${this.currentTicker}/${this.currentExpiry}`);
            clearTimeout(timeoutId); // Clear timeout if request completes
            const data = await response.json();

            if (data.success) {
                // Store the Pine Script for copying
                this.currentPineScript = data.pine_script;

                // Update modal content
                document.getElementById('pine-script-code').value = data.pine_script;

                // Update modal title with ticker info
                const modalTitle = document.getElementById('tradingview-export-modal-label');
                modalTitle.innerHTML = `
                    <i class="fas fa-download me-2"></i>
                    ${this.currentTicker} - TradingView Pine Script Export
                `;

                // Reset button state before showing modal
                this.resetExportButtonState();

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('tradingview-export-modal'));
                modal.show();

                console.log('Pine Script generated successfully');
            } else {
                alert(`Error generating Pine Script: ${data.error}`);
                this.resetExportButtonState();
            }
        } catch (error) {
            console.error('Error exporting to TradingView:', error);
            alert('Error generating Pine Script. Please try again.');
            this.resetExportButtonState();
        }
    }

    resetExportButtonState() {
        // Reset the export button to its original state
        if (this.exportButtonState) {
            const { element, originalText, originalDisabled } = this.exportButtonState;
            if (element) {
                element.innerHTML = originalText;
                element.disabled = originalDisabled;
            }
            this.exportButtonState = null;
        } else {
            // Fallback if no stored state
            const exportBtn = document.getElementById('export-tradingview-btn');
            if (exportBtn) {
                exportBtn.innerHTML = '<i class="fas fa-download me-1"></i>Export to TradingView';
                exportBtn.disabled = false;
            }
        }
    }

    copyPineScriptToClipboard() {
        // Copy Pine Script code to clipboard
        const pineScriptCode = document.getElementById('pine-script-code');

        if (pineScriptCode && pineScriptCode.value) {
            // Select and copy the text
            pineScriptCode.select();
            pineScriptCode.setSelectionRange(0, 99999); // For mobile devices

            try {
                // Try using the modern clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(pineScriptCode.value).then(() => {
                        this.showCopySuccess();
                    }).catch(() => {
                        // Fallback to execCommand
                        this.fallbackCopyToClipboard(pineScriptCode.value);
                    });
                } else {
                    // Fallback to execCommand
                    this.fallbackCopyToClipboard(pineScriptCode.value);
                }
            } catch (error) {
                console.error('Error copying to clipboard:', error);
                alert('Error copying to clipboard. Please manually select and copy the code.');
            }
        } else {
            alert('No Pine Script code to copy.');
        }
    }

    fallbackCopyToClipboard(text) {
        // Fallback method using execCommand
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                this.showCopySuccess();
            } else {
                alert('Error copying to clipboard. Please manually select and copy the code.');
            }
        } catch (error) {
            console.error('Fallback copy failed:', error);
            alert('Error copying to clipboard. Please manually select and copy the code.');
        } finally {
            document.body.removeChild(textArea);
        }
    }

    showCopySuccess() {
        // Show success feedback
        const copyBtn = document.getElementById('copy-pine-script-btn');
        const originalText = copyBtn.innerHTML;

        copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        copyBtn.classList.remove('btn-success');
        copyBtn.classList.add('btn-success');

        setTimeout(() => {
            copyBtn.innerHTML = originalText;
        }, 2000);

        console.log('Pine Script copied to clipboard successfully');
    }

    // Fullscreen chart functionality
    initializeFullscreenHandlers() {
        const modal = document.getElementById('chart-fullscreen-modal');
        const closeBtn = document.getElementById('close-fullscreen-btn');
        const titleElement = document.getElementById('fullscreen-chart-title');
        const containerElement = document.getElementById('fullscreen-chart-container');

        // Add click handlers to all fullscreen buttons
        document.querySelectorAll('.chart-fullscreen-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const chartId = btn.getAttribute('data-chart');
                this.openFullscreen(chartId);
            });
        });

        // Close button handler
        closeBtn.addEventListener('click', () => {
            this.closeFullscreen();
        });

        // ESC key handler
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                this.closeFullscreen();
            }
        });

        // Click outside to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeFullscreen();
            }
        });
    }

    openFullscreen(chartId) {
        console.log('Opening fullscreen for chart:', chartId);
        const modal = document.getElementById('chart-fullscreen-modal');
        const titleElement = document.getElementById('fullscreen-chart-title');
        const containerElement = document.getElementById('fullscreen-chart-container');
        const originalChart = document.getElementById(chartId);

        if (!originalChart) {
            console.error('Original chart not found:', chartId);
            return;
        }

        if (!modal) {
            console.error('Fullscreen modal not found');
            return;
        }

        // Set title based on chart type
        const chartTitles = {
            'candlestick-chart': 'Candlestick Chart',
            'gex-chart': 'Gamma Exposure (GEX)',
            'vgex-chart': 'Volume Gamma Exposure (VGEX)',
            'dex-chart': 'Delta Exposure (DEX)',
            'dgex-chart': 'Delta-Adjusted Gamma Exposure (D_GEX)',
            'tex-chart': 'Theta Exposure (TEX)',
            'vegx-chart': 'Vega Exposure (VEGX)',
            'vex-chart': 'Vanna Exposure (VEX)',
            'cex-chart': 'Charm Exposure (CEX)',
            'oi-chart': 'Open Interest Distribution',
            'gex-visualizer-chart': 'GEX Visualizer',
            'volatility-surface-chart': 'Volatility Surface',
            'volatility-skew-chart': 'Volatility Skew',

        };

        titleElement.textContent = chartTitles[chartId] || 'Chart';
        console.log('Set title to:', titleElement.textContent);

        // Show modal first
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        console.log('Modal activated, body overflow hidden');
        console.log('Modal classes:', modal.className);
        console.log('Modal display style:', window.getComputedStyle(modal).display);

        // Store original chart ID for restoration
        modal.setAttribute('data-original-chart', chartId);

        // Re-render the chart in fullscreen after a short delay
        setTimeout(() => {
            console.log('Rendering fullscreen chart for:', chartId);
            this.renderFullscreenChart(chartId);
        }, 100);
    }

    closeFullscreen() {
        const modal = document.getElementById('chart-fullscreen-modal');
        const containerElement = document.getElementById('fullscreen-chart-container');

        // Hide modal
        modal.classList.remove('active');
        document.body.style.overflow = '';

        // Clear fullscreen zone fill series
        if (this.fullscreenZoneFillSeries) {
            this.fullscreenZoneFillSeries = [];
        }

        // Clear container
        containerElement.innerHTML = '';
        modal.removeAttribute('data-original-chart');
    }

    renderFullscreenChart(originalChartId) {
        const containerElement = document.getElementById('fullscreen-chart-container');
        console.log('Rendering fullscreen chart, container found:', !!containerElement);

        // Handle different chart types
        if (originalChartId === 'candlestick-chart') {
            console.log('Rendering candlestick chart in fullscreen');
            this.renderFullscreenCandlestickChart();
        } else {
            console.log('Rendering Plotly chart in fullscreen:', originalChartId);
            // Handle Plotly charts - copy the chart to fullscreen
            this.renderFullscreenPlotlyChart(originalChartId);
        }
    }

    renderFullscreenCandlestickChart() {
        const containerElement = document.getElementById('fullscreen-chart-container');
        if (!containerElement) {
            console.error('Fullscreen container not found');
            return;
        }

        console.log('Container dimensions:', containerElement.clientWidth, 'x', containerElement.clientHeight);

        // Clear container
        containerElement.innerHTML = '';

        try {
            // Check if LightweightCharts is available
            if (typeof LightweightCharts === 'undefined') {
                console.error('LightweightCharts library not available in fullscreen mode');
                containerElement.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">TradingView library not loaded. Please refresh the page.</div>';
                return;
            }

            const colors = this.getCSSColors();
            console.log('Got CSS colors for fullscreen chart');

            // Create fullscreen chart with larger dimensions
            console.log('Creating fullscreen TradingView chart...');
            const fullscreenChart = LightweightCharts.createChart(containerElement, {
                width: containerElement.clientWidth,
                height: containerElement.clientHeight,
                layout: {
                    background: { color: colors.bgChartAlt },
                    textColor: colors.textPrimary,
                    fontFamily: 'Inter, "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif',
                },
                grid: {
                    vertLines: {
                        color: colors.gridColor,
                        style: LightweightCharts.LineStyle.Solid,
                        visible: true
                    },
                    horzLines: {
                        color: colors.gridColor,
                        style: LightweightCharts.LineStyle.Solid,
                        visible: true
                    },
                },
                rightPriceScale: {
                    borderColor: colors.gridColor,
                    textColor: colors.textSecondary,
                },
                timeScale: {
                    borderColor: colors.gridColor,
                    textColor: colors.textSecondary,
                    timeVisible: true,
                    secondsVisible: false,
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                    vertLine: {
                        color: colors.accentPrimary,
                        width: 1,
                        style: LightweightCharts.LineStyle.Dashed,
                    },
                    horzLine: {
                        color: colors.accentPrimary,
                        width: 1,
                        style: LightweightCharts.LineStyle.Dashed,
                    },
                },
            });

            console.log('Fullscreen chart created successfully');

            // Add candlestick series
            console.log('Adding candlestick series to fullscreen chart...');
            const candlestickSeries = fullscreenChart.addCandlestickSeries({
                upColor: colors.callColor,
                downColor: colors.putColor,
                borderDownColor: colors.putColor,
                borderUpColor: colors.callColor,
                wickDownColor: colors.putColor,
                wickUpColor: colors.callColor,
                borderVisible: true,
                wickVisible: true,
            });

            // Add volume series
            console.log('Adding volume series to fullscreen chart...');
            const volumeSeries = fullscreenChart.addHistogramSeries({
                color: colors.accentInfo,
                priceFormat: {
                    type: 'volume',
                },
                priceScaleId: 'volume',
                scaleMargins: {
                    top: 0.8,
                    bottom: 0,
                },
            });

            // Configure volume price scale
            fullscreenChart.priceScale('volume').applyOptions({
                scaleMargins: {
                    top: 0.8,
                    bottom: 0,
                },
            });

            // Copy data from original chart if available
            if (this.candlestickData && this.volumeData) {
                console.log('Setting candlestick data in fullscreen chart...');
                candlestickSeries.setData(this.candlestickData);
                volumeSeries.setData(this.volumeData);

                // Fit content to show all data
                fullscreenChart.timeScale().fitContent();

                console.log(`Fullscreen chart populated with ${this.candlestickData.length} candles`);

                // Add gamma zones to fullscreen chart if available
                if (this.gammaZoneData) {
                    console.log('Adding gamma zones to fullscreen chart...');
                    this.addGammaZonesToFullscreenChart(fullscreenChart, candlestickSeries, containerElement);
                }
            } else {
                console.log('No candlestick data available for fullscreen chart');
                // Show a message in the chart area
                const messageDiv = document.createElement('div');
                messageDiv.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: white;
                    text-align: center;
                    font-size: 18px;
                    z-index: 1000;
                `;
                messageDiv.innerHTML = `
                    <h5>No Chart Data Available</h5>
                    <p>Please load chart data first by clicking "Update Chart" in the candlestick tab.</p>
                `;
                containerElement.appendChild(messageDiv);
            }

        } catch (error) {
            console.error('Error creating fullscreen candlestick chart:', error);
            containerElement.innerHTML = `<div style="color: white; text-align: center; padding: 50px;">
                <h5>Error loading chart</h5>
                <p>${error.message}</p>
                <p>Please try again or refresh the page.</p>
            </div>`;
        }
    }

    addGammaZonesToFullscreenChart(fullscreenChart, candlestickSeries, containerElement) {
        try {
            if (!this.gammaZoneData) {
                console.log('No gamma zone data available for fullscreen chart');
                return;
            }

            const { gexData, currentPrice } = this.gammaZoneData;
            console.log(`Adding gamma zones to fullscreen chart - Data length: ${gexData.length}, Current price: ${currentPrice}`);

            // Store references for fullscreen zone fills
            this.fullscreenZoneFillSeries = [];

            // Calculate net gamma exposure by strike (same logic as original)
            const netExposureByStrike = {};

            gexData.forEach(item => {
                const strike = item.strike;
                if (!netExposureByStrike[strike]) {
                    netExposureByStrike[strike] = 0;
                }
                netExposureByStrike[strike] += item.net_gex || 0;
            });

            // Convert to array and sort by absolute exposure
            const strikeExposures = Object.entries(netExposureByStrike)
                .map(([strike, exposure]) => ({
                    strike: parseFloat(strike),
                    exposure: exposure
                }))
                .sort((a, b) => Math.abs(b.exposure) - Math.abs(a.exposure));

            // Get top 3 positive and top 3 negative strikes
            const positiveStrikes = strikeExposures
                .filter(item => item.exposure > 0)
                .slice(0, 3)
                .map(item => item.strike);

            const negativeStrikes = strikeExposures
                .filter(item => item.exposure < 0)
                .slice(0, 3)
                .map(item => item.strike);

            console.log('Fullscreen - Top positive strikes (resistance):', positiveStrikes);
            console.log('Fullscreen - Top negative strikes (support):', negativeStrikes);

            // Add zero line first (baseline for zones)
            console.log('Adding zero line to fullscreen chart...');
            this.addZeroLineToFullscreenChart(candlestickSeries, currentPrice);

            // Add positive zones (green - resistance levels) with fills
            positiveStrikes.forEach((strike, index) => {
                this.addGammaZoneToFullscreenChart(fullscreenChart, candlestickSeries, strike, 'green', index + 1, 'Resistance');
            });

            // Add negative zones (red - support levels) with fills
            negativeStrikes.forEach((strike, index) => {
                this.addGammaZoneToFullscreenChart(fullscreenChart, candlestickSeries, strike, 'red', index + 1, 'Support');
            });

            // Calculate and add zero GEX level
            const zeroGexLevel = this.calculateZeroGexLevel(strikeExposures, currentPrice);
            if (zeroGexLevel) {
                console.log('Adding zero GEX line to fullscreen chart...');
                this.addZeroGexLineToFullscreenChart(candlestickSeries, zeroGexLevel);
            }

            console.log(`✅ Finished adding gamma zones to fullscreen chart. Zone fills: ${this.fullscreenZoneFillSeries.length}`);

        } catch (error) {
            console.error('❌ Error adding gamma zones to fullscreen chart:', error);
        }
    }

    addGammaZoneToFullscreenChart(fullscreenChart, candlestickSeries, strike, color, rank, type) {
        try {
            console.log(`Adding fullscreen gamma zone: ${type} ${rank} at strike ${strike} with color ${color}`);

            // Calculate zone boundaries (±0.08% around strike)
            const upperLevel = strike * 1.0008;  // +0.08%
            const lowerLevel = strike * 0.9992;  // -0.08%

            // Set transparency levels to match CSS variables
            const colors = this.getCSSColors();
            const transparencyLevels = [colors.zoneAlpha1, colors.zoneAlpha2, colors.zoneAlpha3];
            const alpha = transparencyLevels[rank - 1] || colors.zoneAlpha3;

            console.log(`Fullscreen zone boundaries: ${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)}, alpha: ${alpha}`);

            // Create zone fill using baseline series (same as original implementation)
            this.createFullscreenZoneFill(fullscreenChart, upperLevel, lowerLevel, color, alpha, rank, type);

            // Upper zone boundary (dashed)
            const upperLine = candlestickSeries.createPriceLine({
                price: upperLevel,
                color: color,
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: false,
                title: '',
            });

            // Lower zone boundary (dashed)
            const lowerLine = candlestickSeries.createPriceLine({
                price: lowerLevel,
                color: color,
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: false,
                title: '',
            });

            console.log(`✅ Successfully added fullscreen ${color} gamma zone ${rank} with fill: ${strike.toFixed(2)} (${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)})`);

        } catch (error) {
            console.error(`❌ Error adding fullscreen gamma zone for strike ${strike}:`, error);
        }
    }

    addZeroLineToFullscreenChart(candlestickSeries, currentPrice) {
        try {
            // Add zero line (baseline for gamma zones) - solid white line at current price
            const zeroLine = candlestickSeries.createPriceLine({
                price: currentPrice,
                color: '#ffffff',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: true,
                title: 'Current Price',
            });

            console.log(`Added zero line (current price) to fullscreen chart at: ${currentPrice.toFixed(2)}`);

        } catch (error) {
            console.error('Error adding zero line to fullscreen chart:', error);
        }
    }

    addZeroGexLineToFullscreenChart(candlestickSeries, zeroGexLevel) {
        try {
            // Add zero GEX line (purple dashed)
            const zeroLine = candlestickSeries.createPriceLine({
                price: zeroGexLevel,
                color: '#8b5cf6',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: true,
                title: 'Zero GEX',
            });

            console.log(`Added zero GEX line to fullscreen chart at: ${zeroGexLevel.toFixed(2)}`);

        } catch (error) {
            console.error('Error adding zero GEX line to fullscreen chart:', error);
        }
    }

    createFullscreenZoneFill(fullscreenChart, upperLevel, lowerLevel, color, alpha, rank, type) {
        try {
            console.log(`Creating fullscreen zone fill for ${type} ${rank}: ${lowerLevel.toFixed(2)} - ${upperLevel.toFixed(2)}`);

            // Use CSS color variables for consistent theming
            const colors = this.getCSSColors();

            // Convert hex to RGB for zone fills
            const hexToRgb = (hex) => {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16)
                } : { r: 128, g: 128, b: 128 };
            };

            const colorMap = {
                'green': hexToRgb(colors.callColor),    // Call color for positive zones
                'red': hexToRgb(colors.putColor),       // Put color for negative zones
                'yellow': hexToRgb(colors.accentWarning) // Warning color for zero GEX
            };

            const rgb = colorMap[color] || { r: 128, g: 128, b: 128 };
            const fillColor = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${alpha})`;

            // Create a baseline series that fills between the upper and lower levels
            const zoneFillSeries = fullscreenChart.addBaselineSeries({
                baseValue: { type: 'price', price: lowerLevel },
                topLineColor: 'transparent',
                bottomLineColor: 'transparent',
                topFillColor1: fillColor,
                topFillColor2: fillColor,
                bottomFillColor1: 'transparent',
                bottomFillColor2: 'transparent',
                lineWidth: 0,
                priceLineVisible: false,
                lastValueVisible: false,
                crosshairMarkerVisible: false,
                title: ''
            });

            // Create time data that spans the chart
            const currentTime = Math.floor(Date.now() / 1000);
            const timePoints = [];

            // Create data points every hour for 30 days
            for (let i = -30; i <= 30; i++) {
                for (let h = 0; h < 24; h++) {
                    timePoints.push(currentTime + (i * 24 * 3600) + (h * 3600));
                }
            }

            // Create data at the upper level (baseline will fill down to lowerLevel)
            const fillData = timePoints.map(time => ({
                time: time,
                value: upperLevel
            }));

            zoneFillSeries.setData(fillData);
            this.fullscreenZoneFillSeries.push(zoneFillSeries);

            console.log(`✅ Created fullscreen zone fill with ${fillData.length} data points and color ${fillColor}`);

        } catch (error) {
            console.error('❌ Error creating fullscreen zone fill:', error);
        }
    }

    renderFullscreenPlotlyChart(originalChartId) {
        const containerElement = document.getElementById('fullscreen-chart-container');
        const originalChart = document.getElementById(originalChartId);

        if (!containerElement || !originalChart) return;

        // Clear container
        containerElement.innerHTML = '';

        // Create a new div for the fullscreen chart
        const fullscreenChartDiv = document.createElement('div');
        fullscreenChartDiv.id = 'fullscreen-' + originalChartId;
        fullscreenChartDiv.style.width = '100%';
        fullscreenChartDiv.style.height = '100%';
        containerElement.appendChild(fullscreenChartDiv);

        // Get the original chart's data and layout
        try {
            const originalPlotlyData = originalChart.data;
            const originalPlotlyLayout = originalChart.layout;

            if (originalPlotlyData && originalPlotlyLayout) {
                // Create a copy of the layout with fullscreen dimensions
                const fullscreenLayout = {
                    ...originalPlotlyLayout,
                    width: containerElement.clientWidth,
                    height: containerElement.clientHeight,
                    margin: { t: 60, r: 60, b: 60, l: 60 }
                };

                const config = {
                    responsive: true,
                    displayModeBar: true,
                    modeBarButtonsToRemove: ['lasso2d', 'select2d'],
                    displaylogo: false
                };

                // Render the chart in fullscreen
                Plotly.newPlot(fullscreenChartDiv.id, originalPlotlyData, fullscreenLayout, config);
            }
        } catch (error) {
            console.error('Error creating fullscreen Plotly chart:', error);
            // Fallback: just copy the original chart content
            containerElement.innerHTML = originalChart.outerHTML;
            const copiedChart = containerElement.firstChild;
            if (copiedChart) {
                copiedChart.id = 'fullscreen-' + originalChartId;
                copiedChart.style.width = '100%';
                copiedChart.style.height = '100%';
            }
        }
    }

    // Volatility Surface and Skew Functions
    async fetchVolatilityData() {
        if (!this.currentTicker) return;

        try {
            console.log('Fetching volatility data...');
            this.updateVolatilityStatus('Loading volatility data...', 'info');

            // Initialize view containers
            this.handleVolatilityViewChange();

            const viewType = document.getElementById('volatility-view-type')?.value || 'skew';

            if (viewType === 'surface' || viewType === 'both') {
                await this.fetchVolatilitySurface();
            }

            if (viewType === 'skew' || viewType === 'both') {
                await this.fetchVolatilitySkew();
            }

            this.updateVolatilityStatus('Ready', 'success');
        } catch (error) {
            console.error('Error fetching volatility data:', error);
            this.updateVolatilityStatus('Error loading data', 'error');
        }
    }

    async fetchVolatilitySurface() {
        try {
            const response = await fetch(`/api/volatility-surface/${this.currentTicker}`);
            const data = await response.json();

            if (data.success) {
                this.renderVolatilitySurface(data.surface_data, data.current_price);
                this.updateVolatilityStats(data.surface_data, data.current_price);
            } else {
                console.error('Failed to fetch volatility surface:', data.error);
            }
        } catch (error) {
            console.error('Error fetching volatility surface:', error);
        }
    }

    async fetchVolatilitySkew() {
        if (!this.currentExpiry) {
            console.warn('No current expiry set for volatility skew');
            return;
        }

        try {
            console.log(`Fetching volatility skew for ${this.currentTicker} ${this.currentExpiry}`);
            const response = await fetch(`/api/volatility-skew/${this.currentTicker}/${this.currentExpiry}`);
            const data = await response.json();

            console.log('Volatility skew response:', data);

            if (data.success) {
                this.renderVolatilitySkew(data.skew_data, data.current_price);
                this.updateVolatilityStats(data.skew_data, data.current_price);
            } else {
                console.error('Failed to fetch volatility skew:', data.error);
                const chartDiv = document.getElementById('volatility-skew-chart');
                if (chartDiv) {
                    chartDiv.innerHTML = `<div style="color: white; text-align: center; padding: 50px;">Error: ${data.error}</div>`;
                }
            }
        } catch (error) {
            console.error('Error fetching volatility skew:', error);
            const chartDiv = document.getElementById('volatility-skew-chart');
            if (chartDiv) {
                chartDiv.innerHTML = `<div style="color: white; text-align: center; padding: 50px;">Error loading volatility skew</div>`;
            }
        }
    }

    renderVolatilitySurface(surfaceData, currentPrice) {
        const chartDiv = document.getElementById('volatility-surface-chart');
        if (!chartDiv) return;

        try {
            console.log('Rendering volatility surface with', surfaceData.length, 'data points');

            // Filter data based on settings
            const showCalls = document.getElementById('volatility-show-calls')?.checked !== false;
            const showPuts = document.getElementById('volatility-show-puts')?.checked !== false;

            let filteredData = surfaceData.filter(item =>
                ((showCalls && item.option_type === 'call') || (showPuts && item.option_type === 'put'))
            );

            console.log('After filtering:', filteredData.length, 'data points (calls:', showCalls, 'puts:', showPuts, ')');

            if (filteredData.length === 0) {
                chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">No volatility surface data available</div>';
                return;
            }

            // Prepare data for 3D surface plot
            const strikes = [...new Set(filteredData.map(item => item.strike))].sort((a, b) => a - b);
            const daysToExpiry = [...new Set(filteredData.map(item => item.days_to_expiry))].sort((a, b) => a - b);

            // Create Z matrix for surface
            const zMatrix = [];
            const callsMatrix = [];
            const putsMatrix = [];

            daysToExpiry.forEach(days => {
                const zRow = [];
                const callsRow = [];
                const putsRow = [];

                strikes.forEach(strike => {
                    const callData = filteredData.find(item =>
                        item.strike === strike &&
                        item.days_to_expiry === days &&
                        item.option_type === 'call'
                    );
                    const putData = filteredData.find(item =>
                        item.strike === strike &&
                        item.days_to_expiry === days &&
                        item.option_type === 'put'
                    );

                    const callIV = callData ? callData.implied_volatility * 100 : null;
                    const putIV = putData ? putData.implied_volatility * 100 : null;

                    // Use average of call and put IV, or whichever is available
                    let avgIV = null;
                    if (callIV !== null && putIV !== null) {
                        avgIV = (callIV + putIV) / 2;
                    } else if (callIV !== null) {
                        avgIV = callIV;
                    } else if (putIV !== null) {
                        avgIV = putIV;
                    }

                    zRow.push(avgIV);
                    callsRow.push(callIV);
                    putsRow.push(putIV);
                });

                zMatrix.push(zRow);
                callsMatrix.push(callsRow);
                putsMatrix.push(putsRow);
            });

            // Interpolate missing values to eliminate gaps in the surface
            console.log('Before interpolation - zMatrix nulls:', this.countNulls(zMatrix));
            this.interpolateMatrix(zMatrix);
            this.interpolateMatrix(callsMatrix);
            this.interpolateMatrix(putsMatrix);
            console.log('After interpolation - zMatrix nulls:', this.countNulls(zMatrix));

            const traces = [];

            // Combined surface
            if (showCalls && showPuts) {
                traces.push({
                    type: 'surface',
                    x: strikes,
                    y: daysToExpiry,
                    z: zMatrix,
                    name: 'Combined IV',
                    colorscale: 'Viridis',
                    showscale: true,
                    connectgaps: true,  // This helps fill gaps in the surface
                    contours: {
                        z: {
                            show: true,
                            usecolormap: true,
                            highlightcolor: "#42f462",
                            project: { z: true }
                        }
                    },
                    colorbar: {
                        title: 'Implied Volatility (%)',
                        titleside: 'right'
                    }
                });
            } else if (showCalls) {
                traces.push({
                    type: 'surface',
                    x: strikes,
                    y: daysToExpiry,
                    z: callsMatrix,
                    name: 'Calls IV',
                    colorscale: 'Greens',
                    showscale: true,
                    connectgaps: true,  // This helps fill gaps in the surface
                    contours: {
                        z: {
                            show: true,
                            usecolormap: true,
                            highlightcolor: "#42f462",
                            project: { z: true }
                        }
                    },
                    colorbar: {
                        title: 'Calls IV (%)',
                        titleside: 'right'
                    }
                });
            } else if (showPuts) {
                traces.push({
                    type: 'surface',
                    x: strikes,
                    y: daysToExpiry,
                    z: putsMatrix,
                    name: 'Puts IV',
                    colorscale: 'Reds',
                    showscale: true,
                    connectgaps: true,  // This helps fill gaps in the surface
                    contours: {
                        z: {
                            show: true,
                            usecolormap: true,
                            highlightcolor: "#42f462",
                            project: { z: true }
                        }
                    },
                    colorbar: {
                        title: 'Puts IV (%)',
                        titleside: 'right'
                    }
                });
            }

            const layout = {
                title: {
                    text: `${this.currentTicker} Volatility Surface`,
                    font: { color: 'white', size: 16 }
                },
                scene: {
                    xaxis: {
                        title: {
                            text: 'Strike Price ($)',
                            font: { color: 'white', size: 14 }
                        },
                        color: 'white',
                        gridcolor: '#444',
                        showspikes: false
                    },
                    yaxis: {
                        title: {
                            text: 'Days to Expiry',
                            font: { color: 'white', size: 14 }
                        },
                        color: 'white',
                        gridcolor: '#444',
                        showspikes: false
                    },
                    zaxis: {
                        title: {
                            text: 'Implied Volatility (%)',
                            font: { color: 'white', size: 14 }
                        },
                        color: 'white',
                        gridcolor: '#444',
                        showspikes: false
                    },
                    bgcolor: 'rgba(0,0,0,0)',
                    camera: {
                        eye: { x: 1.5, y: 1.5, z: 1.5 }
                    }
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: { color: 'white' },
                margin: { t: 50, r: 50, b: 50, l: 50 }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['lasso2d', 'select2d'],
                displaylogo: false
            };

            Plotly.newPlot(chartDiv, traces, layout, config);

        } catch (error) {
            console.error('Error rendering volatility surface:', error);
            chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">Error rendering volatility surface</div>';
        }
    }

    interpolateMatrix(matrix) {
        // Fill null values using linear interpolation to create smooth surfaces
        const rows = matrix.length;
        const cols = matrix[0] ? matrix[0].length : 0;

        if (rows === 0 || cols === 0) return;

        // First pass: interpolate horizontally (across strikes for each expiry)
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                if (matrix[i][j] === null) {
                    // Find nearest non-null values to the left and right
                    let leftVal = null, rightVal = null;
                    let leftIdx = -1, rightIdx = -1;

                    // Search left
                    for (let k = j - 1; k >= 0; k--) {
                        if (matrix[i][k] !== null) {
                            leftVal = matrix[i][k];
                            leftIdx = k;
                            break;
                        }
                    }

                    // Search right
                    for (let k = j + 1; k < cols; k++) {
                        if (matrix[i][k] !== null) {
                            rightVal = matrix[i][k];
                            rightIdx = k;
                            break;
                        }
                    }

                    // Interpolate if we have both left and right values
                    if (leftVal !== null && rightVal !== null) {
                        const ratio = (j - leftIdx) / (rightIdx - leftIdx);
                        matrix[i][j] = leftVal + ratio * (rightVal - leftVal);
                    } else if (leftVal !== null) {
                        matrix[i][j] = leftVal; // Use left value if no right value
                    } else if (rightVal !== null) {
                        matrix[i][j] = rightVal; // Use right value if no left value
                    }
                }
            }
        }

        // Second pass: interpolate vertically (across expiries for each strike)
        for (let j = 0; j < cols; j++) {
            for (let i = 0; i < rows; i++) {
                if (matrix[i][j] === null) {
                    // Find nearest non-null values above and below
                    let topVal = null, bottomVal = null;
                    let topIdx = -1, bottomIdx = -1;

                    // Search up
                    for (let k = i - 1; k >= 0; k--) {
                        if (matrix[k][j] !== null) {
                            topVal = matrix[k][j];
                            topIdx = k;
                            break;
                        }
                    }

                    // Search down
                    for (let k = i + 1; k < rows; k++) {
                        if (matrix[k][j] !== null) {
                            bottomVal = matrix[k][j];
                            bottomIdx = k;
                            break;
                        }
                    }

                    // Interpolate if we have both top and bottom values
                    if (topVal !== null && bottomVal !== null) {
                        const ratio = (i - topIdx) / (bottomIdx - topIdx);
                        matrix[i][j] = topVal + ratio * (bottomVal - topVal);
                    } else if (topVal !== null) {
                        matrix[i][j] = topVal; // Use top value if no bottom value
                    } else if (bottomVal !== null) {
                        matrix[i][j] = bottomVal; // Use bottom value if no top value
                    }
                }
            }
        }

        // Final pass: fill any remaining nulls with nearest neighbor
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                if (matrix[i][j] === null) {
                    // Find the nearest non-null value in the entire matrix
                    let nearestVal = null;
                    let minDistance = Infinity;

                    for (let ii = 0; ii < rows; ii++) {
                        for (let jj = 0; jj < cols; jj++) {
                            if (matrix[ii][jj] !== null) {
                                const distance = Math.sqrt((i - ii) ** 2 + (j - jj) ** 2);
                                if (distance < minDistance) {
                                    minDistance = distance;
                                    nearestVal = matrix[ii][jj];
                                }
                            }
                        }
                    }

                    if (nearestVal !== null) {
                        matrix[i][j] = nearestVal;
                    }
                }
            }
        }
    }

    countNulls(matrix) {
        let count = 0;
        for (let i = 0; i < matrix.length; i++) {
            for (let j = 0; j < matrix[i].length; j++) {
                if (matrix[i][j] === null) count++;
            }
        }
        return count;
    }

    renderVolatilitySkew(skewData, currentPrice) {
        console.log('🎯 renderVolatilitySkew called - NEW VERSION');
        const chartDiv = document.getElementById('volatility-skew-chart');
        if (!chartDiv) {
            console.error('Volatility skew chart div not found');
            return;
        }

        try {
            console.log('Rendering volatility skew with data:', skewData.length, 'items');
            console.log('Current price:', currentPrice);

            // Filter data based on settings
            const showCalls = document.getElementById('volatility-show-calls')?.checked !== false;
            const showPuts = document.getElementById('volatility-show-puts')?.checked !== false;

            console.log('Settings - Show calls:', showCalls, 'Show puts:', showPuts);

            let filteredData = skewData.filter(item =>
                ((showCalls && item.option_type === 'call') || (showPuts && item.option_type === 'put'))
            );

            console.log('Filtered data:', filteredData.length, 'items');

            if (filteredData.length === 0) {
                console.warn('No data after filtering');
                chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">No volatility skew data available</div>';
                return;
            }

            // Test if Plotly is available
            if (typeof Plotly === 'undefined') {
                console.error('Plotly is not loaded');
                chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">Plotly library not loaded</div>';
                return;
            }

            // Separate calls and puts data
            const callsData = filteredData.filter(item => item.option_type === 'call');
            const putsData = filteredData.filter(item => item.option_type === 'put');

            console.log('Calls data:', callsData.length, 'items');
            console.log('Puts data:', putsData.length, 'items');
            console.log('Sample calls data:', callsData.slice(0, 3));
            console.log('Sample puts data:', putsData.slice(0, 3));

            const traces = [];

            // Add calls trace
            if (showCalls && callsData.length > 0) {
                traces.push({
                    x: callsData.map(item => item.strike),
                    y: callsData.map(item => item.implied_volatility * 100),
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Calls IV',
                    line: { color: '#22c55e', width: 2 },
                    marker: { size: 6, color: '#22c55e' },
                    hovertemplate: 'Strike: $%{x}<br>IV: %{y:.1f}%<br>Type: Call<extra></extra>'
                });
            }

            // Add puts trace
            if (showPuts && putsData.length > 0) {
                traces.push({
                    x: putsData.map(item => item.strike),
                    y: putsData.map(item => item.implied_volatility * 100),
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Puts IV',
                    line: { color: '#ef4444', width: 2 },
                    marker: { size: 6, color: '#ef4444' },
                    hovertemplate: 'Strike: $%{x}<br>IV: %{y:.1f}%<br>Type: Put<extra></extra>'
                });
            }

            // Add current price line
            const yRange = filteredData.map(item => item.implied_volatility * 100);
            const minIV = Math.min(...yRange);
            const maxIV = Math.max(...yRange);

            traces.push({
                x: [currentPrice, currentPrice],
                y: [minIV, maxIV],
                type: 'scatter',
                mode: 'lines',
                name: 'Current Price',
                line: { color: 'white', width: 2, dash: 'dash' },
                hovertemplate: 'Current Price: $%{x}<extra></extra>',
                showlegend: true
            });

            const layout = {
                title: {
                    text: `${this.currentTicker} Volatility Skew - ${this.currentExpiry}`,
                    font: { color: 'white', size: 16 }
                },
                xaxis: {
                    title: 'Strike Price',
                    color: 'white',
                    gridcolor: '#444',
                    tickformat: '$,.0f'
                },
                yaxis: {
                    title: 'Implied Volatility (%)',
                    color: 'white',
                    gridcolor: '#444',
                    tickformat: '.1f'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: { color: 'white' },
                legend: {
                    font: { color: 'white' },
                    bgcolor: 'rgba(0,0,0,0.5)'
                },
                margin: { t: 50, r: 50, b: 50, l: 50 }
            };

            const config = {
                responsive: true,
                displayModeBar: true,
                modeBarButtonsToRemove: ['lasso2d', 'select2d'],
                displaylogo: false
            };

            console.log('Creating Plotly chart with traces:', traces.length);
            console.log('Chart layout:', layout);

            Plotly.newPlot(chartDiv, traces, layout, config).then(() => {
                console.log('✅ Volatility skew chart rendered successfully');
            }).catch((error) => {
                console.error('❌ Error creating Plotly chart:', error);
                chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">Error creating chart</div>';
            });

        } catch (error) {
            console.error('Error rendering volatility skew:', error);
            chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">Error rendering volatility skew</div>';
        }
    }

    updateVolatilityStats(data, currentPrice) {
        try {
            if (!data || data.length === 0) return;

            // Calculate ATM IV (closest to current price)
            const atmData = data.reduce((closest, item) => {
                const currentDiff = Math.abs(item.strike - currentPrice);
                const closestDiff = Math.abs(closest.strike - currentPrice);
                return currentDiff < closestDiff ? item : closest;
            });

            // Calculate IV skew (put IV - call IV at same strike)
            const callsData = data.filter(item => item.option_type === 'call');
            const putsData = data.filter(item => item.option_type === 'put');

            let skewSum = 0;
            let skewCount = 0;

            callsData.forEach(call => {
                const matchingPut = putsData.find(put => Math.abs(put.strike - call.strike) < 0.01);
                if (matchingPut) {
                    skewSum += (matchingPut.implied_volatility - call.implied_volatility) * 100;
                    skewCount++;
                }
            });

            const avgSkew = skewCount > 0 ? skewSum / skewCount : 0;

            // Calculate Put/Call IV ratio
            const avgCallIV = callsData.length > 0 ?
                callsData.reduce((sum, item) => sum + item.implied_volatility, 0) / callsData.length : 0;
            const avgPutIV = putsData.length > 0 ?
                putsData.reduce((sum, item) => sum + item.implied_volatility, 0) / putsData.length : 0;
            const pcRatio = avgCallIV > 0 ? avgPutIV / avgCallIV : 0;

            // Calculate IV range
            const allIVs = data.map(item => item.implied_volatility * 100);
            const minIV = Math.min(...allIVs);
            const maxIV = Math.max(...allIVs);

            // Update UI elements
            document.getElementById('atm-iv').textContent = `${(atmData.implied_volatility * 100).toFixed(1)}%`;
            document.getElementById('iv-skew').textContent = `${avgSkew.toFixed(1)}%`;
            document.getElementById('pc-iv-ratio').textContent = pcRatio.toFixed(2);
            document.getElementById('iv-range').textContent = `${minIV.toFixed(1)}% - ${maxIV.toFixed(1)}%`;
            document.getElementById('term-structure').textContent = 'Normal'; // Placeholder

        } catch (error) {
            console.error('Error updating volatility stats:', error);
        }
    }

    updateVolatilityStatus(message, type) {
        const statusElement = document.getElementById('volatility-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = 'fw-bold';

            switch (type) {
                case 'info':
                    statusElement.className += ' text-info';
                    break;
                case 'success':
                    statusElement.className += ' text-success';
                    break;
                case 'error':
                    statusElement.className += ' text-danger';
                    break;
                default:
                    statusElement.className += ' text-info';
            }
        }
    }

    handleVolatilityViewChange() {
        const viewType = document.getElementById('volatility-view-type')?.value || 'skew';
        const surfaceContainer = document.getElementById('volatility-surface-container');
        const skewContainer = document.getElementById('volatility-skew-container');

        if (!surfaceContainer || !skewContainer) return;

        switch (viewType) {
            case 'surface':
                surfaceContainer.style.display = 'block';
                skewContainer.style.display = 'none';
                surfaceContainer.className = 'col-12';
                // Set full height for surface-only view
                document.getElementById('volatility-surface-chart').style.height = '800px';
                break;
            case 'skew':
                surfaceContainer.style.display = 'none';
                skewContainer.style.display = 'block';
                skewContainer.className = 'col-12';
                // Set full height for skew-only view
                document.getElementById('volatility-skew-chart').style.height = '600px';
                break;
            case 'both':
                surfaceContainer.style.display = 'block';
                skewContainer.style.display = 'block';
                surfaceContainer.className = 'col-12';
                skewContainer.className = 'col-12';
                // Adjust heights for both views
                document.getElementById('volatility-surface-chart').style.height = '600px';
                document.getElementById('volatility-skew-chart').style.height = '500px';
                break;
        }

        // View layout updated - data will auto-refresh when view type changes
        console.log('✅ Volatility view layout updated');
    }

    // Exposure Surface Functions
    async fetchExposureSurface() {
        if (!this.currentTicker || !this.currentExpiry) {
            this.updateExposureSurfaceStatus('Please select ticker and expiry', 'error');
            return;
        }

        try {
            console.log('Starting exposure surface calculation...');
            this.updateExposureSurfaceStatus('Starting exposure surface calculation...', 'info');

            // Load session price data for candlestick overlay
            await this.loadExposureSessionData();

            // Clear existing exposure surface data since we're recalculating
            this.exposureSurfaceData = null;
            this.exposureSurfaceCurrentPrice = null;

            // Get user-selected settings first
            const priceRange = document.getElementById('exposure-surface-price-range')?.value || '5';
            const timeRange = document.getElementById('exposure-surface-time-range')?.value || '120';
            const priceStep = document.getElementById('exposure-surface-price-step')?.value || '1';
            const timeStep = document.getElementById('exposure-surface-time-step')?.value || '3';
            const gexType = document.getElementById('exposure-surface-gex-type')?.value || 'normal';

            // Show detailed loading in chart area with real progress bar
            const chartDiv = document.getElementById('exposure-surface-chart');
            if (chartDiv) {
                chartDiv.innerHTML = `
                    <div id="exposure-surface-loading" style="color: white; text-align: center; padding: 100px; height: 100%;">
                        <div class="spinner-border text-info mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5>Calculating Full Exposure Surface Heatmap</h5>
                        <p>Processing complete exposure matrix across time and price levels...</p>
                        <p class="text-warning">⚠️ This calculation runs in the background</p>
                        <p class="text-muted">Settings: ±${priceRange}% range, ${timeRange}min time, $${priceStep} price steps, ${timeStep}min intervals, ${gexType} exposure</p>

                        <div class="progress mt-3" style="width: 400px; margin: 0 auto; height: 25px;">
                            <div id="exposure-surface-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-info"
                                 role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                0%
                            </div>
                        </div>

                        <p id="exposure-surface-progress-message" class="text-muted mt-2">Initializing calculation...</p>
                        <p class="text-success"><i class="fas fa-check-circle me-1"></i>No timeouts - calculation runs in background</p>
                        ${this.socketIOAvailable ?
                            '<p class="text-info"><i class="fas fa-wifi me-1"></i>Real-time progress updates via WebSocket</p>' :
                            '<p class="text-warning"><i class="fas fa-clock me-1"></i>Progress updates via polling (executable mode)</p>'
                        }
                    </div>
                `;
            }

            // Start the background calculation with user settings
            const response = await fetch(`/api/gamma-surface/${this.currentTicker}/${this.currentExpiry}?price_range=${priceRange}&time_range=${timeRange}&price_step=${priceStep}&time_step=${timeStep}&gex_type=${gexType}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                console.log('Exposure surface calculation started in background');
                this.updateExposureSurfaceStatus('Calculation started in background...', 'info');

                // If SocketIO is not available, start polling for results
                if (!this.socketIOAvailable) {
                    console.log('🔄 SocketIO not available - starting polling for exposure surface results');
                    this.startExposureSurfacePolling();
                } else {
                    console.log('🔌 SocketIO available - waiting for WebSocket updates');
                    // Results will come via WebSocket when complete
                }
            } else {
                console.error('Failed to start exposure surface calculation:', data.error);
                this.updateExposureSurfaceStatus(`Error: ${data.error}`, 'error');
                if (chartDiv) {
                    chartDiv.innerHTML = `<div style="color: white; text-align: center; padding: 50px;">Error: ${data.error}</div>`;
                }
            }
        } catch (error) {
            console.error('Error starting exposure surface calculation:', error);

            let errorMessage = 'Error starting calculation';
            if (error.message) {
                errorMessage = error.message;
            }

            this.updateExposureSurfaceStatus(errorMessage, 'error');

            const chartDiv = document.getElementById('exposure-surface-chart');
            if (chartDiv) {
                chartDiv.innerHTML = `<div style="color: white; text-align: center; padding: 50px;">Error: ${errorMessage}</div>`;
            }
        }
    }

    startExposureSurfacePolling() {
        // Polling mechanism for exposure surface results when SocketIO is not available
        let pollCount = 0;
        const maxPolls = 120; // 2 minutes max (120 * 1 second)

        const pollInterval = setInterval(async () => {
            pollCount++;

            try {
                // Check if calculation is complete by trying to get results
                const response = await fetch(`/api/gamma-surface-status/${this.currentTicker}/${this.currentExpiry}`);
                const data = await response.json();

                if (data.success && data.complete) {
                    // Calculation is complete
                    clearInterval(pollInterval);
                    console.log('✅ Exposure surface calculation completed (via polling)');

                    // Handle the completion
                    this.handleExposureSurfaceComplete({
                        success: true,
                        surface_data: data.surface_data,
                        current_price: data.current_price,
                        data_points: data.surface_data ? data.surface_data.length : 0
                    });

                } else if (data.success && data.progress !== undefined) {
                    // Update progress
                    this.updateExposureSurfaceProgress({
                        progress: data.progress,
                        message: data.message || `Calculating... ${data.progress.toFixed(1)}%`
                    });

                } else if (!data.success && data.error) {
                    // Error occurred
                    clearInterval(pollInterval);
                    console.error('❌ Exposure surface calculation failed (via polling):', data.error);
                    this.handleExposureSurfaceComplete({
                        success: false,
                        error: data.error
                    });
                }

                // Stop polling after max attempts
                if (pollCount >= maxPolls) {
                    clearInterval(pollInterval);
                    console.warn('⏰ Exposure surface polling timeout - stopping');
                    this.updateExposureSurfaceStatus('Calculation timeout - please try again', 'error');
                }

            } catch (error) {
                console.error('Error polling exposure surface status:', error);

                // Check if it's a connection error
                if (error.message && (error.message.includes('Failed to fetch') || error.message.includes('CONNECTION_REFUSED'))) {
                    console.warn('⚠️ Server connection lost during exposure surface calculation');
                    clearInterval(pollInterval);
                    this.updateExposureSurfaceStatus('Server connection lost - calculation may still be running', 'error');

                    // Show user-friendly message
                    const chartDiv = document.getElementById('exposure-surface-chart');
                    if (chartDiv) {
                        chartDiv.innerHTML = `
                            <div style="color: white; text-align: center; padding: 50px;">
                                <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 48px;"></i>
                                <h5>Server Connection Lost</h5>
                                <p>The calculation was very intensive and may have caused the server to become unresponsive.</p>
                                <p class="text-info">Try using smaller calculation settings:</p>
                                <ul class="text-left" style="max-width: 400px; margin: 0 auto;">
                                    <li>Reduce price range (try ±10% instead of ±20%)</li>
                                    <li>Increase price step (try $2 instead of $1)</li>
                                    <li>Reduce time range (try 120min instead of 405min)</li>
                                </ul>
                                <button class="btn btn-outline-light btn-sm mt-3" onclick="location.reload()">
                                    <i class="fas fa-redo me-1"></i>Reload Dashboard
                                </button>
                            </div>
                        `;
                    }
                    return;
                }

                // Continue polling unless we've exceeded max attempts
                if (pollCount >= maxPolls) {
                    clearInterval(pollInterval);
                    this.updateExposureSurfaceStatus('Polling timeout - please try again', 'error');
                }
            }
        }, 1000); // Poll every 1 second
    }

    updateExposureSurfaceProgress(data) {
        // Update progress bar and message during exposure surface calculation
        const progressBar = document.getElementById('exposure-surface-progress-bar');
        const progressMessage = document.getElementById('exposure-surface-progress-message');

        if (progressBar && progressMessage) {
            const progress = Math.max(0, Math.min(100, data.progress));

            if (progress >= 0) {
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                progressBar.textContent = `${progress.toFixed(1)}%`;
                progressMessage.textContent = data.message || 'Calculating...';

                // Change color based on progress
                if (progress < 25) {
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                } else if (progress < 50) {
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-warning';
                } else if (progress < 75) {
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-primary';
                } else if (progress < 100) {
                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-success';
                } else {
                    progressBar.className = 'progress-bar bg-success';
                    progressBar.textContent = '100% Complete!';
                }
            } else {
                // Error state
                progressBar.className = 'progress-bar bg-danger';
                progressBar.style.width = '100%';
                progressBar.textContent = 'Error';
                progressMessage.textContent = data.message || 'Calculation failed';
            }
        }

        // Update status as well
        if (data.progress >= 100) {
            this.updateExposureSurfaceStatus('Calculation complete, rendering chart...', 'success');
        } else if (data.progress >= 0) {
            this.updateExposureSurfaceStatus(`Progress: ${data.progress.toFixed(1)}%`, 'info');
        } else {
            this.updateExposureSurfaceStatus('Calculation failed', 'error');
        }
    }

    showExposureSurfaceInstructions() {
        // Show instructions when exposure surface tab is opened
        const chartDiv = document.getElementById('exposure-surface-chart');
        if (chartDiv) {
            chartDiv.innerHTML = `
                <div style="color: white; text-align: center; padding: 100px; height: 100%;">
                    <div class="mb-4">
                        <i class="fas fa-chart-area text-info" style="font-size: 64px;"></i>
                    </div>
                    <h4>Exposure Surface Heatmap</h4>
                    <p class="text-muted mb-4">Professional-grade exposure analysis across time and price levels</p>

                    <div class="alert alert-info mx-auto" style="max-width: 600px; text-align: left;">
                        <h6><i class="fas fa-info-circle me-2"></i>How to Use:</h6>
                        <ol class="mb-0">
                            <li><strong>Select your settings</strong> above (price range, time range, price steps)</li>
                            <li><strong>Click the "Refresh" button</strong> to start the calculation</li>
                            <li><strong>Wait for the calculation</strong> to complete (30 seconds to 2 minutes)</li>
                            <li><strong>Analyze the heatmap</strong> - Red = negative exposure, Green = positive exposure</li>
                        </ol>
                    </div>

                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg" onclick="dashboard.fetchExposureSurface()">
                            <i class="fas fa-play me-2"></i>
                            Calculate Exposure Surface
                        </button>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Calculation time depends on your settings - larger ranges and smaller steps take longer
                        </small>
                    </div>
                </div>
            `;
        }

        // Update status
        this.updateExposureSurfaceStatus('Ready to calculate - click refresh button', 'info');
    }

    handleExposureSurfaceComplete(data) {
        // Handle completion of exposure surface calculation
        console.log('Exposure surface calculation completed:', data);

        if (data.success) {
            // Store the exposure surface data so it persists when switching tabs
            this.exposureSurfaceData = data.surface_data;
            this.exposureSurfaceCurrentPrice = data.current_price;

            // Render the completed exposure surface
            this.renderExposureSurface(data.surface_data, data.current_price);
            this.updateExposureSurfaceStats(data.surface_data, data.current_price);
            this.updateExposureSurfaceStatus(`Ready (${data.data_points || data.surface_data.length} points)`, 'success');
        } else {
            // Handle error
            console.error('Exposure surface calculation failed:', data.error);
            this.updateExposureSurfaceStatus(`Error: ${data.error}`, 'error');

            const chartDiv = document.getElementById('exposure-surface-chart');
            if (chartDiv) {
                chartDiv.innerHTML = `
                    <div style="color: white; text-align: center; padding: 50px;">
                        <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 48px;"></i>
                        <h5>Calculation Failed</h5>
                        <p>Error: ${data.error}</p>
                        <button class="btn btn-outline-light btn-sm" onclick="dashboard.fetchExposureSurface()">
                            <i class="fas fa-redo me-1"></i>Try Again
                        </button>
                    </div>
                `;
            }
        }
    }

    renderExposureSurface(surfaceData, currentPrice) {
        const chartDiv = document.getElementById('exposure-surface-chart');
        if (!chartDiv) return;

        try {
            console.log('Rendering exposure surface with', surfaceData.length, 'data points');

            if (surfaceData.length === 0) {
                chartDiv.innerHTML = '<div style="color: white; text-align: center; padding: 50px;">No exposure surface data available</div>';
                return;
            }

            // Log dataset size
            console.log(`📊 Processing dataset: ${surfaceData.length.toLocaleString()} data points`);

            // Get selected view type and show options
            const viewType = document.getElementById('exposure-surface-view-type')?.value || 'heatmap';
            const showCalls = document.getElementById('exposure-surface-show-calls')?.checked || false;
            const showPuts = document.getElementById('exposure-surface-show-puts')?.checked || false;
            const showNet = document.getElementById('exposure-surface-show-net')?.checked || false;

            console.log('Rendering exposure surface as:', viewType, 'showing:', {calls: showCalls, puts: showPuts, net: showNet});

            // Check if at least one gamma type is selected
            if (!showCalls && !showPuts && !showNet) {
                console.log('No gamma types selected - clearing chart');
                Plotly.newPlot(chartDiv, [], {
                    title: {
                        text: 'Select at least one gamma type to display',
                        font: { color: 'white', size: 16 }
                    },
                    paper_bgcolor: 'rgba(0,0,0,0)',
                    plot_bgcolor: 'rgba(0,0,0,0)',
                    font: { color: 'white' }
                });
                return;
            }

            // Extract unique price levels and time points, filtering out empty periods
            const priceLevels = [...new Set(surfaceData.map(d => d.price_level))].sort((a, b) => b - a); // High to low

            // Filter time points to remove periods with no meaningful gamma data
            const timePointsWithData = this.filterTimePointsWithData(surfaceData);
            const timePoints = timePointsWithData.sort((a, b) => b - a); // High to low

            console.log(`📊 Matrix dimensions: ${priceLevels.length} × ${timePoints.length} = ${(priceLevels.length * timePoints.length).toLocaleString()} cells`);

            // Use optimized processing for any size dataset
            const matrixSize = priceLevels.length * timePoints.length;
            console.log(`🚀 Optimizing matrix creation for ${matrixSize.toLocaleString()} cells`);

            // Create Z matrices for each gamma type that's enabled
            const matrices = {};

            if (showCalls) {
                matrices.calls = this.createGammaMatrix(surfaceData, priceLevels, timePoints, 'call_gamma_exposure');
            }
            if (showPuts) {
                matrices.puts = this.createGammaMatrix(surfaceData, priceLevels, timePoints, 'put_gamma_exposure');
            }
            if (showNet) {
                matrices.net = this.createGammaMatrix(surfaceData, priceLevels, timePoints, 'net_gamma_exposure');
            }

            // Fetch historical price data for the charts
            this.fetchExposureSurfacePriceData(timePoints).then(priceData => {
                if (viewType === 'surface') {
                    this.render3DExposureSurface(matrices, priceLevels, timePoints, currentPrice, chartDiv, {showCalls, showPuts, showNet}, priceData);
                } else {
                    this.render2DExposureHeatmap(matrices, priceLevels, timePoints, currentPrice, chartDiv, {showCalls, showPuts, showNet}, priceData);
                }
            }).catch(error => {
                console.warn('Could not fetch price data for exposure surface:', error);
                // Render without price data if fetch fails
                if (viewType === 'surface') {
                    this.render3DExposureSurface(matrices, priceLevels, timePoints, currentPrice, chartDiv, {showCalls, showPuts, showNet});
                } else {
                    this.render2DExposureHeatmap(matrices, priceLevels, timePoints, currentPrice, chartDiv, {showCalls, showPuts, showNet});
                }
            });

        } catch (error) {
            console.error('Error rendering exposure surface:', error);
            chartDiv.innerHTML = `
                <div style="color: white; text-align: center; padding: 100px;">
                    <h5>Error Rendering Exposure Surface</h5>
                    <p class="text-danger">${error.message}</p>
                    <button class="btn btn-primary" onclick="dashboard.fetchExposureSurface()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Try Again
                    </button>
                </div>
            `;
        }
    }

    async fetchExposureSurfacePriceData(timePoints) {
        if (!this.currentTicker || timePoints.length === 0) {
            return null;
        }

        try {
            // Get current exposure surface settings
            const timeRange = parseInt(document.getElementById('exposure-surface-time-range')?.value || 120);
            const timeStep = parseInt(document.getElementById('exposure-surface-time-step')?.value || 3);

            console.log(`📈 Fetching price data for exposure surface: ${this.currentTicker}, time range: ${timeRange}min, step: ${timeStep}min`);

            const response = await fetch(`/api/gamma-surface-price-data/${this.currentTicker}?time_range=${timeRange}&time_step=${timeStep}`);
            const data = await response.json();

            if (data.success) {
                console.log(`📈 Price data fetched: ${data.data_points} data points`);
                return data;
            } else {
                console.warn('Failed to fetch price data:', data.error);
                return null;
            }
        } catch (error) {
            console.error('Error fetching exposure surface price data:', error);
            return null;
        }
    }

    alignPriceDataWithTimePoints(candlestickData, timePoints) {
        if (!candlestickData || candlestickData.length === 0 || !timePoints || timePoints.length === 0) {
            return [];
        }

        console.log(`📈 Aligning ${candlestickData.length} price data points with ${timePoints.length} time points`);

        // Sort time points in descending order (highest to lowest minutes remaining)
        const sortedTimePoints = [...timePoints].sort((a, b) => b - a);

        // Take the most recent price data points to match the number of time points
        const recentPriceData = candlestickData.slice(-sortedTimePoints.length);

        // Align price data with time points
        // Most recent price data (last element) should align with smallest time remaining (last in sorted array)
        const alignedData = [];

        for (let i = 0; i < Math.min(recentPriceData.length, sortedTimePoints.length); i++) {
            const priceIndex = recentPriceData.length - 1 - i;  // Start from most recent
            const timeIndex = sortedTimePoints.length - 1 - i;  // Start from smallest time remaining

            if (priceIndex >= 0 && timeIndex >= 0) {
                alignedData.push({
                    timePoint: sortedTimePoints[timeIndex],
                    open: recentPriceData[priceIndex].open,
                    high: recentPriceData[priceIndex].high,
                    low: recentPriceData[priceIndex].low,
                    close: recentPriceData[priceIndex].close,
                    volume: recentPriceData[priceIndex].volume,
                    timestamp: recentPriceData[priceIndex].timestamp
                });
            }
        }

        console.log(`📈 Aligned ${alignedData.length} price data points with time points`);
        return alignedData;
    }

    filterTimePointsWithData(surfaceData) {
        console.log('🔍 Filtering time points to remove whitespace periods');

        // Group data by time point and check for meaningful gamma values
        const timePointData = new Map();

        for (const dataPoint of surfaceData) {
            const timePoint = dataPoint.minutes_remaining;
            if (!timePointData.has(timePoint)) {
                timePointData.set(timePoint, {
                    maxCallGamma: 0,
                    maxPutGamma: 0,
                    maxNetGamma: 0,
                    totalAbsGamma: 0
                });
            }

            const timeData = timePointData.get(timePoint);
            const callGamma = Math.abs(dataPoint.call_gamma_exposure || 0);
            const putGamma = Math.abs(dataPoint.put_gamma_exposure || 0);
            const netGamma = Math.abs(dataPoint.net_gamma_exposure || 0);

            timeData.maxCallGamma = Math.max(timeData.maxCallGamma, callGamma);
            timeData.maxPutGamma = Math.max(timeData.maxPutGamma, putGamma);
            timeData.maxNetGamma = Math.max(timeData.maxNetGamma, netGamma);
            timeData.totalAbsGamma += callGamma + putGamma + netGamma;
        }

        // Filter out time points with minimal gamma activity
        const filteredTimePoints = [];
        const minGammaThreshold = 1000; // Minimum gamma exposure to consider meaningful

        for (const [timePoint, data] of timePointData.entries()) {
            const hasSignificantGamma =
                data.maxCallGamma > minGammaThreshold ||
                data.maxPutGamma > minGammaThreshold ||
                data.maxNetGamma > minGammaThreshold ||
                data.totalAbsGamma > minGammaThreshold * 5;

            if (hasSignificantGamma) {
                filteredTimePoints.push(timePoint);
            }
        }

        console.log(`📊 Filtered time points: ${timePointData.size} → ${filteredTimePoints.length} (removed ${timePointData.size - filteredTimePoints.length} empty periods)`);

        return filteredTimePoints;
    }

    createGammaMatrix(surfaceData, priceLevels, timePoints, gammaField) {
        console.log(`📊 Creating matrix: ${priceLevels.length} x ${timePoints.length} = ${(priceLevels.length * timePoints.length).toLocaleString()} cells`);

        // Create a lookup map for fast access
        const dataMap = new Map();
        for (const dataPoint of surfaceData) {
            const key = `${dataPoint.price_level}_${dataPoint.minutes_remaining}`;
            dataMap.set(key, dataPoint[gammaField] || 0);
        }

        // Create matrix using the lookup map
        const zMatrix = [];
        for (let i = 0; i < priceLevels.length; i++) {
            const row = [];
            const priceLevel = priceLevels[i];

            for (let j = 0; j < timePoints.length; j++) {
                const timePoint = timePoints[j];
                const key = `${priceLevel}_${timePoint}`;
                row.push(dataMap.get(key) || 0);
            }
            zMatrix.push(row);
        }

        console.log(`✅ Matrix created: ${zMatrix.length} rows x ${zMatrix[0]?.length || 0} columns`);
        return zMatrix;
    }

    render2DExposureHeatmap(matrices, priceLevels, timePoints, currentPrice, chartDiv, showOptions, priceData = null) {
        const traces = [];

        // Get CSS colors for consistency
        const colors = this.getCSSColors();

        // Apply interpolation and smoothing to matrices for a smoother, 3D-like appearance
        console.log('🎨 Applying interpolation and smoothing to 2D exposure surface...');
        const smoothedMatrices = this.smoothExposureMatrices(matrices, priceLevels, timePoints);

        // Add session price data candlestick chart if available
        if (this.exposureSessionData.candlestickData.length > 0) {
            console.log('📈 Adding session price candlestick chart to 2D exposure surface');
            console.log(`📈 Session data: ${this.exposureSessionData.candlestickData.length} points`);

            // Calculate minutes until expiration for each session data point
            // Get expiry date from current expiry selection
            const expiryDate = new Date(this.currentExpiry);

            const sessionTimePoints = this.exposureSessionData.timestamps.map(timestamp => {
                // Calculate minutes until expiration from each timestamp
                const minutesUntilExpiry = Math.max(0, (expiryDate - timestamp) / (1000 * 60));
                return Math.round(minutesUntilExpiry);
            });

            const candlestickTrace = {
                type: 'candlestick',
                x: sessionTimePoints,
                open: this.exposureSessionData.candlestickData.map(d => d.open),
                high: this.exposureSessionData.candlestickData.map(d => d.high),
                low: this.exposureSessionData.candlestickData.map(d => d.low),
                close: this.exposureSessionData.candlestickData.map(d => d.close),
                name: 'Session Price Movement',
                increasing: { line: { color: colors.callColor } },
                decreasing: { line: { color: colors.putColor } },
                hovertemplate:
                    'Minutes Until Expiry: %{x}<br>' +
                    'Open: $%{open}<br>' +
                    'High: $%{high}<br>' +
                    'Low: $%{low}<br>' +
                    'Close: $%{close}<br>' +
                    '<extra></extra>'
            };

            traces.push(candlestickTrace);
        } else if (priceData && priceData.candlestick_data && priceData.candlestick_data.length > 0) {
            // Fallback to historical data if no live recording is available
            console.log('📈 Adding historical candlestick chart to 2D exposure surface (fallback)');
            console.log(`📈 Historical data: ${priceData.candlestick_data.length} points, interval: ${priceData.actual_interval}, period: ${priceData.period_used}`);

            // Align price data with gamma surface time points
            const alignedCandlestickData = this.alignPriceDataWithTimePoints(priceData.candlestick_data, timePoints);

            if (alignedCandlestickData.length > 0) {
                const candlestickTrace = {
                    type: 'candlestick',
                    x: alignedCandlestickData.map(d => d.timePoint),
                    open: alignedCandlestickData.map(d => d.open),
                    high: alignedCandlestickData.map(d => d.high),
                    low: alignedCandlestickData.map(d => d.low),
                    close: alignedCandlestickData.map(d => d.close),
                    name: 'Historical Price Data',
                    increasing: { line: { color: colors.callColor } },
                    decreasing: { line: { color: colors.putColor } },
                    hovertemplate:
                        'Minutes Until Expiry: %{x}<br>' +
                        'Open: $%{open}<br>' +
                        'High: $%{high}<br>' +
                        'Low: $%{low}<br>' +
                        'Close: $%{close}<br>' +
                        '<extra></extra>'
                };

                traces.push(candlestickTrace);
            }
        } else {
            console.warn('📈 No price data available for candlestick chart');
        }

        // Add Call Gamma heatmap
        if (showOptions.showCalls && smoothedMatrices.calls) {
            const callValues = smoothedMatrices.calls.flat();
            const maxCallValue = Math.max(...callValues);

            traces.push({
                type: 'heatmap',
                x: timePoints,
                y: priceLevels,
                z: smoothedMatrices.calls,
                name: 'Call Gamma',
                zmin: 0,
                zmax: maxCallValue,
                colorscale: [
                    [0, '#FFFFFF'],      // White for zero
                    [0.2, '#90EE90'],    // Light green
                    [0.4, '#00FF00'],    // Green
                    [0.6, '#32CD32'],    // Lime green
                    [0.8, '#228B22'],    // Forest green
                    [1, '#006400']       // Dark green for maximum
                ],
                showscale: true,
                colorbar: {
                    title: 'Call Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.85
                },
                hoverongaps: false,
                connectgaps: true,  // Fill gaps for smoother appearance
                zsmooth: 'fast',    // Enable moderate Plotly smoothing for gradient-like appearance
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Call Gamma: %{z:.2s}<br>' +
                    '<extra></extra>'
            });
        }

        // Add Put Gamma heatmap (puts are negative values, displayed below zero)
        if (showOptions.showPuts && smoothedMatrices.puts) {
            const putValues = smoothedMatrices.puts.flat();
            const minPutValue = Math.min(...putValues);  // Should be negative

            traces.push({
                type: 'heatmap',
                x: timePoints,
                y: priceLevels,
                z: smoothedMatrices.puts,
                name: 'Put Gamma',
                zmin: minPutValue,  // Negative minimum
                zmax: 0,            // Zero maximum
                colorscale: [
                    [0, '#8B0000'],      // Dark red for most negative
                    [0.2, '#DC143C'],    // Crimson
                    [0.4, '#FF0000'],    // Red
                    [0.6, '#FF6B6B'],    // Light red
                    [0.8, '#FFB6C1'],    // Very light red
                    [1, '#FFFFFF']       // White for zero
                ],
                showscale: true,
                colorbar: {
                    title: 'Put Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.5
                },
                hoverongaps: false,
                connectgaps: true,  // Fill gaps for smoother appearance
                zsmooth: 'fast',    // Enable moderate Plotly smoothing for gradient-like appearance
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Put Gamma: %{z:.2s}<br>' +
                    '<extra></extra>'
            });
        }

        // Add Net Gamma heatmap
        if (showOptions.showNet && smoothedMatrices.net) {
            const netValues = smoothedMatrices.net.flat();
            const minValue = Math.min(...netValues);
            const maxValue = Math.max(...netValues);
            const maxAbsValue = Math.max(Math.abs(minValue), Math.abs(maxValue));

            traces.push({
                type: 'heatmap',
                x: timePoints,
                y: priceLevels,
                z: smoothedMatrices.net,
                name: 'Net Gamma',
                zmin: -maxAbsValue,
                zmax: maxAbsValue,
                colorscale: [
                    [0, '#8B0000'],      // Dark red for very negative
                    [0.25, '#FF0000'],   // Red for negative
                    [0.45, '#FF6B6B'],   // Light red
                    [0.5, '#FFFFFF'],    // White for zero (exactly at center)
                    [0.55, '#90EE90'],   // Light green
                    [0.75, '#00FF00'],   // Green for positive
                    [1, '#006400']       // Dark green for very positive
                ],
                showscale: true,
                colorbar: {
                    title: 'Net Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.15
                },
                hoverongaps: false,
                connectgaps: true,  // Fill gaps for smoother appearance
                zsmooth: 'fast',    // Enable moderate Plotly smoothing for gradient-like appearance
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Net Gamma: %{z:.2s}<br>' +
                    '<extra></extra>'
            });

            // All contour lines removed per user request - clean exposure surface visualization
        }

        // Add current price line
        const currentPriceLine = {
            type: 'scatter',
            x: timePoints,
            y: Array(timePoints.length).fill(currentPrice),
            mode: 'lines',
            name: 'Current Price',
            line: {
                color: 'yellow',
                width: 3,
                dash: 'dash'
            },
            hovertemplate: 'Current Price: $%{y}<extra></extra>'
        };

        // Add current price line to traces
        traces.push(currentPriceLine);

        // Create title based on what's being shown
        let titleParts = [];
        if (showOptions.showCalls) titleParts.push('Calls');
        if (showOptions.showPuts) titleParts.push('Puts');
        if (showOptions.showNet) titleParts.push('Net');
        const titleSuffix = titleParts.length > 0 ? titleParts.join(' + ') : 'Gamma';

        const layout = {
            title: {
                text: `${this.currentTicker} ${titleSuffix} Exposure Surface - ${this.currentExpiry}${priceData ? ' with Price History' : ''}`,
                font: { color: 'white', size: 16 }
            },
            xaxis: {
                title: 'Minutes Until Expiration',
                color: 'white',
                gridcolor: '#444',
                autorange: 'reversed',  // Show time countdown from left to right
                rangeslider: { visible: false }  // Disable range slider to prevent contours appearing there
            },
            yaxis: {
                title: 'Price Level ($)',
                color: 'white',
                gridcolor: '#444',
                tickformat: '$,.0f'
            },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: 'white' },
            margin: { t: 50, r: 120, b: 50, l: 50 }  // Extra right margin for multiple color bars
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartDiv, traces, layout, config);
    }

    render3DExposureSurface(matrices, priceLevels, timePoints, currentPrice, chartDiv, showOptions, priceData = null) {
        const traces = [];

        // Add line chart for historical price data if available
        if (priceData && priceData.line_data && priceData.line_data.length > 0) {
            console.log('📈 Adding price line to 3D exposure surface');

            // Align price data with gamma surface time points
            const alignedLineData = this.alignPriceDataWithTimePoints(
                priceData.line_data.map(d => ({
                    open: d.price,
                    high: d.price,
                    low: d.price,
                    close: d.price,
                    timestamp: d.timestamp
                })),
                timePoints
            );

            if (alignedLineData.length > 0) {
                const priceLineTrace = {
                    type: 'scatter3d',
                    mode: 'lines+markers',
                    x: alignedLineData.map(d => d.timePoint),
                    y: alignedLineData.map(d => d.close),
                    z: Array(alignedLineData.length).fill(0), // Place line at z=0 level
                    name: 'Price History',
                    line: {
                        color: '#FFFF00',  // Bright yellow for visibility
                        width: 6
                    },
                    marker: {
                        color: '#FFFF00',
                        size: 4
                    },
                    hovertemplate:
                        'Time: %{x} min<br>' +
                        'Price: $%{y}<br>' +
                        '<extra></extra>'
                };

                traces.push(priceLineTrace);
            }
        }

        // Add Call Gamma surface
        if (showOptions.showCalls && matrices.calls) {
            const callValues = matrices.calls.flat();
            const maxCallValue = Math.max(...callValues);

            traces.push({
                type: 'surface',
                x: timePoints,
                y: priceLevels,
                z: matrices.calls,
                name: 'Call Gamma',
                cmin: 0,
                cmax: maxCallValue,
                colorscale: [
                    [0, '#FFFFFF'],      // White for zero
                    [0.2, '#90EE90'],    // Light green
                    [0.4, '#00FF00'],    // Green
                    [0.6, '#32CD32'],    // Lime green
                    [0.8, '#228B22'],    // Forest green
                    [1, '#006400']       // Dark green for maximum
                ],
                showscale: true,
                colorbar: {
                    title: 'Call Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.85
                },
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Call Gamma: %{z:.2s}<br>' +
                    '<extra></extra>',
                contours: {
                    z: {
                        show: false,  // Disable automatic contour lines
                        usecolormap: false,
                        project: { z: false }
                    }
                },
                opacity: 1.0,  // Solid, no transparency
                lighting: {
                    ambient: 1.0,      // Full ambient lighting (no shadows)
                    diffuse: 0.0,      // No diffuse lighting (removes 3D shading)
                    specular: 0.0,     // No specular highlights
                    roughness: 1.0,    // Maximum roughness (flat appearance)
                    fresnel: 0.0       // No fresnel effects
                }
            });
        }

        // Add Put Gamma surface (puts are negative values, displayed below zero)
        if (showOptions.showPuts && matrices.puts) {
            const putValues = matrices.puts.flat();
            const minPutValue = Math.min(...putValues);  // Should be negative
            const maxPutValue = Math.max(...putValues);  // Should be zero or close to zero

            traces.push({
                type: 'surface',
                x: timePoints,
                y: priceLevels,
                z: matrices.puts,
                name: 'Put Gamma',
                cmin: minPutValue,  // Negative minimum
                cmax: 0,            // Zero maximum
                colorscale: [
                    [0, '#8B0000'],      // Dark red for most negative
                    [0.2, '#DC143C'],    // Crimson
                    [0.4, '#FF0000'],    // Red
                    [0.6, '#FF6B6B'],    // Light red
                    [0.8, '#FFB6C1'],    // Very light red
                    [1, '#FFFFFF']       // White for zero
                ],
                showscale: true,
                colorbar: {
                    title: 'Put Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.5
                },
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Put Gamma: %{z:.2s}<br>' +
                    '<extra></extra>',
                contours: {
                    z: {
                        show: false,  // Disable automatic contour lines
                        usecolormap: false,
                        project: { z: false }
                    }
                },
                opacity: 1.0,  // Solid, no transparency
                lighting: {
                    ambient: 1.0,      // Full ambient lighting (no shadows)
                    diffuse: 0.0,      // No diffuse lighting (removes 3D shading)
                    specular: 0.0,     // No specular highlights
                    roughness: 1.0,    // Maximum roughness (flat appearance)
                    fresnel: 0.0       // No fresnel effects
                }
            });
        }

        // Add Net Gamma surface
        if (showOptions.showNet && matrices.net) {
            const netValues = matrices.net.flat();
            const minValue = Math.min(...netValues);
            const maxValue = Math.max(...netValues);
            const maxAbsValue = Math.max(Math.abs(minValue), Math.abs(maxValue));

            traces.push({
                type: 'surface',
                x: timePoints,
                y: priceLevels,
                z: matrices.net,
                name: 'Net Gamma',
                cmin: -maxAbsValue,
                cmax: maxAbsValue,
                colorscale: [
                    [0, '#8B0000'],      // Dark red for very negative
                    [0.25, '#FF0000'],   // Red for negative
                    [0.45, '#FF6B6B'],   // Light red
                    [0.5, '#FFFFFF'],    // White for zero (exactly at center)
                    [0.55, '#90EE90'],   // Light green
                    [0.75, '#00FF00'],   // Green for positive
                    [1, '#006400']       // Dark green for very positive
                ],
                showscale: true,
                colorbar: {
                    title: 'Net Gamma',
                    titleside: 'right',
                    tickformat: '.2s',
                    x: 1.02,
                    len: 0.3,
                    y: 0.15
                },
                hovertemplate:
                    'Time: %{x} min<br>' +
                    'Price: $%{y}<br>' +
                    'Net Gamma: %{z:.2s}<br>' +
                    '<extra></extra>',
                contours: {
                    z: {
                        show: false,  // Disable automatic contour lines
                        usecolormap: false,
                        project: { z: false }
                    }
                },
                opacity: 1.0,  // Solid, no transparency
                lighting: {
                    ambient: 1.0,      // Full ambient lighting (no shadows)
                    diffuse: 0.0,      // No diffuse lighting (removes 3D shading)
                    specular: 0.0,     // No specular highlights
                    roughness: 1.0,    // Maximum roughness (flat appearance)
                    fresnel: 0.0       // No fresnel effects
                }
            });
        }

        // Create title based on what's being shown
        let titleParts = [];
        if (showOptions.showCalls) titleParts.push('Calls');
        if (showOptions.showPuts) titleParts.push('Puts');
        if (showOptions.showNet) titleParts.push('Net');
        const titleSuffix = titleParts.length > 0 ? titleParts.join(' + ') : 'Gamma';

        const layout = {
            title: {
                text: `${this.currentTicker} 3D ${titleSuffix} Exposure Surface - ${this.currentExpiry}${priceData ? ' with Price History' : ''}`,
                font: { color: 'white', size: 16 }
            },
            scene: {
                xaxis: {
                    title: 'Minutes Until Expiration',
                    titlefont: { color: 'white' },
                    tickfont: { color: 'white' },
                    gridcolor: '#444',
                    showgrid: true,
                    backgroundcolor: 'rgba(0,0,0,0)',
                    autorange: 'reversed'
                },
                yaxis: {
                    title: 'Price Level ($)',
                    titlefont: { color: 'white' },
                    tickfont: { color: 'white' },
                    gridcolor: '#444',
                    showgrid: true,
                    backgroundcolor: 'rgba(0,0,0,0)',
                    tickformat: '$,.0f'
                },
                zaxis: {
                    title: 'Gamma Exposure',
                    titlefont: { color: 'white' },
                    tickfont: { color: 'white' },
                    gridcolor: '#444',
                    showgrid: true,
                    backgroundcolor: 'rgba(0,0,0,0)',
                    tickformat: '.2s'
                },
                bgcolor: 'rgba(0,0,0,0)',
                camera: {
                    eye: { x: 1.5, y: 1.5, z: 1.5 }
                }
            },
            paper_bgcolor: 'rgba(0,0,0,0)',
            plot_bgcolor: 'rgba(0,0,0,0)',
            font: { color: 'white' },
            margin: { t: 50, r: 120, b: 50, l: 50 }  // Extra right margin for multiple color bars
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartDiv, traces, layout, config);
    }

    smoothExposureMatrices(matrices, priceLevels, timePoints) {
        console.log('🎨 Applying smoothing to exposure matrices...');

        const smoothedMatrices = {};

        // Apply smoothing to each matrix type
        ['calls', 'puts', 'net'].forEach(matrixType => {
            if (matrices[matrixType]) {
                console.log(`📊 Smoothing ${matrixType} matrix...`);

                // Create a deep copy of the matrix
                const originalMatrix = matrices[matrixType].map(row => [...row]);

                // Apply interpolation to fill any gaps
                this.interpolateExposureMatrix(originalMatrix);

                // Apply moderate smoothing for gradient-like appearance without blur
                // Use Plotly's built-in smoothing primarily, with light Gaussian smoothing for data preparation
                const smoothedMatrix = this.applyGaussianSmoothing(originalMatrix, 0.8); // Light smoothing to preserve detail

                smoothedMatrices[matrixType] = smoothedMatrix;

                console.log(`✅ ${matrixType} matrix smoothed: ${smoothedMatrix.length}x${smoothedMatrix[0]?.length || 0}`);
            }
        });

        return smoothedMatrices;
    }

    interpolateExposureMatrix(matrix) {
        // Enhanced interpolation specifically for exposure matrices
        // Similar to the existing interpolateMatrix but optimized for exposure data
        const rows = matrix.length;
        const cols = matrix[0] ? matrix[0].length : 0;

        if (rows === 0 || cols === 0) return;

        // First pass: interpolate horizontally (across time for each price level)
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                if (matrix[i][j] === null || matrix[i][j] === undefined || isNaN(matrix[i][j])) {
                    // Find nearest non-null values to the left and right
                    let leftVal = null, rightVal = null;
                    let leftIdx = -1, rightIdx = -1;

                    // Search left
                    for (let k = j - 1; k >= 0; k--) {
                        if (matrix[i][k] !== null && matrix[i][k] !== undefined && !isNaN(matrix[i][k])) {
                            leftVal = matrix[i][k];
                            leftIdx = k;
                            break;
                        }
                    }

                    // Search right
                    for (let k = j + 1; k < cols; k++) {
                        if (matrix[i][k] !== null && matrix[i][k] !== undefined && !isNaN(matrix[i][k])) {
                            rightVal = matrix[i][k];
                            rightIdx = k;
                            break;
                        }
                    }

                    // Interpolate if we have both left and right values
                    if (leftVal !== null && rightVal !== null) {
                        const ratio = (j - leftIdx) / (rightIdx - leftIdx);
                        matrix[i][j] = leftVal + ratio * (rightVal - leftVal);
                    } else if (leftVal !== null) {
                        matrix[i][j] = leftVal; // Use left value if no right value
                    } else if (rightVal !== null) {
                        matrix[i][j] = rightVal; // Use right value if no left value
                    } else {
                        matrix[i][j] = 0; // Default to zero if no values found
                    }
                }
            }
        }

        // Second pass: interpolate vertically (across price levels for each time point)
        for (let j = 0; j < cols; j++) {
            for (let i = 0; i < rows; i++) {
                if (matrix[i][j] === null || matrix[i][j] === undefined || isNaN(matrix[i][j])) {
                    // Find nearest non-null values above and below
                    let topVal = null, bottomVal = null;
                    let topIdx = -1, bottomIdx = -1;

                    // Search up
                    for (let k = i - 1; k >= 0; k--) {
                        if (matrix[k][j] !== null && matrix[k][j] !== undefined && !isNaN(matrix[k][j])) {
                            topVal = matrix[k][j];
                            topIdx = k;
                            break;
                        }
                    }

                    // Search down
                    for (let k = i + 1; k < rows; k++) {
                        if (matrix[k][j] !== null && matrix[k][j] !== undefined && !isNaN(matrix[k][j])) {
                            bottomVal = matrix[k][j];
                            bottomIdx = k;
                            break;
                        }
                    }

                    // Interpolate if we have both top and bottom values
                    if (topVal !== null && bottomVal !== null) {
                        const ratio = (i - topIdx) / (bottomIdx - topIdx);
                        matrix[i][j] = topVal + ratio * (bottomVal - topVal);
                    } else if (topVal !== null) {
                        matrix[i][j] = topVal; // Use top value if no bottom value
                    } else if (bottomVal !== null) {
                        matrix[i][j] = bottomVal; // Use bottom value if no top value
                    } else {
                        matrix[i][j] = 0; // Default to zero if no values found
                    }
                }
            }
        }
    }

    applyGaussianSmoothing(matrix, sigma = 1.0) {
        // Apply Gaussian smoothing to create a more polished, 3D-like appearance
        const rows = matrix.length;
        const cols = matrix[0] ? matrix[0].length : 0;

        if (rows === 0 || cols === 0) return matrix;

        // Create Gaussian kernel
        const kernelSize = Math.max(3, Math.ceil(sigma * 3) * 2 + 1); // Ensure odd size
        const kernel = this.createGaussianKernel(kernelSize, sigma);

        // Apply convolution
        const smoothedMatrix = [];
        const halfKernel = Math.floor(kernelSize / 2);

        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                let sum = 0;
                let weightSum = 0;

                // Apply kernel
                for (let ki = -halfKernel; ki <= halfKernel; ki++) {
                    for (let kj = -halfKernel; kj <= halfKernel; kj++) {
                        const ni = i + ki;
                        const nj = j + kj;

                        // Check bounds
                        if (ni >= 0 && ni < rows && nj >= 0 && nj < cols) {
                            const weight = kernel[ki + halfKernel][kj + halfKernel];
                            const value = matrix[ni][nj];

                            if (value !== null && value !== undefined && !isNaN(value)) {
                                sum += value * weight;
                                weightSum += weight;
                            }
                        }
                    }
                }

                // Normalize by weight sum to handle edge cases
                row.push(weightSum > 0 ? sum / weightSum : (matrix[i][j] || 0));
            }
            smoothedMatrix.push(row);
        }

        return smoothedMatrix;
    }

    createGaussianKernel(size, sigma) {
        // Create a 2D Gaussian kernel for smoothing
        const kernel = [];
        const center = Math.floor(size / 2);
        let sum = 0;

        // Generate kernel values
        for (let i = 0; i < size; i++) {
            const row = [];
            for (let j = 0; j < size; j++) {
                const x = i - center;
                const y = j - center;
                const value = Math.exp(-(x * x + y * y) / (2 * sigma * sigma));
                row.push(value);
                sum += value;
            }
            kernel.push(row);
        }

        // Normalize kernel
        for (let i = 0; i < size; i++) {
            for (let j = 0; j < size; j++) {
                kernel[i][j] /= sum;
            }
        }

        return kernel;
    }

    applyBicubicInterpolation(matrix) {
        // Apply bicubic interpolation to increase resolution and create smoother gradients
        const rows = matrix.length;
        const cols = matrix[0] ? matrix[0].length : 0;

        if (rows === 0 || cols === 0) return matrix;

        // Create a higher resolution matrix (2x resolution)
        const newRows = (rows - 1) * 2 + 1;
        const newCols = (cols - 1) * 2 + 1;
        const interpolatedMatrix = [];

        // Initialize the new matrix
        for (let i = 0; i < newRows; i++) {
            interpolatedMatrix.push(new Array(newCols).fill(0));
        }

        // Copy original values to even positions
        for (let i = 0; i < rows; i++) {
            for (let j = 0; j < cols; j++) {
                interpolatedMatrix[i * 2][j * 2] = matrix[i][j] || 0;
            }
        }

        // Interpolate horizontally (odd columns)
        for (let i = 0; i < newRows; i += 2) {
            for (let j = 1; j < newCols; j += 2) {
                const leftCol = Math.floor(j / 2);
                const rightCol = Math.min(leftCol + 1, cols - 1);
                const leftVal = interpolatedMatrix[i][leftCol * 2];
                const rightVal = interpolatedMatrix[i][rightCol * 2];
                interpolatedMatrix[i][j] = (leftVal + rightVal) / 2;
            }
        }

        // Interpolate vertically (odd rows)
        for (let i = 1; i < newRows; i += 2) {
            for (let j = 0; j < newCols; j++) {
                const topRow = Math.floor(i / 2);
                const bottomRow = Math.min(topRow + 1, rows - 1);
                const topVal = interpolatedMatrix[topRow * 2][j];
                const bottomVal = interpolatedMatrix[bottomRow * 2][j];
                interpolatedMatrix[i][j] = (topVal + bottomVal) / 2;
            }
        }

        // Downsample back to original resolution with smoother values
        const smoothedMatrix = [];
        for (let i = 0; i < rows; i++) {
            const row = [];
            for (let j = 0; j < cols; j++) {
                // Average a 2x2 area from the interpolated matrix
                const startI = i * 2;
                const startJ = j * 2;
                let sum = 0;
                let count = 0;

                for (let di = 0; di < 2 && startI + di < newRows; di++) {
                    for (let dj = 0; dj < 2 && startJ + dj < newCols; dj++) {
                        sum += interpolatedMatrix[startI + di][startJ + dj];
                        count++;
                    }
                }

                row.push(count > 0 ? sum / count : 0);
            }
            smoothedMatrix.push(row);
        }

        return smoothedMatrix;
    }

    findZeroGammaLevels(matrix) {
        // Find zero gamma levels in the exposure matrix
        // Returns an array containing only zero or near-zero contour levels

        if (!matrix || matrix.length === 0 || !matrix[0] || matrix[0].length === 0) {
            return [];
        }

        const allValues = matrix.flat().filter(v => v !== null && v !== undefined && !isNaN(v));

        if (allValues.length === 0) {
            return [];
        }

        // Calculate statistical properties
        const minValue = Math.min(...allValues);
        const maxValue = Math.max(...allValues);

        // Only add zero level if the data crosses zero (has both positive and negative values)
        if (minValue < 0 && maxValue > 0) {
            console.log(`🎯 Zero gamma level found - range: ${minValue.toFixed(2)} to ${maxValue.toFixed(2)}`);
            return [0];
        }

        console.log(`🎯 No zero gamma crossing found - range: ${minValue.toFixed(2)} to ${maxValue.toFixed(2)}`);
        return [];
    }

    findPeakTroughContourLevels(matrix) {
        // Find significant peaks and troughs in the gamma exposure data
        // Returns an array of contour levels that highlight these features
        // NOTE: This function is kept for compatibility but not used in exposure surface visualization

        if (!matrix || matrix.length === 0 || !matrix[0] || matrix[0].length === 0) {
            return [];
        }

        const rows = matrix.length;
        const cols = matrix[0].length;
        const allValues = matrix.flat().filter(v => v !== null && v !== undefined && !isNaN(v));

        if (allValues.length === 0) {
            return [];
        }

        // Calculate statistical properties
        const minValue = Math.min(...allValues);
        const maxValue = Math.max(...allValues);
        const range = maxValue - minValue;

        if (range === 0) {
            return [minValue]; // All values are the same
        }

        // Find local peaks and troughs using a sliding window approach
        const localExtrema = [];
        const windowSize = Math.max(2, Math.floor(Math.min(rows, cols) / 10)); // Adaptive window size

        // Scan for local maxima and minima
        for (let i = windowSize; i < rows - windowSize; i++) {
            for (let j = windowSize; j < cols - windowSize; j++) {
                const centerValue = matrix[i][j];

                if (centerValue === null || centerValue === undefined || isNaN(centerValue)) {
                    continue;
                }

                let isLocalMax = true;
                let isLocalMin = true;

                // Check surrounding values in the window
                for (let di = -windowSize; di <= windowSize; di++) {
                    for (let dj = -windowSize; dj <= windowSize; dj++) {
                        if (di === 0 && dj === 0) continue; // Skip center point

                        const neighborValue = matrix[i + di][j + dj];
                        if (neighborValue !== null && neighborValue !== undefined && !isNaN(neighborValue)) {
                            if (neighborValue >= centerValue) isLocalMax = false;
                            if (neighborValue <= centerValue) isLocalMin = false;
                        }
                    }
                }

                // If it's a significant local extremum, add it
                if (isLocalMax && centerValue > minValue + range * 0.1) { // At least 10% above minimum
                    localExtrema.push({ value: centerValue, type: 'peak', i, j });
                } else if (isLocalMin && centerValue < maxValue - range * 0.1) { // At least 10% below maximum
                    localExtrema.push({ value: centerValue, type: 'trough', i, j });
                }
            }
        }

        // Sort extrema by absolute magnitude (most significant first)
        localExtrema.sort((a, b) => Math.abs(b.value) - Math.abs(a.value));

        // Select the most significant extrema (limit to prevent overcrowding)
        const maxContours = 8; // Maximum number of contour lines
        const selectedExtrema = localExtrema.slice(0, maxContours);

        // Extract contour levels
        let contourLevels = selectedExtrema.map(ext => ext.value);

        // If we don't have enough local extrema, add some strategic levels
        if (contourLevels.length < 3) {
            // Add zero line if it's within the range
            if (minValue < 0 && maxValue > 0) {
                contourLevels.push(0);
            }

            // Add quartile levels for better coverage
            const q1 = minValue + range * 0.25;
            const q3 = minValue + range * 0.75;

            if (!contourLevels.some(level => Math.abs(level - q1) < range * 0.05)) {
                contourLevels.push(q1);
            }
            if (!contourLevels.some(level => Math.abs(level - q3) < range * 0.05)) {
                contourLevels.push(q3);
            }
        }

        // Remove duplicates and sort
        contourLevels = [...new Set(contourLevels.map(level => Math.round(level * 1000) / 1000))];
        contourLevels.sort((a, b) => a - b);

        console.log(`🎯 Found ${selectedExtrema.length} local extrema, generated ${contourLevels.length} contour levels`);
        console.log(`📊 Peaks: ${selectedExtrema.filter(e => e.type === 'peak').length}, Troughs: ${selectedExtrema.filter(e => e.type === 'trough').length}`);

        return {
            levels: contourLevels,
            extrema: selectedExtrema,
            stats: {
                minValue,
                maxValue,
                range,
                totalExtrema: localExtrema.length
            }
        };
    }

    updateExposureSurfaceStatus(message, type) {
        const statusElement = document.getElementById('exposure-surface-status');
        const statusDisplayElement = document.getElementById('exposure-surface-status-display');

        if (statusElement) {
            statusElement.innerHTML = `<i class="fas fa-info-circle me-1"></i>${message}`;
            statusElement.className = 'text-muted small';

            if (type === 'error') {
                statusElement.className += ' text-danger';
            } else if (type === 'success') {
                statusElement.className += ' text-success';
            } else if (type === 'info') {
                statusElement.className += ' text-info';
            }
        }

        if (statusDisplayElement) {
            statusDisplayElement.textContent = message;
            statusDisplayElement.className = 'fw-bold';

            if (type === 'error') {
                statusDisplayElement.className += ' text-danger';
            } else if (type === 'success') {
                statusDisplayElement.className += ' text-success';
            } else if (type === 'info') {
                statusDisplayElement.className += ' text-info';
            }
        }
    }

    updateExposureSurfaceStats(surfaceData, currentPrice) {
        if (!surfaceData || surfaceData.length === 0) return;

        try {
            // Get what's currently being shown
            const showCalls = document.getElementById('exposure-surface-show-calls')?.checked || false;
            const showPuts = document.getElementById('exposure-surface-show-puts')?.checked || false;
            const showNet = document.getElementById('exposure-surface-show-net')?.checked || false;

            // Calculate statistics based on what's being shown (prioritize net, then calls, then puts)
            let gammaValues;
            let gammaType;

            if (showNet) {
                gammaValues = surfaceData.map(d => d.net_gamma_exposure || 0);
                gammaType = 'net';
            } else if (showCalls) {
                gammaValues = surfaceData.map(d => d.call_gamma_exposure || 0);
                gammaType = 'calls';
            } else if (showPuts) {
                gammaValues = surfaceData.map(d => d.put_gamma_exposure || 0);
                gammaType = 'puts';
            } else {
                // Nothing selected, show placeholder
                document.getElementById('max-positive-gex').textContent = '--';
                document.getElementById('max-negative-gex').textContent = '--';
                document.getElementById('zero-gex-level').textContent = '--';
                document.getElementById('exposure-surface-current-price').textContent =
                    currentPrice ? `$${currentPrice.toFixed(2)}` : '--';
                document.getElementById('exposure-surface-data-points').textContent =
                    surfaceData.length.toLocaleString();
                return;
            }

            const maxPositive = Math.max(...gammaValues.filter(v => v > 0));
            const maxNegative = Math.min(...gammaValues.filter(v => v < 0));

            // Find zero GEX level (closest to zero) - only meaningful for net gamma
            let zeroGexPoint = null;
            if (gammaType === 'net') {
                zeroGexPoint = surfaceData.reduce((closest, current) => {
                    const currentValue = current.net_gamma_exposure || 0;
                    const closestValue = closest.net_gamma_exposure || 0;
                    return Math.abs(currentValue) < Math.abs(closestValue) ? current : closest;
                });
            }

            // Update statistics display
            document.getElementById('max-positive-gex').textContent =
                maxPositive > 0 ? this.formatNumber(maxPositive) : '--';
            document.getElementById('max-negative-gex').textContent =
                maxNegative < 0 ? this.formatNumber(maxNegative) : '--';
            document.getElementById('zero-gex-level').textContent =
                (gammaType === 'net' && zeroGexPoint) ? `$${zeroGexPoint.price_level.toFixed(2)}` :
                (gammaType === 'net' ? '--' : 'N/A (Calls/Puts Only)');
            document.getElementById('exposure-surface-current-price').textContent =
                currentPrice ? `$${currentPrice.toFixed(2)}` : '--';
            document.getElementById('exposure-surface-data-points').textContent =
                surfaceData.length.toLocaleString();

        } catch (error) {
            console.error('Error updating exposure surface stats:', error);
        }
    }

    // Chart Help System
    initializeChartHelp() {
        // Add click handlers to all help buttons
        document.querySelectorAll('.chart-help-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const chartType = btn.getAttribute('data-chart');
                this.showChartHelp(chartType);
            });
        });
    }

    showChartHelp(chartType) {
        const modal = new bootstrap.Modal(document.getElementById('chart-help-modal'));
        const titleElement = document.getElementById('chart-help-modal-label');
        const contentElement = document.getElementById('chart-help-content');

        const helpContent = this.getChartHelpContent(chartType);
        titleElement.innerHTML = `<i class="fas fa-question-circle me-2"></i>${helpContent.title}`;
        contentElement.innerHTML = helpContent.content;

        modal.show();
    }

    getChartHelpContent(chartType) {
        const helpData = {
            'candlestick': {
                title: 'Candlestick Chart Help',
                content: `
                    <h6><i class="fas fa-chart-line me-2"></i>What This Chart Shows</h6>
                    <p>The candlestick chart displays comprehensive price action over time with integrated volume analysis. Each candlestick represents the complete price story for your selected timeframe, showing opening price, highest price reached, lowest price touched, and closing price. The volume histogram below shows trading activity intensity.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Detailed Settings Guide</h6>
                    <ul>
                        <li><strong>Time Period:</strong> Controls historical data range. Use 1-5 days for intraday scalping, 1-3 months for swing trading, 1-2 years for long-term trend analysis. Longer periods help identify major support/resistance levels.</li>
                        <li><strong>Interval:</strong> Determines candlestick timeframe. 1-5 minute intervals for day trading, 15-30 minutes for short-term swings, 1 hour for intraday trends, daily for position trading. Match interval to your trading style.</li>
                        <li><strong>Quick Timeframes:</strong>
                            <ul>
                                <li><em>Intraday (1D/5M):</em> Perfect for scalping and day trading options</li>
                                <li><em>5D/1M:</em> High-resolution view for precise entry/exit timing</li>
                                <li><em>Monthly (1M/1D):</em> Swing trading and weekly options strategies</li>
                                <li><em>Yearly (1Y/1W):</em> Long-term trend analysis and LEAPS strategies</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Trading Applications</h6>
                    <ul>
                        <li><strong>Strike Selection:</strong> Use recent highs/lows to select call/put strikes. Price rejection at levels indicates strong support/resistance for covered calls or cash-secured puts.</li>
                        <li><strong>Volatility Timing:</strong> Large candlestick bodies indicate high volatility - good for selling premium. Small bodies suggest low volatility - good for buying options before breakouts.</li>
                        <li><strong>Volume Confirmation:</strong> High volume on breakouts confirms moves. Low volume on breakouts suggests false signals. Use volume spikes to time option entries.</li>
                        <li><strong>Pattern Recognition:</strong>
                            <ul>
                                <li><em>Doji patterns:</em> Indecision - good for straddles/strangles</li>
                                <li><em>Hammer/Shooting star:</em> Reversal signals - good for directional plays</li>
                                <li><em>Engulfing patterns:</em> Strong reversal - good for aggressive directional trades</li>
                            </ul>
                        </li>
                        <li><strong>Gamma Integration:</strong> Combine with gamma levels to identify where price is likely to pin or break through. High gamma areas often act as magnets.</li>
                        <li><strong>Earnings Plays:</strong> Use pre-earnings consolidation patterns to position for volatility expansion. Look for tight ranges before events.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Key Trading Tips</h6>
                    <ul>
                        <li>Always confirm price action with volume - price moves without volume often fail</li>
                        <li>Use multiple timeframes - enter on smaller timeframes, confirm on larger ones</li>
                        <li>Watch for divergences between price and volume for early reversal signals</li>
                        <li>Combine with options flow data to understand institutional positioning</li>
                    </ul>
                `
            },
            'gamma-zones': {
                title: 'Gamma Zone Settings Help',
                content: `
                    <h6><i class="fas fa-layer-group me-2"></i>What Gamma Zones Show</h6>
                    <p>Gamma zones are sophisticated overlays that visualize where market makers have concentrated gamma exposure, creating invisible but powerful forces that influence price movement. These zones represent areas where dealers must hedge aggressively, creating natural support and resistance levels that often act as price magnets or barriers.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Detailed Settings Explained</h6>
                    <ul>
                        <li><strong>Show Gamma Zones:</strong> Toggles colored zone overlays on the price chart. Green zones indicate positive gamma (price support), red zones indicate negative gamma (price acceleration). Zone intensity reflects gamma magnitude.</li>
                        <li><strong>Show Zero GEX Line:</strong> Displays the critical zero gamma exposure level - the dividing line between positive and negative gamma regimes. This is often the most important level for intraday trading.</li>
                        <li><strong>Export to TradingView:</strong> Generates Pine Script code that recreates these gamma zones in TradingView. Includes zone fills, background coloring, and alert conditions for zone breaks.</li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Trading Applications</h6>
                    <ul>
                        <li><strong>Zone Trading Strategy:</strong>
                            <ul>
                                <li><em>Strong Positive Zones (Bright Green):</em> Act as price magnets. Sell calls above, buy puts below. Price tends to revert to these levels.</li>
                                <li><em>Weak Positive Zones (Light Green):</em> Provide temporary support but can be broken with volume. Good for scalping bounces.</li>
                                <li><em>Negative Zones (Red):</em> Price accelerates through these areas. Use for breakout trades and momentum plays.</li>
                            </ul>
                        </li>
                        <li><strong>Zero GEX Line Strategy:</strong>
                            <ul>
                                <li><em>Above Zero GEX:</em> Market makers provide liquidity, dampening volatility. Sell premium strategies work well. Price tends to mean revert.</li>
                                <li><em>Below Zero GEX:</em> Market makers amplify moves, increasing volatility. Buy options before moves, sell after spikes.</li>
                                <li><em>Zero GEX Breaks:</em> Major regime changes. Often coincide with significant directional moves.</li>
                            </ul>
                        </li>
                        <li><strong>Intraday Scalping:</strong> Use zone boundaries for precise entry/exit points. Price often bounces off zone edges multiple times before breaking through.</li>
                        <li><strong>Swing Trading:</strong> Strong gamma walls (thick zones) often mark major turning points. Use for position sizing and risk management.</li>
                        <li><strong>Options Expiration Effects:</strong> Gamma zones become stronger approaching expiration as time decay accelerates. Pin risk increases near large zones.</li>
                        <li><strong>Earnings and Events:</strong> Gamma zones often break during high-impact events. Position accordingly before announcements.</li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>Zone Interpretation Guide</h6>
                    <ul>
                        <li><strong>Zone Thickness:</strong> Thicker zones = stronger gamma concentration = more reliable support/resistance</li>
                        <li><strong>Zone Color Intensity:</strong> Brighter colors = higher gamma values = stronger price influence</li>
                        <li><strong>Multiple Zones:</strong> Clustered zones create "gamma walls" - extremely strong levels that are difficult to break</li>
                        <li><strong>Zone Gaps:</strong> Areas with no gamma coverage where price can move freely - good for breakout targets</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Risk Management Tips</h6>
                    <ul>
                        <li>Don't fight strong gamma walls - trade bounces off them instead of trying to break through</li>
                        <li>Be cautious of positions when approaching zero GEX - volatility can spike suddenly</li>
                        <li>Use zone breaks as stop-loss triggers - once broken, zones often become resistance/support</li>
                        <li>Monitor zone changes throughout the day as new options activity can shift levels</li>
                    </ul>
                `
            },
            'gex': {
                title: 'Gamma Exposure (GEX) Help',
                content: `
                    <h6><i class="fas fa-chart-bar me-2"></i>What GEX Shows</h6>
                    <p>Gamma Exposure (GEX) reveals the hidden forces driving market behavior by measuring how much stock market makers must buy or sell as prices move. This creates a feedback loop where high gamma areas become self-reinforcing price levels. Positive GEX means dealers buy when price falls and sell when price rises (stabilizing), while negative GEX means they amplify moves in both directions.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Comprehensive Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Selection:</strong>
                            <ul>
                                <li><em>Bar Charts:</em> Best for identifying specific strike levels and comparing relative magnitudes. Use for precise level identification.</li>
                                <li><em>Line Charts:</em> Excellent for seeing gamma distribution trends and smooth transitions between strikes. Use for understanding overall gamma shape.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying outliers and unusual gamma concentrations. Good for spotting anomalies.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Optimization:</strong>
                            <ul>
                                <li><em>Narrow Range (5-10):</em> Focus on immediate ATM gamma for intraday trading</li>
                                <li><em>Medium Range (15-25):</em> Standard view for most trading strategies</li>
                                <li><em>Wide Range (30-50):</em> See full gamma landscape for position trading and risk management</li>
                            </ul>
                        </li>
                        <li><strong>Exposure Type Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals where call sellers are concentrated. High call GEX = resistance levels.</li>
                                <li><em>Show Puts:</em> Shows put seller concentration. High put GEX = support levels.</li>
                                <li><em>Show Net:</em> Combined view showing overall gamma effect. Most important for understanding price behavior.</li>
                            </ul>
                        </li>
                        <li><strong>Mark Highest:</strong> Automatically highlights the maximum gamma strike - often the most significant price magnet or barrier.</li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Trading Strategies</h6>
                    <ul>
                        <li><strong>Pin Risk Trading:</strong>
                            <ul>
                                <li><em>High Positive GEX Strikes:</em> Price tends to "pin" near these levels, especially on expiration. Sell straddles/strangles at these strikes.</li>
                                <li><em>Gamma Walls:</em> Extremely high GEX levels that act as strong magnets. Price often gravitates toward these levels throughout the day.</li>
                                <li><em>Pin Risk Timing:</em> Effect strongest in final hours before expiration when gamma peaks.</li>
                            </ul>
                        </li>
                        <li><strong>Breakout Strategy:</strong>
                            <ul>
                                <li><em>Negative GEX Zones:</em> Areas where dealers amplify moves. Perfect for momentum trades and breakout plays.</li>
                                <li><em>Low GEX Areas:</em> Price can move freely through these zones. Use as breakout targets.</li>
                                <li><em>GEX Cliff Trading:</em> When price breaks through high GEX, it often accelerates to the next GEX level.</li>
                            </ul>
                        </li>
                        <li><strong>Mean Reversion Plays:</strong>
                            <ul>
                                <li><em>Overshoots:</em> When price moves beyond high GEX levels, it often snaps back quickly.</li>
                                <li><em>Gamma Squeeze Setup:</em> Price compression between two high GEX levels often leads to explosive moves.</li>
                                <li><em>Intraday Reversals:</em> Use GEX levels as precise reversal points for scalping.</li>
                            </ul>
                        </li>
                        <li><strong>Options Strategy Selection:</strong>
                            <ul>
                                <li><em>High GEX Environment:</em> Sell premium (iron condors, strangles) as volatility gets crushed</li>
                                <li><em>Low/Negative GEX:</em> Buy options before moves, as volatility tends to expand</li>
                                <li><em>GEX Transitions:</em> Major strategy shifts needed when crossing zero GEX</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>GEX Interpretation Mastery</h6>
                    <ul>
                        <li><strong>Magnitude Matters:</strong> GEX values above $50M per 1% move are significant, above $100M are major levels</li>
                        <li><strong>Asymmetric GEX:</strong> When call and put GEX are very different, expect directional bias</li>
                        <li><strong>GEX Decay:</strong> Gamma exposure decreases rapidly as expiration approaches - monitor changes</li>
                        <li><strong>Multi-Expiry Effects:</strong> Weekly and monthly expirations can create competing gamma forces</li>
                        <li><strong>Market Cap Scaling:</strong> Larger stocks need higher absolute GEX values to create the same price effect</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Professional Trading Tips</h6>
                    <ul>
                        <li>Always check GEX before entering any options position - it affects your probability of success</li>
                        <li>Use GEX levels for position sizing - larger positions near low GEX, smaller near high GEX</li>
                        <li>Monitor GEX changes throughout the day - new options flow can shift levels dramatically</li>
                        <li>Combine with volume analysis - high volume at GEX levels confirms their significance</li>
                        <li>Be aware of gamma flips - when positive GEX becomes negative, market behavior changes completely</li>
                    </ul>
                `
            },
            'vgex': {
                title: 'Volume Gamma Exposure Help',
                content: `
                    <h6><i class="fas fa-chart-area me-2"></i>What Volume GEX Shows</h6>
                    <p>Volume Gamma Exposure (VGEX) represents the evolution of gamma analysis by weighting theoretical gamma exposure with actual trading volume. This creates a more realistic picture of where market makers are actively hedging, as opposed to just where they theoretically should hedge. VGEX reveals the "hot spots" of real market maker activity and shows which gamma levels are backed by actual money flow.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Advanced Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Optimization:</strong>
                            <ul>
                                <li><em>Bar Charts:</em> Best for comparing volume-weighted gamma across strikes. Shows which levels have the most "real" activity.</li>
                                <li><em>Line Charts:</em> Excellent for identifying volume-weighted gamma trends and seeing how activity flows across strikes.</li>
                                <li><em>Scatter Plots:</em> Reveals volume anomalies and unusual concentrations of activity that might be missed in other views.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Strategy:</strong>
                            <ul>
                                <li><em>Tight Range (5-15):</em> Focus on immediate volume activity around current price for scalping</li>
                                <li><em>Standard Range (20-30):</em> Balanced view for most trading strategies and swing trades</li>
                                <li><em>Wide Range (35-50):</em> Full market structure view for understanding institutional positioning</li>
                            </ul>
                        </li>
                        <li><strong>Volume-Weighted Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals where actual call volume is creating gamma exposure. High call VGEX = active resistance.</li>
                                <li><em>Show Puts:</em> Shows real put volume gamma concentration. High put VGEX = active support.</li>
                                <li><em>Show Net:</em> Combined volume-weighted view showing where the real money is positioned.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional Trading Strategies</h6>
                    <ul>
                        <li><strong>Volume-Confirmed Levels:</strong>
                            <ul>
                                <li><em>High VGEX Strikes:</em> These levels have both theoretical gamma AND actual volume backing them. Much more reliable than theoretical GEX alone.</li>
                                <li><em>VGEX vs GEX Divergence:</em> When VGEX is much lower than GEX, the level may be weaker than it appears. When VGEX exceeds GEX, the level is stronger than expected.</li>
                                <li><em>Volume Confirmation:</em> Use VGEX to validate GEX levels before trading. Only trade levels with significant volume backing.</li>
                            </ul>
                        </li>
                        <li><strong>Institutional Flow Analysis:</strong>
                            <ul>
                                <li><em>Large VGEX Spikes:</em> Often indicate institutional activity. These levels become major support/resistance.</li>
                                <li><em>VGEX Clusters:</em> Multiple strikes with high VGEX create "institutional zones" - extremely reliable levels.</li>
                                <li><em>Volume Migration:</em> Watch VGEX shift throughout the day to see where smart money is repositioning.</li>
                            </ul>
                        </li>
                        <li><strong>Intraday Scalping Mastery:</strong>
                            <ul>
                                <li><em>VGEX Bounces:</em> Price often bounces multiple times off high VGEX levels. Perfect for scalping with tight stops.</li>
                                <li><em>Volume Exhaustion:</em> When price breaks through high VGEX after multiple bounces, it often accelerates quickly.</li>
                                <li><em>VGEX Fades:</em> As volume decreases, VGEX levels weaken. Monitor for level degradation throughout the session.</li>
                            </ul>
                        </li>
                        <li><strong>Options Strategy Enhancement:</strong>
                            <ul>
                                <li><em>Strike Selection:</em> Use VGEX to choose strikes with actual volume support rather than just theoretical levels.</li>
                                <li><em>Risk Management:</em> High VGEX levels make excellent stop-loss and take-profit targets.</li>
                                <li><em>Timing Entries:</em> Enter positions when price approaches high VGEX levels for better risk/reward.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-microscope me-2"></i>VGEX vs Regular GEX Analysis</h6>
                    <ul>
                        <li><strong>Reliability Factor:</strong> VGEX levels are typically 2-3x more reliable than theoretical GEX because they're backed by actual trading</li>
                        <li><strong>Strength Validation:</strong> Compare VGEX to GEX ratios - ratios above 1.5 indicate very strong levels</li>
                        <li><strong>False Level Identification:</strong> Low VGEX despite high GEX often indicates "paper" levels that won't hold</li>
                        <li><strong>Market Maker Reality:</strong> VGEX shows where dealers are actually hedging vs where they theoretically should hedge</li>
                    </ul>

                    <h6><i class="fas fa-clock me-2"></i>Time-Based VGEX Analysis</h6>
                    <ul>
                        <li><strong>Opening Hour:</strong> VGEX builds as volume accumulates. Early levels may be weak until volume confirms them.</li>
                        <li><strong>Mid-Day:</strong> Peak VGEX reliability as volume has accumulated. Best time for VGEX-based strategies.</li>
                        <li><strong>Closing Hour:</strong> VGEX may spike due to closing activity. Be cautious of artificial level creation.</li>
                        <li><strong>Low Volume Periods:</strong> VGEX becomes less reliable during lunch hours and low-activity periods.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Advanced Risk Management</h6>
                    <ul>
                        <li>Never trade against high VGEX levels without strong fundamental catalysts</li>
                        <li>Use VGEX level breaks as definitive stop-loss triggers - they rarely fail twice</li>
                        <li>Monitor VGEX degradation throughout the day - levels weaken as relative volume decreases</li>
                        <li>Combine VGEX with order flow data for maximum edge in level trading</li>
                        <li>Be aware that VGEX can create false confidence - always confirm with price action</li>
                    </ul>
                `
            },
            'dex': {
                title: 'Delta Exposure (DEX) Help',
                content: `
                    <h6><i class="fas fa-chart-line me-2"></i>What DEX Shows</h6>
                    <p>Delta Exposure (DEX) reveals the directional positioning and hedging obligations of market makers across all strike prices. Unlike gamma which shows acceleration effects, delta exposure shows the linear directional bias that market makers must hedge. Positive DEX means dealers are net long (must sell as price rises), while negative DEX means dealers are net short (must buy as price falls). This creates predictable flow patterns that sophisticated traders can exploit.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Comprehensive Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Analysis:</strong>
                            <ul>
                                <li><em>Bar Charts:</em> Perfect for identifying specific strikes with high directional exposure. Shows exact levels where dealer flow will occur.</li>
                                <li><em>Line Charts:</em> Excellent for visualizing overall directional bias trends across the option chain. Shows flow patterns clearly.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying unusual delta concentrations and outlier strikes that might create unexpected flow.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Optimization:</strong>
                            <ul>
                                <li><em>Narrow Range (10-20):</em> Focus on immediate ATM delta effects for short-term trading</li>
                                <li><em>Medium Range (25-35):</em> Standard view for most directional strategies and swing trades</li>
                                <li><em>Wide Range (40-60):</em> Full market structure view for understanding overall dealer positioning and major flow levels</li>
                            </ul>
                        </li>
                        <li><strong>Exposure Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call dealer positioning. High positive call DEX = dealers must sell stock as price rises (creates resistance).</li>
                                <li><em>Show Puts:</em> Shows put dealer positioning. High negative put DEX = dealers must buy stock as price falls (creates support).</li>
                                <li><em>Show Net:</em> Combined directional exposure showing overall dealer flow obligations. Most critical for understanding market dynamics.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Trading Strategies</h6>
                    <ul>
                        <li><strong>Dealer Flow Anticipation:</strong>
                            <ul>
                                <li><em>High Positive DEX Zones:</em> Dealers must sell stock as price rises. Expect selling pressure and potential resistance. Good for shorting rallies or selling calls.</li>
                                <li><em>High Negative DEX Zones:</em> Dealers must buy stock as price falls. Expect buying support and potential bounces. Good for buying dips or selling puts.</li>
                                <li><em>DEX Transition Zones:</em> Where DEX changes from positive to negative (or vice versa). Often mark significant turning points.</li>
                            </ul>
                        </li>
                        <li><strong>Momentum and Mean Reversion Identification:</strong>
                            <ul>
                                <li><em>Aligned DEX (all positive or all negative):</em> Dealer flow reinforces price moves. Expect momentum continuation.</li>
                                <li><em>Opposing DEX:</em> Dealer flow fights price moves. Expect mean reversion and range-bound behavior.</li>
                                <li><em>DEX Magnitude:</em> Larger absolute DEX values create stronger flow effects and more predictable price behavior.</li>
                            </ul>
                        </li>
                        <li><strong>Support and Resistance Prediction:</strong>
                            <ul>
                                <li><em>DEX Walls:</em> Large concentrated DEX at specific strikes creates strong support (negative DEX) or resistance (positive DEX).</li>
                                <li><em>DEX Ramps:</em> Gradually increasing DEX creates accelerating flow effects as price moves through the range.</li>
                                <li><em>DEX Cliffs:</em> Sudden DEX changes create sharp support/resistance levels that often mark turning points.</li>
                            </ul>
                        </li>
                        <li><strong>Options Strategy Selection:</strong>
                            <ul>
                                <li><em>High Positive DEX Environment:</em> Favor put strategies and short calls. Dealer selling pressure helps put positions.</li>
                                <li><em>High Negative DEX Environment:</em> Favor call strategies and short puts. Dealer buying support helps call positions.</li>
                                <li><em>Balanced DEX:</em> Neutral strategies like iron condors work well when DEX is balanced around current price.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-arrows-alt me-2"></i>DEX and Price Movement Correlation</h6>
                    <ul>
                        <li><strong>Upward Price Moves:</strong> Move through positive DEX zones slowly (dealer selling), accelerate through negative DEX zones</li>
                        <li><strong>Downward Price Moves:</strong> Move through negative DEX zones slowly (dealer buying), accelerate through positive DEX zones</li>
                        <li><strong>Sideways Markets:</strong> Often occur when positive and negative DEX zones balance each other out</li>
                        <li><strong>Breakout Prediction:</strong> Price often breaks out when moving from high DEX zones into low DEX areas</li>
                    </ul>

                    <h6><i class="fas fa-sync-alt me-2"></i>DEX Integration with Other Greeks</h6>
                    <ul>
                        <li><strong>DEX + GEX Analysis:</strong> High DEX with high GEX creates very strong levels. High DEX with low GEX creates flow without acceleration.</li>
                        <li><strong>DEX + Volume:</strong> DEX backed by high volume is much more reliable than theoretical DEX alone.</li>
                        <li><strong>DEX + Volatility:</strong> High DEX during low volatility often precedes major moves as positions unwind.</li>
                        <li><strong>DEX Evolution:</strong> Monitor how DEX changes throughout the day as new positions are established.</li>
                    </ul>

                    <h6><i class="fas fa-calendar me-2"></i>Time-Sensitive DEX Effects</h6>
                    <ul>
                        <li><strong>Expiration Approach:</strong> DEX effects intensify as expiration nears due to increased hedging activity</li>
                        <li><strong>Intraday Patterns:</strong> DEX often builds during market hours and resets overnight</li>
                        <li><strong>Weekly Cycles:</strong> DEX patterns often repeat weekly, especially around regular expiration cycles</li>
                        <li><strong>Event-Driven Changes:</strong> Earnings, Fed meetings, and other events can dramatically shift DEX positioning</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Professional Risk Management</h6>
                    <ul>
                        <li>Always consider DEX when sizing positions - trade smaller against high opposing DEX</li>
                        <li>Use DEX level breaks as stop-loss triggers - dealer flow changes can be swift and powerful</li>
                        <li>Monitor DEX changes in real-time - new options activity can shift dealer obligations quickly</li>
                        <li>Be aware of DEX concentration risk - avoid overexposure to single high-DEX strikes</li>
                        <li>Combine DEX analysis with fundamental catalysts for highest probability trades</li>
                    </ul>
                `
            },
            'dgex': {
                title: 'Delta-Gamma Exposure Help',
                content: `
                    <h6><i class="fas fa-chart-pie me-2"></i>What Delta-Gamma Shows</h6>
                    <p>Delta-Gamma Exposure (DGEX) represents the sophisticated intersection of directional bias and acceleration effects in options markets. This metric combines the linear directional exposure (delta) with the acceleration component (gamma) to reveal how market maker hedging intensity changes as prices move. DGEX shows not just where dealers will hedge, but how aggressively they'll hedge, making it crucial for predicting volatility expansion, contraction, and price acceleration patterns.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Advanced Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Selection:</strong>
                            <ul>
                                <li><em>Line Charts:</em> Best for visualizing the smooth relationship between delta and gamma effects across strikes. Shows acceleration patterns clearly.</li>
                                <li><em>Bar Charts:</em> Excellent for identifying specific strikes where delta-gamma effects are concentrated. Good for precise level identification.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying non-linear relationships and unusual delta-gamma combinations that might signal market anomalies.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Strategy:</strong>
                            <ul>
                                <li><em>Tight ATM Focus (10-15):</em> Concentrate on immediate delta-gamma effects around current price for scalping and day trading</li>
                                <li><em>Standard Range (20-30):</em> Balanced view for most trading strategies, capturing primary acceleration zones</li>
                                <li><em>Wide Analysis (35-50):</em> Full market view for understanding complete delta-gamma landscape and major inflection points</li>
                            </ul>
                        </li>
                        <li><strong>Exposure Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call delta-gamma concentration. High values indicate where call hedging will accelerate price moves.</li>
                                <li><em>Show Puts:</em> Shows put delta-gamma effects. High values indicate where put hedging will create acceleration or deceleration.</li>
                                <li><em>Show Net:</em> Combined delta-gamma view showing overall acceleration/deceleration effects. Most important for predicting price behavior.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional Trading Strategies</h6>
                    <ul>
                        <li><strong>Volatility Expansion Prediction:</strong>
                            <ul>
                                <li><em>High Positive DGEX:</em> Indicates areas where price moves will accelerate due to dealer hedging. Perfect for momentum trades and breakout strategies.</li>
                                <li><em>DGEX Ramps:</em> Gradually increasing DGEX creates accelerating volatility as price moves through the range. Use for volatility expansion plays.</li>
                                <li><em>DGEX Cliffs:</em> Sharp DGEX changes often mark volatility regime shifts. Position for major moves when approaching these levels.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Contraction Identification:</strong>
                            <ul>
                                <li><em>Low/Negative DGEX:</em> Areas where dealer hedging dampens price moves. Expect range-bound behavior and volatility compression.</li>
                                <li><em>DGEX Valleys:</em> Low DGEX zones between high DGEX areas often become consolidation ranges. Good for mean reversion strategies.</li>
                                <li><em>DGEX Neutralization:</em> When positive and negative DGEX balance out, expect sideways price action.</li>
                            </ul>
                        </li>
                        <li><strong>Acceleration Zone Trading:</strong>
                            <ul>
                                <li><em>DGEX Momentum Plays:</em> Enter long positions before price enters high positive DGEX zones. Dealer hedging will amplify your moves.</li>
                                <li><em>DGEX Fade Strategies:</em> Short positions work well when price approaches high negative DGEX zones where moves will be dampened.</li>
                                <li><em>DGEX Transition Trading:</em> Major opportunities exist when price moves from low DGEX to high DGEX zones.</li>
                            </ul>
                        </li>
                        <li><strong>Options Strategy Optimization:</strong>
                            <ul>
                                <li><em>High DGEX Environment:</em> Buy options before entering these zones, sell options within them as volatility expands then contracts.</li>
                                <li><em>Low DGEX Environment:</em> Sell premium strategies work well as volatility remains compressed.</li>
                                <li><em>DGEX Timing:</em> Use DGEX levels to time option entries and exits for maximum volatility capture.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-tachometer-alt me-2"></i>DGEX Magnitude Interpretation</h6>
                    <ul>
                        <li><strong>Extreme DGEX (>100M):</strong> Massive acceleration effects. Expect explosive moves and rapid volatility changes.</li>
                        <li><strong>High DGEX (50-100M):</strong> Significant acceleration. Good for momentum trades and volatility plays.</li>
                        <li><strong>Moderate DGEX (10-50M):</strong> Noticeable effects but manageable. Standard trading strategies apply.</li>
                        <li><strong>Low DGEX (<10M):</strong> Minimal acceleration effects. Price moves more predictably and smoothly.</li>
                    </ul>

                    <h6><i class="fas fa-wave-square me-2"></i>DGEX and Market Regimes</h6>
                    <ul>
                        <li><strong>Trending Markets:</strong> High DGEX in direction of trend amplifies moves. Low DGEX against trend dampens corrections.</li>
                        <li><strong>Range-Bound Markets:</strong> Balanced DGEX on both sides creates stable ranges. Unbalanced DGEX suggests breakout direction.</li>
                        <li><strong>Volatile Markets:</strong> Extreme DGEX values often coincide with high volatility periods. Use for volatility trading strategies.</li>
                        <li><strong>Quiet Markets:</strong> Low DGEX across all strikes often precedes major moves as positions build up.</li>
                    </ul>

                    <h6><i class="fas fa-clock me-2"></i>Time-Dependent DGEX Effects</h6>
                    <ul>
                        <li><strong>Expiration Approach:</strong> DGEX effects intensify dramatically as expiration nears due to gamma explosion</li>
                        <li><strong>Intraday Evolution:</strong> DGEX builds throughout the trading day as positions accumulate</li>
                        <li><strong>Weekly Patterns:</strong> DGEX often peaks mid-week and resets on Mondays</li>
                        <li><strong>Event Sensitivity:</strong> DGEX can spike dramatically before earnings and major announcements</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Advanced Risk Management</h6>
                    <ul>
                        <li>Never ignore high DGEX zones - they can turn small moves into large losses quickly</li>
                        <li>Use DGEX to size positions appropriately - smaller size in high DGEX areas</li>
                        <li>Monitor DGEX changes in real-time - new options flow can shift acceleration zones rapidly</li>
                        <li>Combine DGEX with stop-loss strategies - acceleration can blow through normal stops</li>
                        <li>Be prepared for volatility spikes when price enters extreme DGEX zones</li>
                    </ul>
                `
            },
            'tex': {
                title: 'Theta Exposure (TEX) Help',
                content: `
                    <h6><i class="fas fa-clock me-2"></i>What TEX Shows</h6>
                    <p>Theta Exposure (TEX) reveals the time decay dynamics that drive options markets, showing how market makers' positions gain or lose value as time passes. This metric is crucial for understanding the "time premium bleeding" effect and how dealers must adjust their hedging as expiration approaches. TEX shows not just where time decay is occurring, but how it creates predictable flow patterns and trading opportunities.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Advanced Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Optimization:</strong>
                            <ul>
                                <li><em>Bar Charts:</em> Perfect for identifying specific strikes with high time decay exposure. Shows exact levels where theta effects are concentrated.</li>
                                <li><em>Line Charts:</em> Excellent for visualizing theta distribution patterns across the option chain. Shows smooth theta curves and decay gradients.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying theta anomalies and unusual time decay concentrations that might signal opportunities.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Strategy:</strong>
                            <ul>
                                <li><em>ATM Focus (10-20):</em> Concentrate on maximum theta exposure around current price where time decay is most intense</li>
                                <li><em>Standard Range (25-35):</em> Balanced view for most theta strategies and time decay analysis</li>
                                <li><em>Wide Analysis (40-60):</em> Full theta landscape for understanding complete time decay structure across all strikes</li>
                            </ul>
                        </li>
                        <li><strong>Theta Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call theta concentration. High positive call TEX = dealers benefit from call time decay.</li>
                                <li><em>Show Puts:</em> Shows put theta patterns. High positive put TEX = dealers benefit from put time decay.</li>
                                <li><em>Show Net:</em> Combined theta exposure showing overall time decay impact on dealer positions. Most important for strategy selection.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional Trading Strategies</h6>
                    <ul>
                        <li><strong>Time Decay Harvesting:</strong>
                            <ul>
                                <li><em>High Positive TEX Zones:</em> Areas where dealers benefit from time decay. Sell premium strategies work best here as time decay accelerates.</li>
                                <li><em>Theta Farming:</em> Systematically sell options in high positive TEX areas to harvest time decay consistently.</li>
                                <li><em>Weekend Effect:</em> TEX accelerates over weekends and holidays. Position theta-positive strategies before market closures.</li>
                            </ul>
                        </li>
                        <li><strong>Theta Acceleration Trading:</strong>
                            <ul>
                                <li><em>30-Day Rule:</em> Theta acceleration intensifies in final 30 days. Increase theta-positive positioning as expiration approaches.</li>
                                <li><em>Weekly Options:</em> Extreme theta acceleration in final week. Use for high-probability, short-duration trades.</li>
                                <li><em>Daily Options:</em> Maximum theta burn in final 24 hours. Perfect for scalping time decay.</li>
                            </ul>
                        </li>
                        <li><strong>Event-Driven Theta Strategies:</strong>
                            <ul>
                                <li><em>Earnings Theta Crush:</em> Massive theta acceleration after earnings announcements. Sell premium before, buy back after.</li>
                                <li><em>FOMC Theta Plays:</em> Federal Reserve meetings create theta spikes. Position for volatility collapse and theta acceleration.</li>
                                <li><em>Ex-Dividend Theta:</em> Dividend dates affect theta calculations. Adjust strategies around ex-dividend dates.</li>
                            </ul>
                        </li>
                        <li><strong>Theta-Volatility Combinations:</strong>
                            <ul>
                                <li><em>High Theta + Low Volatility:</em> Perfect environment for selling premium. Time decay works in your favor.</li>
                                <li><em>High Theta + High Volatility:</em> Conflicting forces. Use spreads to isolate theta while limiting volatility risk.</li>
                                <li><em>Low Theta + High Volatility:</em> Buy options before theta acceleration kicks in.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-stopwatch me-2"></i>Theta Timing Mastery</h6>
                    <ul>
                        <li><strong>Intraday Theta Patterns:</strong>
                            <ul>
                                <li><em>Market Open:</em> Theta calculations reset. Fresh positioning opportunities.</li>
                                <li><em>Mid-Day:</em> Theta effects accumulate. Monitor for acceleration.</li>
                                <li><em>Market Close:</em> Overnight theta burn begins. Position accordingly.</li>
                            </ul>
                        </li>
                        <li><strong>Weekly Theta Cycles:</strong>
                            <ul>
                                <li><em>Monday:</em> Fresh weekly options with high theta potential.</li>
                                <li><em>Wednesday:</em> Mid-week theta acceleration begins.</li>
                                <li><em>Friday:</em> Maximum weekly theta burn. High-probability theta trades.</li>
                            </ul>
                        </li>
                        <li><strong>Monthly Theta Patterns:</strong>
                            <ul>
                                <li><em>Monthly Expiration Week:</em> Extreme theta acceleration for monthly options.</li>
                                <li><em>OPEX Friday:</em> Maximum theta burn day. Highest probability theta strategies.</li>
                                <li><em>Post-Expiration:</em> New monthly cycle begins. Fresh theta opportunities.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-calculator me-2"></i>Theta Magnitude Analysis</h6>
                    <ul>
                        <li><strong>High TEX (>$10M/day):</strong> Significant time decay exposure. Major theta trading opportunities.</li>
                        <li><strong>Moderate TEX ($1-10M/day):</strong> Standard theta effects. Normal time decay strategies apply.</li>
                        <li><strong>Low TEX (<$1M/day):</strong> Minimal time decay impact. Focus on other Greeks for strategy selection.</li>
                        <li><strong>Negative TEX:</strong> Dealers lose money from time decay. Opportunities to buy premium cheaply.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Theta Risk Management</h6>
                    <ul>
                        <li>Never ignore theta when holding options positions - time decay never stops</li>
                        <li>Use TEX to size theta-sensitive positions appropriately</li>
                        <li>Monitor theta acceleration as expiration approaches - effects intensify rapidly</li>
                        <li>Be aware of weekend and holiday theta burn - positions decay even when markets are closed</li>
                        <li>Combine theta analysis with volatility forecasts for optimal strategy selection</li>
                    </ul>
                `
            },
            'vegx': {
                title: 'Vega Exposure (VEGX) Help',
                content: `
                    <h6><i class="fas fa-wave-square me-2"></i>What VEGX Shows</h6>
                    <p>Vega Exposure (VEGX) reveals the volatility sensitivity landscape that drives options pricing and market maker hedging behavior. This metric shows how dealer positions gain or lose value when implied volatility changes, creating predictable flow patterns during volatility expansions and contractions. VEGX is essential for understanding volatility cycles, timing volatility trades, and predicting when markets will experience volatility clustering or mean reversion.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Comprehensive Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Analysis:</strong>
                            <ul>
                                <li><em>Line Charts:</em> Perfect for visualizing volatility sensitivity trends across strikes. Shows smooth vega curves and sensitivity gradients.</li>
                                <li><em>Bar Charts:</em> Excellent for identifying specific strikes with high volatility exposure. Shows exact levels where vega effects are concentrated.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying vega anomalies and unusual volatility sensitivity patterns that might signal opportunities.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Optimization:</strong>
                            <ul>
                                <li><em>ATM Focus (15-25):</em> Concentrate on maximum vega exposure around current price where volatility sensitivity is highest</li>
                                <li><em>Standard Range (30-40):</em> Balanced view for most volatility strategies and vega analysis</li>
                                <li><em>Wide Analysis (45-60):</em> Full vega landscape for understanding complete volatility sensitivity structure</li>
                            </ul>
                        </li>
                        <li><strong>Vega Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call vega concentration. High positive call VEGX = dealers benefit from volatility increases in calls.</li>
                                <li><em>Show Puts:</em> Shows put vega patterns. High positive put VEGX = dealers benefit from volatility increases in puts.</li>
                                <li><em>Show Net:</em> Combined vega exposure showing overall volatility sensitivity. Critical for understanding market volatility dynamics.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Volatility Strategies</h6>
                    <ul>
                        <li><strong>Volatility Expansion Trading:</strong>
                            <ul>
                                <li><em>High Positive VEGX Zones:</em> Areas where dealers benefit from volatility increases. Expect volatility support and mean reversion resistance.</li>
                                <li><em>Volatility Breakout Plays:</em> When volatility breaks above high VEGX levels, expect acceleration as dealers hedge aggressively.</li>
                                <li><em>VIX Correlation Trading:</em> Use VEGX levels to predict VIX movements and trade volatility ETFs accordingly.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Contraction Strategies:</strong>
                            <ul>
                                <li><em>High Negative VEGX Areas:</em> Zones where dealers benefit from volatility decreases. Expect volatility compression and mean reversion.</li>
                                <li><em>Volatility Crush Plays:</em> Position for volatility collapse in high negative VEGX areas, especially after events.</li>
                                <li><em>Premium Selling Optimization:</em> Use VEGX to identify optimal strikes for selling premium during volatility contractions.</li>
                            </ul>
                        </li>
                        <li><strong>Event-Driven Volatility Trading:</strong>
                            <ul>
                                <li><em>Earnings Volatility Plays:</em> High VEGX before earnings often leads to volatility crush after. Sell premium before, buy back after.</li>
                                <li><em>FOMC Volatility Strategies:</em> Federal Reserve meetings create massive VEGX shifts. Position for volatility expansion before, contraction after.</li>
                                <li><em>Economic Data Releases:</em> Use VEGX to position for volatility spikes around major economic announcements.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Arbitrage:</strong>
                            <ul>
                                <li><em>VEGX Spread Trading:</em> Trade volatility differences between strikes with different VEGX exposures.</li>
                                <li><em>Calendar Volatility Plays:</em> Use VEGX to identify optimal calendar spread opportunities across expirations.</li>
                                <li><em>Cross-Asset Volatility:</em> Compare VEGX across different underlyings for relative volatility trades.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>VEGX and Market Cycles</h6>
                    <ul>
                        <li><strong>Volatility Clustering:</strong>
                            <ul>
                                <li><em>High VEGX Persistence:</em> When VEGX is elevated, volatility tends to remain high. Use for momentum volatility strategies.</li>
                                <li><em>VEGX Momentum:</em> Rising VEGX often precedes volatility spikes. Position for expansion when VEGX is building.</li>
                                <li><em>Volatility Regime Changes:</em> Major VEGX shifts often mark transitions between low and high volatility regimes.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Mean Reversion:</strong>
                            <ul>
                                <li><em>Extreme VEGX Levels:</em> Very high or low VEGX often marks volatility extremes. Position for mean reversion.</li>
                                <li><em>VEGX Exhaustion:</em> When VEGX reaches extreme levels, volatility often reverses direction.</li>
                                <li><em>Historical VEGX Analysis:</em> Compare current VEGX to historical levels to identify mean reversion opportunities.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-sync-alt me-2"></i>VEGX Integration Strategies</h6>
                    <ul>
                        <li><strong>VEGX + VIX Analysis:</strong> High VEGX with low VIX suggests volatility expansion potential. Low VEGX with high VIX suggests contraction.</li>
                        <li><strong>VEGX + Gamma Correlation:</strong> High VEGX with high gamma creates explosive volatility potential. Monitor for breakouts.</li>
                        <li><strong>VEGX + Volume Confirmation:</strong> VEGX backed by high options volume is more reliable than theoretical VEGX alone.</li>
                        <li><strong>VEGX + Technical Analysis:</strong> Combine VEGX with chart patterns for optimal volatility trade timing.</li>
                    </ul>

                    <h6><i class="fas fa-calendar-alt me-2"></i>Time-Sensitive VEGX Effects</h6>
                    <ul>
                        <li><strong>Expiration Approach:</strong> VEGX effects intensify as expiration nears due to increased volatility sensitivity</li>
                        <li><strong>Weekly Volatility Cycles:</strong> VEGX often peaks mid-week and declines into weekends</li>
                        <li><strong>Monthly Patterns:</strong> VEGX typically builds toward monthly expiration and resets afterward</li>
                        <li><strong>Seasonal Volatility:</strong> VEGX patterns often follow seasonal volatility trends (higher in fall, lower in summer)</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Professional VEGX Risk Management</h6>
                    <ul>
                        <li>Always consider volatility environment when trading high VEGX positions</li>
                        <li>Use VEGX to hedge portfolio volatility exposure appropriately</li>
                        <li>Monitor VEGX changes for early warning of volatility regime shifts</li>
                        <li>Be aware of volatility clustering - high VEGX periods tend to persist</li>
                        <li>Combine VEGX analysis with fundamental catalysts for highest probability trades</li>
                    </ul>
                `
            },
            'vex': {
                title: 'Vanna Exposure (VEX) Help',
                content: `
                    <h6><i class="fas fa-exchange-alt me-2"></i>What VEX Shows</h6>
                    <p>Vanna Exposure (VEX) reveals the sophisticated cross-sensitivity between directional exposure (delta) and volatility sensitivity (vega), showing how market maker hedging obligations change when both price and volatility move simultaneously. This second-order Greek effect is crucial for understanding complex market dynamics, predicting dealer flow during volatility events, and identifying opportunities in multi-dimensional options strategies.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Advanced Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Selection:</strong>
                            <ul>
                                <li><em>Scatter Plots:</em> Perfect for visualizing vanna relationships and identifying non-linear patterns. Shows correlation between delta and volatility effects.</li>
                                <li><em>Line Charts:</em> Excellent for seeing vanna trends across strikes and understanding smooth vanna curves.</li>
                                <li><em>Bar Charts:</em> Good for identifying specific strikes with high vanna concentration and comparing relative magnitudes.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Strategy:</strong>
                            <ul>
                                <li><em>OTM Focus (20-40):</em> Out-of-the-money options often have highest vanna exposure. Critical for understanding tail risk dynamics.</li>
                                <li><em>ATM Analysis (15-25):</em> At-the-money vanna for understanding immediate cross-sensitivity effects.</li>
                                <li><em>Full Range (40-60):</em> Complete vanna landscape for understanding entire cross-sensitivity structure.</li>
                            </ul>
                        </li>
                        <li><strong>Vanna Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call vanna patterns. Positive call vanna = calls become more directionally sensitive as volatility rises.</li>
                                <li><em>Show Puts:</em> Shows put vanna dynamics. Negative put vanna = puts become less directionally sensitive as volatility rises.</li>
                                <li><em>Show Net:</em> Combined vanna exposure showing overall cross-sensitivity. Most important for predicting complex market behavior.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional Vanna Strategies</h6>
                    <ul>
                        <li><strong>Volatility-Directional Trading:</strong>
                            <ul>
                                <li><em>Positive Vanna Momentum:</em> When vanna is positive, rising volatility increases directional sensitivity. Use for momentum strategies during volatility spikes.</li>
                                <li><em>Negative Vanna Mean Reversion:</em> When vanna is negative, rising volatility decreases directional sensitivity. Use for mean reversion strategies.</li>
                                <li><em>Vanna Flip Trading:</em> When vanna changes sign, market behavior shifts dramatically. Position for regime changes.</li>
                            </ul>
                        </li>
                        <li><strong>Complex Hedging Strategies:</strong>
                            <ul>
                                <li><em>Vanna-Neutral Portfolios:</em> Construct positions that are insensitive to volatility-directional interactions.</li>
                                <li><em>Dynamic Vanna Hedging:</em> Adjust hedge ratios based on vanna exposure as market conditions change.</li>
                                <li><em>Cross-Gamma Strategies:</em> Use vanna to predict how gamma hedging will change during volatility events.</li>
                            </ul>
                        </li>
                        <li><strong>Event-Driven Vanna Plays:</strong>
                            <ul>
                                <li><em>Earnings Vanna Effects:</em> High vanna before earnings creates complex post-earnings behavior. Position for volatility-directional interactions.</li>
                                <li><em>FOMC Vanna Dynamics:</em> Federal Reserve meetings create massive vanna shifts. Understand cross-sensitivity changes.</li>
                                <li><em>Economic Data Vanna:</em> Major economic releases affect both volatility and direction. Use vanna to predict combined effects.</li>
                            </ul>
                        </li>
                        <li><strong>Market Maker Flow Prediction:</strong>
                            <ul>
                                <li><em>Vanna-Driven Hedging:</em> Predict how dealers will hedge when both price and volatility move together.</li>
                                <li><em>Flow Amplification:</em> Positive vanna amplifies dealer flow during volatility spikes. Negative vanna dampens flow.</li>
                                <li><em>Vanna Squeeze Setups:</em> When vanna forces create conflicting hedging obligations, explosive moves often result.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-brain me-2"></i>Vanna Psychology and Market Behavior</h6>
                    <ul>
                        <li><strong>Momentum vs Mean Reversion:</strong>
                            <ul>
                                <li><em>High Positive Vanna:</em> Creates momentum behavior as volatility and direction reinforce each other.</li>
                                <li><em>High Negative Vanna:</em> Creates mean reversion as volatility and direction oppose each other.</li>
                                <li><em>Balanced Vanna:</em> Neutral cross-sensitivity leads to more predictable price behavior.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Regime Interactions:</strong>
                            <ul>
                                <li><em>Low Vol + Positive Vanna:</em> Potential for explosive moves if volatility spikes.</li>
                                <li><em>High Vol + Negative Vanna:</em> Potential for rapid mean reversion as volatility normalizes.</li>
                                <li><em>Vanna Regime Changes:</em> When vanna flips sign, expect fundamental changes in market behavior.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>Advanced Vanna Analysis</h6>
                    <ul>
                        <li><strong>Vanna Magnitude Interpretation:</strong>
                            <ul>
                                <li><em>Extreme Vanna (>$50M):</em> Major cross-sensitivity effects. Expect complex market behavior.</li>
                                <li><em>High Vanna ($10-50M):</em> Significant cross-effects. Important for strategy selection.</li>
                                <li><em>Moderate Vanna ($1-10M):</em> Noticeable effects but manageable with standard strategies.</li>
                                <li><em>Low Vanna (<$1M):</em> Minimal cross-sensitivity. Focus on other Greeks.</li>
                            </ul>
                        </li>
                        <li><strong>Vanna Term Structure:</strong>
                            <ul>
                                <li><em>Front Month Vanna:</em> Immediate cross-sensitivity effects for short-term strategies.</li>
                                <li><em>Back Month Vanna:</em> Longer-term cross-sensitivity for position management.</li>
                                <li><em>Vanna Calendar Effects:</em> Different expirations have different vanna characteristics.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Vanna Risk Management</h6>
                    <ul>
                        <li>Never ignore vanna in complex options strategies - cross-sensitivity can dominate P&L</li>
                        <li>Use vanna to understand how positions will behave during volatility events</li>
                        <li>Monitor vanna changes as they can signal major shifts in market dynamics</li>
                        <li>Be aware of vanna concentration risk - avoid overexposure to single high-vanna strikes</li>
                        <li>Combine vanna analysis with volatility forecasts for optimal position management</li>
                    </ul>
                `
            },
            'cex': {
                title: 'Charm Exposure (CEX) Help',
                content: `
                    <h6><i class="fas fa-magic me-2"></i>What CEX Shows</h6>
                    <p>Charm Exposure (CEX) reveals the sophisticated time-directional dynamics that govern how market maker hedging obligations evolve as expiration approaches. This third-order Greek effect measures how delta exposure changes with time passage, showing how directional bias intensifies or diminishes as options approach expiration. CEX is crucial for understanding time-sensitive hedging flows and predicting how dealer positioning will shift throughout the options lifecycle.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Comprehensive Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Analysis:</strong>
                            <ul>
                                <li><em>Line Charts:</em> Perfect for visualizing charm trends across strikes and seeing smooth time-delta evolution patterns.</li>
                                <li><em>Bar Charts:</em> Excellent for identifying specific strikes with high charm concentration and comparing relative magnitudes.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying charm anomalies and unusual time-delta relationships that might signal opportunities.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Optimization:</strong>
                            <ul>
                                <li><em>ATM Focus (10-20):</em> At-the-money options have highest charm exposure. Critical for understanding immediate time-delta effects.</li>
                                <li><em>Standard Range (25-35):</em> Balanced view for most charm strategies and time-delta analysis.</li>
                                <li><em>Wide Analysis (40-50):</em> Full charm landscape for understanding complete time-directional structure.</li>
                            </ul>
                        </li>
                        <li><strong>Charm Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call charm patterns. Positive call charm = call delta increases with time passage.</li>
                                <li><em>Show Puts:</em> Shows put charm dynamics. Negative put charm = put delta magnitude decreases with time passage.</li>
                                <li><em>Show Net:</em> Combined charm exposure showing overall time-directional effects. Most important for predicting hedging evolution.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Advanced Charm Strategies</h6>
                    <ul>
                        <li><strong>Time-Directional Trading:</strong>
                            <ul>
                                <li><em>Positive Charm Acceleration:</em> Delta increases with time passage. Directional bias intensifies as expiration approaches.</li>
                                <li><em>Negative Charm Deceleration:</em> Delta decreases with time passage. Directional bias weakens over time.</li>
                                <li><em>Charm Reversal Points:</em> Where charm changes sign, directional bias evolution reverses. Key inflection points.</li>
                            </ul>
                        </li>
                        <li><strong>Expiration-Driven Strategies:</strong>
                            <ul>
                                <li><em>Charm Acceleration Trading:</em> High charm areas experience rapid delta changes near expiration. Position for directional acceleration.</li>
                                <li><em>Pin Risk Charm Effects:</em> Charm intensifies pin risk as ATM options approach expiration with accelerating delta.</li>
                                <li><em>Weekly Charm Plays:</em> Weekly options have extreme charm effects in final days. Use for high-probability directional trades.</li>
                            </ul>
                        </li>
                        <li><strong>Dynamic Hedging Strategies:</strong>
                            <ul>
                                <li><em>Charm-Adjusted Hedging:</em> Modify hedge ratios based on charm exposure to account for time-driven delta changes.</li>
                                <li><em>Predictive Hedging:</em> Use charm to predict future hedging needs and position accordingly.</li>
                                <li><em>Charm-Neutral Portfolios:</em> Construct positions that are insensitive to time-driven directional changes.</li>
                            </ul>
                        </li>
                        <li><strong>Multi-Leg Charm Optimization:</strong>
                            <ul>
                                <li><em>Calendar Spread Charm:</em> Use charm differences between expirations to optimize calendar spread performance.</li>
                                <li><em>Diagonal Spread Timing:</em> Charm analysis helps time diagonal spread adjustments for maximum profitability.</li>
                                <li><em>Iron Condor Charm Effects:</em> Understand how charm affects iron condor delta neutrality over time.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-clock me-2"></i>Time-Sensitive Charm Effects</h6>
                    <ul>
                        <li><strong>Expiration Approach Dynamics:</strong>
                            <ul>
                                <li><em>30-Day Charm Acceleration:</em> Charm effects intensify significantly in final 30 days before expiration.</li>
                                <li><em>Weekly Charm Explosion:</em> Final week shows extreme charm effects as delta becomes highly time-sensitive.</li>
                                <li><em>Daily Charm Extremes:</em> Final 24 hours show maximum charm effects with rapid delta evolution.</li>
                            </ul>
                        </li>
                        <li><strong>Intraday Charm Patterns:</strong>
                            <ul>
                                <li><em>Market Open:</em> Charm calculations reset with new time to expiration values.</li>
                                <li><em>Mid-Day Evolution:</em> Charm effects accumulate throughout the trading session.</li>
                                <li><em>Market Close:</em> Overnight charm effects begin as time decay continues.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>Charm Magnitude Analysis</h6>
                    <ul>
                        <li><strong>Extreme Charm (>$5M/day):</strong> Major time-directional effects. Expect rapid delta evolution.</li>
                        <li><strong>High Charm ($1-5M/day):</strong> Significant time-delta sensitivity. Important for strategy timing.</li>
                        <li><strong>Moderate Charm ($0.1-1M/day):</strong> Noticeable effects but manageable with standard approaches.</li>
                        <li><strong>Low Charm (<$0.1M/day):</strong> Minimal time-directional impact. Focus on other Greeks.</li>
                    </ul>

                    <h6><i class="fas fa-sync-alt me-2"></i>Charm Integration with Other Greeks</h6>
                    <ul>
                        <li><strong>Charm + Gamma Analysis:</strong> High charm with high gamma creates explosive directional acceleration near expiration.</li>
                        <li><strong>Charm + Theta Correlation:</strong> Charm and theta often work together to create time-driven position changes.</li>
                        <li><strong>Charm + Vanna Effects:</strong> Combined charm-vanna exposure creates complex time-volatility-directional interactions.</li>
                        <li><strong>Charm + Delta Evolution:</strong> Monitor how charm predictions align with actual delta changes over time.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Charm Risk Management</h6>
                    <ul>
                        <li>Always consider charm when holding positions through expiration approach</li>
                        <li>Use charm to predict how directional exposure will evolve over time</li>
                        <li>Monitor charm acceleration as expiration nears - effects intensify rapidly</li>
                        <li>Be aware of charm concentration risk in ATM positions near expiration</li>
                        <li>Combine charm analysis with volatility forecasts for complete risk assessment</li>
                    </ul>
                `
            },
            'oi': {
                title: 'Open Interest Help',
                content: `
                    <h6><i class="fas fa-users me-2"></i>What Open Interest Shows</h6>
                    <p>Open Interest (OI) represents the total number of outstanding option contracts that have been opened but not yet closed, exercised, or expired. This metric reveals where the largest concentrations of options positions exist, indicating significant market participant interest and potential price influence. High OI areas often become self-fulfilling prophecies as market makers and large traders defend their positions, creating natural support and resistance levels.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Comprehensive Settings Guide</h6>
                    <ul>
                        <li><strong>Chart Type Selection:</strong>
                            <ul>
                                <li><em>Bar Charts:</em> Perfect for visualizing OI distribution across strikes. Shows clear concentration levels and relative magnitudes.</li>
                                <li><em>Line Charts:</em> Excellent for seeing OI trends and smooth distribution patterns across the option chain.</li>
                                <li><em>Scatter Plots:</em> Useful for identifying OI outliers and unusual concentrations that might signal institutional activity.</li>
                            </ul>
                        </li>
                        <li><strong>Strike Range Strategy:</strong>
                            <ul>
                                <li><em>Narrow Range (15-25):</em> Focus on immediate OI around current price for short-term trading</li>
                                <li><em>Standard Range (30-40):</em> Balanced view for most OI analysis and support/resistance identification</li>
                                <li><em>Wide Range (50-80):</em> Full market structure view for understanding complete institutional positioning</li>
                            </ul>
                        </li>
                        <li><strong>OI Component Analysis:</strong>
                            <ul>
                                <li><em>Show Calls:</em> Reveals call open interest concentration. High call OI often indicates resistance levels or bullish positioning.</li>
                                <li><em>Show Puts:</em> Shows put open interest patterns. High put OI typically indicates support levels or bearish hedging.</li>
                                <li><em>Show Net:</em> Combined view showing overall OI distribution. Most important for understanding market structure.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional OI Strategies</h6>
                    <ul>
                        <li><strong>Pin Risk Trading:</strong>
                            <ul>
                                <li><em>Maximum OI Strikes:</em> Strikes with highest OI often act as price magnets, especially near expiration. Price tends to "pin" at these levels.</li>
                                <li><em>OI Walls:</em> Massive OI concentrations create strong support/resistance that's difficult to break through.</li>
                                <li><em>Expiration Pin Effects:</em> OI effects intensify dramatically in final hours before expiration as positions must be closed or exercised.</li>
                            </ul>
                        </li>
                        <li><strong>Market Structure Analysis:</strong>
                            <ul>
                                <li><em>OI Distribution Patterns:</em> Symmetric OI suggests range-bound expectations. Asymmetric OI suggests directional bias.</li>
                                <li><em>Call/Put OI Ratios:</em> High call/put ratios indicate bullish sentiment. High put/call ratios suggest bearish sentiment or hedging activity.</li>
                                <li><em>OI Migration:</em> Watch OI shift between strikes to understand changing market expectations.</li>
                            </ul>
                        </li>
                        <li><strong>Institutional Activity Detection:</strong>
                            <ul>
                                <li><em>Unusual OI Spikes:</em> Sudden large increases in OI often indicate institutional positioning. Follow the smart money.</li>
                                <li><em>OI vs Volume Analysis:</em> High OI with low volume suggests established positions. High volume with low OI suggests new positioning.</li>
                                <li><em>Dark Pool OI:</em> Large OI appearing overnight often indicates institutional block trades executed after hours.</li>
                            </ul>
                        </li>
                        <li><strong>Max Pain Theory Application:</strong>
                            <ul>
                                <li><em>Max Pain Calculation:</em> Price level where most options expire worthless, causing maximum pain to option buyers.</li>
                                <li><em>Gravitational Pull:</em> Price often gravitates toward max pain level as expiration approaches due to dealer hedging.</li>
                                <li><em>Max Pain Breaks:</em> When price moves significantly away from max pain, expect strong counter-moves or volatility spikes.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-bar me-2"></i>OI Magnitude Interpretation</h6>
                    <ul>
                        <li><strong>Massive OI (>50,000 contracts):</strong> Institutional-level positioning. Extremely strong support/resistance levels.</li>
                        <li><strong>High OI (10,000-50,000):</strong> Significant market interest. Strong levels that often hold multiple tests.</li>
                        <li><strong>Moderate OI (1,000-10,000):</strong> Standard retail/small institutional interest. Moderate support/resistance.</li>
                        <li><strong>Low OI (<1,000):</strong> Limited interest. Levels may not provide reliable support/resistance.</li>
                    </ul>

                    <h6><i class="fas fa-clock me-2"></i>Time-Sensitive OI Effects</h6>
                    <ul>
                        <li><strong>Expiration Approach:</strong>
                            <ul>
                                <li><em>30-Day Effect:</em> OI begins to influence price action more strongly in final 30 days.</li>
                                <li><em>Weekly Intensification:</em> OI effects become dominant in final week before expiration.</li>
                                <li><em>Expiration Friday:</em> Maximum OI influence as positions must be closed or exercised.</li>
                            </ul>
                        </li>
                        <li><strong>OI Lifecycle Patterns:</strong>
                            <ul>
                                <li><em>Early Cycle:</em> OI builds as new positions are established.</li>
                                <li><em>Mid Cycle:</em> OI peaks as maximum interest develops.</li>
                                <li><em>Late Cycle:</em> OI decreases as positions are closed before expiration.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-brain me-2"></i>OI Psychology and Market Sentiment</h6>
                    <ul>
                        <li><strong>Fear vs Greed Indicators:</strong>
                            <ul>
                                <li><em>High Put OI:</em> Often indicates fear and hedging activity. May signal oversold conditions.</li>
                                <li><em>High Call OI:</em> Often indicates optimism and speculation. May signal overbought conditions.</li>
                                <li><em>Balanced OI:</em> Suggests neutral sentiment and range-bound expectations.</li>
                            </ul>
                        </li>
                        <li><strong>Contrarian Indicators:</strong>
                            <ul>
                                <li><em>Extreme OI Concentrations:</em> Very high OI at specific strikes often marks turning points.</li>
                                <li><em>OI Exhaustion:</em> When OI reaches extreme levels, expect mean reversion or breakouts.</li>
                                <li><em>OI Divergence:</em> When price moves away from high OI areas, momentum often accelerates.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>OI Risk Management</h6>
                    <ul>
                        <li>Always check OI before entering positions - high OI areas can trap price movement</li>
                        <li>Use OI levels as natural stop-loss and take-profit targets</li>
                        <li>Monitor OI changes for early warning of shifting market structure</li>
                        <li>Be aware of expiration effects - OI influence intensifies near expiration</li>
                        <li>Combine OI analysis with volume data for complete market understanding</li>
                    </ul>
                `
            },
            'gex-visualizer': {
                title: 'GEX Visualizer Help',
                content: `
                    <h6><i class="fas fa-chart-area me-2"></i>What the GEX Visualizer Shows</h6>
                    <p>The GEX Visualizer provides a dynamic, time-series view of gamma exposure evolution, tracking how market maker positioning and hedging obligations change throughout trading sessions. This powerful tool reveals the real-time building and destruction of gamma walls, showing when market structure shifts occur and predicting future volatility regimes. By visualizing GEX changes over time, traders can identify trend reversals, breakout setups, and optimal entry/exit timing.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Advanced Settings Guide</h6>
                    <ul>
                        <li><strong>GEX Type Selection:</strong>
                            <ul>
                                <li><em>Open Interest Based:</em> Shows theoretical gamma exposure based on outstanding contracts. Good for understanding overall market structure.</li>
                                <li><em>Volume Weighted:</em> Weights gamma by actual trading volume. More reliable for identifying active hedging levels.</li>
                                <li><em>Both Combined:</em> Comprehensive view combining theoretical and volume-weighted GEX. Best for complete analysis.</li>
                            </ul>
                        </li>
                        <li><strong>Time Range Controls:</strong>
                            <ul>
                                <li><em>Intraday View:</em> Focus on current session GEX evolution for day trading strategies.</li>
                                <li><em>Multi-Day View:</em> Extended time series for swing trading and trend analysis.</li>
                                <li><em>Custom Ranges:</em> Specific time periods around events or market moves.</li>
                            </ul>
                        </li>
                        <li><strong>Refresh Strategy:</strong>
                            <ul>
                                <li><em>Real-Time Updates:</em> Continuous refresh for active trading and scalping.</li>
                                <li><em>Periodic Updates:</em> Scheduled refresh for position monitoring and trend analysis.</li>
                                <li><em>Manual Refresh:</em> On-demand updates for specific analysis needs.</li>
                            </ul>
                        </li>
                        <li><strong>History Management:</strong>
                            <ul>
                                <li><em>Clear History:</em> Reset time series for fresh analysis periods.</li>
                                <li><em>Save Snapshots:</em> Preserve important GEX evolution patterns for future reference.</li>
                                <li><em>Compare Periods:</em> Overlay different time periods for pattern recognition.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Professional Trading Strategies</h6>
                    <ul>
                        <li><strong>Gamma Wall Evolution Trading:</strong>
                            <ul>
                                <li><em>Wall Building:</em> Watch GEX levels strengthen over time. Increasing GEX indicates growing support/resistance.</li>
                                <li><em>Wall Degradation:</em> Monitor GEX levels weakening. Decreasing GEX suggests potential breakouts.</li>
                                <li><em>Wall Migration:</em> Track GEX shifting between strikes. Shows changing market expectations and positioning.</li>
                            </ul>
                        </li>
                        <li><strong>Market Structure Shift Detection:</strong>
                            <ul>
                                <li><em>GEX Regime Changes:</em> Sudden GEX pattern changes often precede major market moves. Position for volatility expansion.</li>
                                <li><em>Zero GEX Crossings:</em> When aggregate GEX crosses zero, market behavior fundamentally changes. Adjust strategies accordingly.</li>
                                <li><em>GEX Acceleration:</em> Rapidly increasing GEX often indicates institutional positioning. Follow the smart money flow.</li>
                            </ul>
                        </li>
                        <li><strong>Volatility Prediction Strategies:</strong>
                            <ul>
                                <li><em>GEX Compression:</em> Decreasing GEX over time often precedes volatility expansion. Buy options before spikes.</li>
                                <li><em>GEX Expansion:</em> Increasing GEX typically leads to volatility compression. Sell premium during high GEX periods.</li>
                                <li><em>GEX Divergence:</em> When GEX trends diverge from price trends, expect major moves or reversals.</li>
                            </ul>
                        </li>
                        <li><strong>Timing Optimization:</strong>
                            <ul>
                                <li><em>Entry Timing:</em> Use GEX evolution to time entries at optimal support/resistance levels.</li>
                                <li><em>Exit Timing:</em> Monitor GEX degradation to time exits before level breaks.</li>
                                <li><em>Reversal Timing:</em> GEX trend changes often signal optimal reversal entry points.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>GEX Evolution Patterns</h6>
                    <ul>
                        <li><strong>Intraday Patterns:</strong>
                            <ul>
                                <li><em>Morning Build-Up:</em> GEX typically builds during opening hours as positions are established.</li>
                                <li><em>Mid-Day Stability:</em> GEX often stabilizes during lunch hours with lower volume.</li>
                                <li><em>Afternoon Dynamics:</em> GEX can shift dramatically during afternoon institutional activity.</li>
                                <li><em>Closing Effects:</em> GEX often changes near close as positions are adjusted for overnight risk.</li>
                            </ul>
                        </li>
                        <li><strong>Weekly Patterns:</strong>
                            <ul>
                                <li><em>Monday Reset:</em> GEX often resets on Mondays as new weekly positions are established.</li>
                                <li><em>Mid-Week Peak:</em> GEX typically peaks Wednesday-Thursday as maximum positioning develops.</li>
                                <li><em>Friday Decay:</em> GEX often decreases Friday as weekly positions expire or are closed.</li>
                            </ul>
                        </li>
                        <li><strong>Event-Driven Patterns:</strong>
                            <ul>
                                <li><em>Pre-Event Build:</em> GEX often increases before earnings, FOMC, or major announcements.</li>
                                <li><em>Event Reaction:</em> GEX can shift dramatically during and after major events.</li>
                                <li><em>Post-Event Normalization:</em> GEX typically returns to normal patterns after event volatility subsides.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-brain me-2"></i>Advanced Analysis Techniques</h6>
                    <ul>
                        <li><strong>Trend Analysis:</strong> Use moving averages on GEX time series to identify longer-term trends and reversals.</li>
                        <li><strong>Momentum Analysis:</strong> Calculate GEX rate of change to identify acceleration and deceleration phases.</li>
                        <li><strong>Correlation Analysis:</strong> Compare GEX evolution with price movement, volume, and volatility for comprehensive insights.</li>
                        <li><strong>Pattern Recognition:</strong> Identify recurring GEX patterns that precede specific market behaviors.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>GEX Visualizer Risk Management</h6>
                    <ul>
                        <li>Don't rely solely on GEX trends - always confirm with price action and volume</li>
                        <li>Be aware of false signals during low-volume periods when GEX may be less reliable</li>
                        <li>Monitor for GEX manipulation around major events or expiration dates</li>
                        <li>Use multiple timeframes to confirm GEX trend changes before making major position adjustments</li>
                        <li>Remember that GEX is predictive but not guaranteed - always use proper risk management</li>
                    </ul>
                `
            },

            'volatility': {
                title: 'Volatility Surface & Skew Help',
                content: `
                    <h6><i class="fas fa-mountain me-2"></i>What These Charts Show</h6>
                    <p>The volatility tools provide comprehensive analysis of implied volatility patterns across strikes and expirations. The <strong>Volatility Skew</strong> shows how implied volatility varies across different strike prices for a single expiration, revealing market sentiment and fear levels. The <strong>3D Volatility Surface</strong> displays the complete volatility landscape across all strikes and expirations, showing how volatility expectations change over time and price levels.</p>

                    <h6><i class="fas fa-lightbulb me-2"></i>Key Trading Insights</h6>
                    <ul>
                        <li><strong>Volatility Skew Analysis:</strong> A steep downward skew (higher IV on puts) indicates fear and bearish sentiment. A flat or upward skew suggests complacency or bullish sentiment. Use skew changes to gauge market emotion and position accordingly.</li>
                        <li><strong>Surface Patterns:</strong> The 3D surface reveals volatility term structure - how volatility expectations change over time. Look for inversions where short-term volatility exceeds long-term, often signaling major events or market stress.</li>
                        <li><strong>ATM vs OTM Relationships:</strong> Compare at-the-money volatility to out-of-the-money levels. Large differences indicate directional bias - higher put volatility suggests downside protection demand, higher call volatility suggests upside speculation.</li>
                        <li><strong>Volatility Smile Detection:</strong> U-shaped volatility curves indicate market uncertainty in both directions. This pattern often appears before earnings or major announcements, creating opportunities for straddle/strangle strategies.</li>
                    </ul>

                    <h6><i class="fas fa-chart-bar me-2"></i>Advanced Interpretation Techniques</h6>
                    <ul>
                        <li><strong>Skew Momentum Analysis:</strong>
                            <ul>
                                <li><em>Steepening Put Skew:</em> Increasing fear, consider protective puts or put spreads. Market expects downside volatility.</li>
                                <li><em>Flattening Put Skew:</em> Decreasing fear, consider selling put premium or bullish strategies. Market becoming more confident.</li>
                                <li><em>Rising Call Skew:</em> Increasing speculation, consider call spreads or covered calls. Market expects upside volatility.</li>
                                <li><em>Falling Call Skew:</em> Decreasing speculation, consider buying calls or reducing hedges. Market becoming less optimistic.</li>
                            </ul>
                        </li>
                        <li><strong>Surface Arbitrage Opportunities:</strong>
                            <ul>
                                <li><em>Calendar Spreads:</em> Look for volatility inversions where near-term IV exceeds long-term. Sell near-term, buy long-term volatility.</li>
                                <li><em>Butterfly Spreads:</em> Identify volatility humps or valleys in the surface. Trade around these anomalies for mean reversion.</li>
                                <li><em>Risk Reversals:</em> Use skew differences to create synthetic positions with favorable volatility characteristics.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-cogs me-2"></i>Settings Optimization Guide</h6>
                    <ul>
                        <li><strong>View Type Selection:</strong>
                            <ul>
                                <li><em>Volatility Skew:</em> Best for analyzing single expiration patterns and identifying directional bias. Use for quick sentiment analysis and strike selection.</li>
                                <li><em>3D Surface:</em> Ideal for comprehensive volatility analysis across all expirations. Use for complex strategy planning and term structure analysis.</li>
                                <li><em>Both Views:</em> Provides complete picture for advanced traders. Use when planning multi-leg strategies or analyzing volatility relationships.</li>
                            </ul>
                        </li>
                        <li><strong>Expiration Selection:</strong> Choose expirations based on your trading timeframe. Weekly options for short-term trades, monthly for swing trades, quarterly for position trades. Compare multiple expirations to identify the best volatility value.</li>
                        <li><strong>Chart Type Analysis:</strong> Line charts show smooth volatility curves for trend analysis. Scatter plots help identify volatility anomalies and outliers that may represent opportunities.</li>
                    </ul>

                    <h6><i class="fas fa-bullseye me-2"></i>Practical Trading Applications</h6>
                    <ul>
                        <li><strong>Volatility Trading:</strong> Buy volatility when IV is below historical levels or when skew is unusually flat. Sell volatility when IV is elevated or when skew is extremely steep. Use the surface to identify the best strikes and expirations for volatility trades.</li>
                        <li><strong>Directional Strategies:</strong> Use skew to optimize strike selection. In high put skew environments, consider selling puts or buying calls. In high call skew environments, consider selling calls or buying puts.</li>
                        <li><strong>Income Strategies:</strong> Target high IV areas of the surface for premium selling strategies. Look for volatility humps where you can sell strangles or iron condors at attractive prices.</li>
                        <li><strong>Hedging Optimization:</strong> Use the surface to find the most cost-effective hedges. Sometimes longer-dated options provide better volatility value than near-term options.</li>
                    </ul>

                    <h6><i class="fas fa-graduation-cap me-2"></i>Volatility Statistics Explained</h6>
                    <ul>
                        <li><strong>ATM IV:</strong> At-the-money implied volatility represents the market's expectation of future volatility. Compare to historical volatility to identify over/undervalued options.</li>
                        <li><strong>IV Skew:</strong> The difference between put and call volatility. Positive skew indicates higher put volatility (fear), negative skew indicates higher call volatility (greed).</li>
                        <li><strong>Put/Call IV Ratio:</strong> Ratio of put to call implied volatility. Values above 1.0 indicate higher put volatility (bearish sentiment), below 1.0 indicate higher call volatility (bullish sentiment).</li>
                        <li><strong>IV Rank/Percentile:</strong> Shows where current volatility stands relative to historical ranges. High rank suggests expensive options, low rank suggests cheap options.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Volatility Analysis Risk Management</h6>
                    <ul>
                        <li>Volatility can change rapidly - always monitor positions and adjust as surface patterns shift</li>
                        <li>Don't chase extreme volatility levels - they often mean-revert quickly</li>
                        <li>Consider transaction costs when trading volatility - bid-ask spreads can be wide on illiquid options</li>
                        <li>Use multiple expirations to confirm volatility patterns before making large bets</li>
                        <li>Remember that implied volatility is forward-looking - past patterns don't guarantee future behavior</li>
                        <li>Be aware of upcoming events (earnings, Fed meetings) that can cause volatility spikes or collapses</li>
                    </ul>
                `
            },

            'exposure-surface': {
                title: 'Exposure Surface Heatmap Help',
                content: `
                    <h6><i class="fas fa-fire me-2"></i>What This Chart Shows</h6>
                    <p>The <strong>Exposure Surface</strong> displays how exposure changes over time and price levels for options approaching expiration. This heatmap visualization shows the complete exposure landscape, revealing where market makers face the most hedging pressure as time decays and prices move.</p>

                    <h6><i class="fas fa-chart-area me-2"></i>How to Read the Heatmap</h6>
                    <ul>
                        <li><strong>X-Axis (Time):</strong> Minutes remaining until expiration (countdown from left to right)</li>
                        <li><strong>Y-Axis (Price):</strong> Simulated stock price levels around current price</li>
                        <li><strong>Colors:</strong> Red areas = negative exposure (put-heavy), Green areas = positive exposure (call-heavy), White = neutral</li>
                        <li><strong>Yellow Dashed Line:</strong> Current stock price level for reference</li>
                        <li><strong>Intensity:</strong> Darker colors indicate stronger exposure concentrations</li>
                    </ul>

                    <h6><i class="fas fa-cogs me-2"></i>Settings & Controls</h6>
                    <ul>
                        <li><strong>Price Range:</strong> Adjust the percentage range around current price (±10% to ±25%)</li>
                        <li><strong>Time Range:</strong> Set how far back in time to simulate (1 hour to 24 hours)</li>
                        <li><strong>Price Step:</strong> Control price level granularity ($0.25 to $2.00 increments)</li>
                        <li><strong>Time Step:</strong> Set time interval granularity (1 to 15 minute steps)</li>
                        <li><strong>Refresh:</strong> Recalculate surface with current settings</li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Trading Applications</h6>
                    <ul>
                        <li><strong>Exposure Pinning:</strong> Look for high exposure concentrations that may "pin" price to specific levels</li>
                        <li><strong>Time Decay Effects:</strong> See how exposure evolves as expiration approaches</li>
                        <li><strong>Market Maker Hedging:</strong> Identify where dealers face maximum hedging pressure</li>
                        <li><strong>Support/Resistance:</strong> High positive exposure areas often act as support, negative exposure as resistance</li>
                        <li><strong>Volatility Expansion:</strong> Areas with low exposure may see increased volatility</li>
                        <li><strong>0DTE Strategy:</strong> Particularly useful for same-day expiration trading strategies</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
                    <ul>
                        <li>This surface is calculated using Black-Scholes exposure with current implied volatilities</li>
                        <li>Real market conditions may differ from theoretical calculations</li>
                        <li>Exposure effects are most pronounced near expiration (0DTE and 1DTE options)</li>
                        <li>Large exposure imbalances can create significant price movement when unwound</li>
                        <li>Always combine with other analysis tools and proper risk management</li>
                        <li>Market maker hedging behavior may vary based on inventory and risk limits</li>
                    </ul>
                `
            },
            'premium': {
                title: 'Top Player Positioning Help',
                content: `
                    <h6><i class="fas fa-users me-2"></i>What This Chart Shows</h6>
                    <p>The Top Player Positioning chart reveals where institutional traders and large market participants have concentrated their premium exposure across different strike prices. This analysis shows net premium flow by strike, helping identify where "smart money" is positioned and where significant support or resistance levels may emerge based on large player positioning.</p>

                    <h6><i class="fas fa-cogs me-2"></i>Settings Guide</h6>
                    <ul>
                        <li><strong>View Mode:</strong>
                            <ul>
                                <li><em>Top 10 Strikes:</em> Shows only the 10 strikes with highest net premium concentration. Best for identifying key institutional levels.</li>
                                <li><em>All Strikes:</em> Displays complete premium distribution across all available strikes. Use for comprehensive market structure analysis.</li>
                            </ul>
                        </li>
                        <li><strong>Show Net Premium:</strong> Toggles display of net premium values (calls minus puts). When enabled, shows the directional bias of large players at each strike level.</li>
                    </ul>

                    <h6><i class="fas fa-lightbulb me-2"></i>Trading Applications</h6>
                    <ul>
                        <li><strong>Institutional Level Identification:</strong>
                            <ul>
                                <li><em>High Positive Premium:</em> Strikes where institutions are net long calls or short puts. Often act as resistance levels that price gravitates toward.</li>
                                <li><em>High Negative Premium:</em> Strikes where institutions are net long puts or short calls. Often act as support levels with strong buying interest.</li>
                                <li><em>Balanced Premium:</em> Strikes with minimal net premium may indicate areas of price neutrality or potential breakout zones.</li>
                            </ul>
                        </li>
                        <li><strong>Support and Resistance Prediction:</strong>
                            <ul>
                                <li><em>Premium Concentration:</em> Strikes with highest absolute premium values often become key technical levels.</li>
                                <li><em>Premium Gaps:</em> Areas with low premium concentration may offer less support/resistance, making them good breakout targets.</li>
                                <li><em>Premium Clusters:</em> Multiple adjacent strikes with high premium create strong zones of support or resistance.</li>
                            </ul>
                        </li>
                        <li><strong>Directional Bias Analysis:</strong>
                            <ul>
                                <li><em>Net Call Premium Dominance:</em> When most strikes show positive net premium, institutions may be positioned for upward moves.</li>
                                <li><em>Net Put Premium Dominance:</em> When most strikes show negative net premium, institutions may be hedging for downward moves.</li>
                                <li><em>Mixed Premium Signals:</em> Balanced call/put premium across strikes often indicates uncertainty or range-bound expectations.</li>
                            </ul>
                        </li>
                    </ul>

                    <h6><i class="fas fa-chart-line me-2"></i>Advanced Analysis Techniques</h6>
                    <ul>
                        <li><strong>Premium Migration Tracking:</strong> Monitor how premium concentration shifts between strikes over time to identify changing institutional sentiment.</li>
                        <li><strong>Strike Selection for Trades:</strong> Use high premium strikes as targets for covered calls, cash-secured puts, or as levels to watch for reversals.</li>
                        <li><strong>Risk Management:</strong> Avoid fighting against strikes with extremely high premium concentration - institutions have deep pockets and strong conviction.</li>
                        <li><strong>Breakout Confirmation:</strong> When price breaks through high premium strikes with volume, it often signals strong institutional repositioning.</li>
                    </ul>

                    <h6><i class="fas fa-dollar-sign me-2"></i>Premium Magnitude Interpretation</h6>
                    <ul>
                        <li><strong>Very High Premium (>$50M):</strong> Extremely significant institutional positioning. These levels often act as major support/resistance.</li>
                        <li><strong>High Premium ($10-50M):</strong> Strong institutional interest. Important levels for technical analysis and trade planning.</li>
                        <li><strong>Moderate Premium ($1-10M):</strong> Standard institutional activity. Useful for identifying secondary support/resistance levels.</li>
                        <li><strong>Low Premium (<$1M):</strong> Minimal institutional positioning. These strikes may offer less reliable technical significance.</li>
                    </ul>

                    <h6><i class="fas fa-clock me-2"></i>Time-Sensitive Considerations</h6>
                    <ul>
                        <li><strong>Expiration Approach:</strong> Premium concentration effects intensify as expiration nears due to increased hedging activity and pin risk.</li>
                        <li><strong>Earnings and Events:</strong> Premium positioning often shifts dramatically before major announcements as institutions reposition.</li>
                        <li><strong>Market Hours:</strong> Premium levels are most reliable during active trading hours when institutional flow is highest.</li>
                        <li><strong>Weekly Patterns:</strong> Premium concentration often builds throughout the week and resets after expiration cycles.</li>
                    </ul>

                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Trading Notes</h6>
                    <ul>
                        <li>Premium positioning reflects past institutional activity - combine with real-time flow data for current sentiment</li>
                        <li>Large premium concentrations can create self-fulfilling prophecies as other traders react to these levels</li>
                        <li>Always confirm premium-based analysis with price action and volume before making trading decisions</li>
                        <li>Be aware that institutional positioning can change rapidly during volatile market conditions</li>
                        <li>Use premium analysis as one component of a comprehensive trading strategy, not as a standalone signal</li>
                    </ul>
                `
            }

        };

        return helpData[chartType] || {
            title: 'Chart Help',
            content: '<p>Help content not available for this chart type.</p>'
        };
    }

    async updateRiskFreeRate() {
        try {
            console.log('🏦 Fetching current risk-free rate...');
            const response = await fetch('/api/risk-free-rate');
            const data = await response.json();

            if (data.success) {
                const rateDisplay = document.getElementById('risk-free-rate-display');
                if (rateDisplay) {
                    rateDisplay.textContent = `${data.rate_percentage}%`;
                    rateDisplay.title = `10-Year Treasury: ${data.rate_percentage}% (Updated: ${new Date(data.last_updated).toLocaleString()})`;
                }
                console.log(`✅ Risk-free rate updated: ${data.rate_percentage}% (${data.source})`);
            } else {
                console.error('❌ Failed to fetch risk-free rate:', data.error);
                const rateDisplay = document.getElementById('risk-free-rate-display');
                if (rateDisplay) {
                    rateDisplay.textContent = 'Error';
                    rateDisplay.title = 'Failed to fetch current risk-free rate';
                }
            }
        } catch (error) {
            console.error('❌ Error fetching risk-free rate:', error);
            const rateDisplay = document.getElementById('risk-free-rate-display');
            if (rateDisplay) {
                rateDisplay.textContent = 'Error';
                rateDisplay.title = 'Failed to fetch current risk-free rate';
            }
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing Greek Terminal...');
    try {
        window.dashboard = new GreekTerminal();

        // Fetch and display current risk-free rate
        window.dashboard.updateRiskFreeRate();

        console.log('Greek Terminal initialized successfully');
    } catch (error) {
        console.error('Error initializing Greek Terminal:', error);
    }
});
