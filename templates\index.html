<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Greek Terminal - Web Edition</title>
    <!-- Cache buster: v5.1 -->
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">



    <!-- TradingView Lightweight Charts for candlestick chart -->
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>

    <!-- Socket.IO for real-time updates (conditionally loaded) -->
    <script>
        // Check if SocketIO is available before loading the library
        fetch('/api/socketio-status')
            .then(response => response.json())
            .then(data => {
                if (data.socketio_available) {
                    console.log('🔌 SocketIO available - loading Socket.IO library');
                    const script = document.createElement('script');
                    script.src = 'https://cdn.socket.io/4.7.2/socket.io.min.js';
                    script.onload = () => {
                        console.log('✅ Socket.IO library loaded successfully');
                    };
                    script.onerror = () => {
                        console.warn('❌ Failed to load Socket.IO library');
                    };
                    document.head.appendChild(script);
                } else {
                    console.log('🔌 SocketIO not available - skipping Socket.IO library load');
                }
            })
            .catch(error => {
                console.warn('Failed to check SocketIO status:', error);
            });
    </script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}?v=3">
</head>
<body class="custom-dark-theme">

    <!-- Trial system removed - using license system only -->

    <!-- Header -->
    <nav class="navbar custom-navbar border-bottom border-secondary">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <!-- Left side: Brand -->
            <div class="navbar-brand d-flex align-items-center">
                <i class="fas fa-chart-line me-2 text-primary"></i>
                <span class="fw-bold text-white">Greek Terminal</span>
            </div>

            <!-- Center: Logout and Disconnect buttons -->
            <div class="d-flex justify-content-center gap-2">
                <button class="btn btn-outline-warning btn-sm" id="logout-btn" title="Logout to login page">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    Logout
                </button>
                <button class="btn btn-outline-danger btn-sm" id="disconnect-data-btn" title="Disconnect and stop server">
                    <i class="fas fa-power-off me-1"></i>
                    Disconnect
                </button>
            </div>

            <!-- Right side: Status text and Admin link -->
            <div class="d-flex align-items-center">
                <span class="navbar-text me-3">
                    <i class="fas fa-chart-bar me-1"></i>
                    Real-time Options Analysis
                </span>
                {% if has_admin_access %}
                <a href="/admin" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-shield-alt me-1"></i>
                    Admin
                </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid mt-4">
        <!-- Enhanced Controls Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card fade-in-up">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Trading Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-end g-3">
                            <!-- Ticker Input -->
                            <div class="col-md-2">
                                <label for="ticker-input" class="form-label">Ticker Symbol</label>
                                <input type="text" class="form-control" id="ticker-input" value="SPY" placeholder="SPY">
                            </div>

                            <!-- Expiry Date -->
                            <div class="col-md-2">
                                <label for="expiry-select" class="form-label">Expiration Date</label>
                                <select class="form-select" id="expiry-select">
                                    <option value="">Loading...</option>
                                </select>
                            </div>

                            <!-- Pricing Model Selection -->
                            <div class="col-md-2">
                                <label for="pricing-model-select" class="form-label">
                                    Pricing Model
                                    <span class="badge bg-primary ms-1" id="active-model-badge">Black-Scholes</span>
                                </label>
                                <select class="form-select" id="pricing-model-select">
                                    <option value="black_scholes" selected>Black-Scholes</option>
                                    <option value="sabr">SABR Model</option>
                                    <option value="dupire">Dupire Local Vol</option>
                                    <option value="heston">Heston Stochastic Vol</option>
                                    <option value="dupire_heston">Dupire-Heston Hybrid</option>
                                </select>
                            </div>

                            <!-- Model Test Button -->
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-info w-100" id="test-models-btn" title="Test all pricing models to see differences">
                                    <i class="fas fa-flask me-1"></i>
                                    Test Models
                                </button>
                            </div>

                            <!-- Refresh All Charts Button -->
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-light w-100" id="fetch-data-btn">
                                    <i class="fas fa-sync-alt me-1"></i>
                                    Refresh All
                                </button>
                            </div>

                            <!-- Auto Refresh All -->
                            <div class="col-md-2">
                                <label class="form-label">Auto Refresh All</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto-refresh">
                                    <label class="form-check-label" for="auto-refresh">
                                        <small>15s</small>
                                    </label>
                                </div>
                            </div>

                            <!-- Gamma Regime Indicator -->
                            <div class="col-md-4 text-center">
                                <label for="gamma-regime-text" class="form-label">Gamma Regime</label>
                                <div class="gamma-regime-indicator">
                                    <span id="gamma-regime-text" class="badge gamma-regime-badge gamma-regime-loading">
                                        <i class="fas fa-circle-notch fa-spin me-1"></i>
                                        Loading...
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- SABR Parameters Section (initially hidden) -->
                        <div class="row g-3 mt-2" id="sabr-parameters-section" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-cog me-2"></i>
                                            SABR Model Parameters
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label for="sabr-alpha" class="form-label">Alpha (Initial Vol)</label>
                                                <input type="number" class="form-control form-control-sm" id="sabr-alpha"
                                                       value="0.2" min="0.01" max="2.0" step="0.01">
                                                <small class="form-text text-muted">Initial volatility parameter</small>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="sabr-beta" class="form-label">Beta (CEV)</label>
                                                <input type="number" class="form-control form-control-sm" id="sabr-beta"
                                                       value="0.5" min="0" max="1" step="0.1">
                                                <small class="form-text text-muted">CEV parameter (0=normal, 1=lognormal)</small>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="sabr-rho" class="form-label">Rho (Correlation)</label>
                                                <input type="number" class="form-control form-control-sm" id="sabr-rho"
                                                       value="-0.3" min="-1" max="1" step="0.1">
                                                <small class="form-text text-muted">Correlation between price and vol</small>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="sabr-nu" class="form-label">Nu (Vol of Vol)</label>
                                                <input type="number" class="form-control form-control-sm" id="sabr-nu"
                                                       value="0.4" min="0" max="2" step="0.1">
                                                <small class="form-text text-muted">Volatility of volatility</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Heston Parameters Section (initially hidden) -->
                        <div class="row g-3 mt-2" id="heston-parameters-section" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-chart-line me-2"></i>
                                            Heston Stochastic Volatility Parameters
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-2">
                                                <label for="heston-kappa" class="form-label">κ (Mean Reversion)</label>
                                                <input type="number" class="form-control form-control-sm" id="heston-kappa"
                                                       value="2.0" min="0.1" max="10" step="0.1">
                                                <small class="form-text text-muted">Speed of mean reversion</small>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="heston-theta" class="form-label">θ (Long-term Var)</label>
                                                <input type="number" class="form-control form-control-sm" id="heston-theta"
                                                       value="0.04" min="0.01" max="1" step="0.01">
                                                <small class="form-text text-muted">Long-term variance level</small>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="heston-sigma" class="form-label">σ (Vol of Vol)</label>
                                                <input type="number" class="form-control form-control-sm" id="heston-sigma"
                                                       value="0.3" min="0.01" max="2" step="0.01">
                                                <small class="form-text text-muted">Volatility of volatility</small>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="heston-rho" class="form-label">ρ (Correlation)</label>
                                                <input type="number" class="form-control form-control-sm" id="heston-rho"
                                                       value="-0.7" min="-1" max="1" step="0.1">
                                                <small class="form-text text-muted">Price-vol correlation</small>
                                            </div>
                                            <div class="col-md-2">
                                                <label for="heston-v0" class="form-label">v₀ (Initial Var)</label>
                                                <input type="number" class="form-control form-control-sm" id="heston-v0"
                                                       value="0.04" min="0.001" max="1" step="0.001">
                                                <small class="form-text text-muted">Initial variance level</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dupire Parameters Section (initially hidden) -->
                        <div class="row g-3 mt-2" id="dupire-parameters-section" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-layer-group me-2"></i>
                                            Dupire Local Volatility Parameters
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <label for="dupire-spot" class="form-label">Spot Price</label>
                                                <input type="number" class="form-control form-control-sm" id="dupire-spot"
                                                       value="100" min="1" max="10000" step="1">
                                                <small class="form-text text-muted">Current underlying price</small>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="dupire-rate" class="form-label">Risk-Free Rate</label>
                                                <input type="number" class="form-control form-control-sm" id="dupire-rate"
                                                       value="0.05" min="0" max="0.2" step="0.001">
                                                <small class="form-text text-muted">Risk-free interest rate</small>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="dupire-dividend" class="form-label">Dividend Yield</label>
                                                <input type="number" class="form-control form-control-sm" id="dupire-dividend"
                                                       value="0.0" min="0" max="0.1" step="0.001">
                                                <small class="form-text text-muted">Continuous dividend yield</small>
                                            </div>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-12">
                                                <div class="alert alert-info alert-sm">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <strong>Note:</strong> Dupire model uses local volatility surface calibrated from market data.
                                                    The model will use a simplified surface if market data is not available.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dupire-Heston Hybrid Parameters Section (initially hidden) -->
                        <div class="row g-3 mt-2" id="dupire-heston-parameters-section" style="display: none;">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            Dupire-Heston Hybrid Model Parameters
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="hybrid-dupire-weight" class="form-label">Dupire Weight</label>
                                                <input type="range" class="form-range" id="hybrid-dupire-weight"
                                                       min="0" max="1" step="0.1" value="0.5">
                                                <div class="d-flex justify-content-between">
                                                    <small class="text-muted">0% (Pure Heston)</small>
                                                    <small class="text-muted" id="dupire-weight-value">50%</small>
                                                    <small class="text-muted">100% (Pure Dupire)</small>
                                                </div>
                                                <small class="form-text text-muted">Weight between Dupire and Heston components</small>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="hybrid-correlation-adj" class="form-label">Correlation Adjustment</label>
                                                <input type="number" class="form-control form-control-sm" id="hybrid-correlation-adj"
                                                       value="0.1" min="0" max="0.5" step="0.01">
                                                <small class="form-text text-muted">Model correlation adjustment factor</small>
                                            </div>
                                        </div>
                                        <div class="row g-3 mt-2">
                                            <div class="col-12">
                                                <div class="alert alert-warning alert-sm">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <strong>Hybrid Model:</strong> This model combines Dupire local volatility with Heston stochastic volatility.
                                                    Adjust the weight to control the blend between deterministic (Dupire) and stochastic (Heston) components.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Chart Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card slide-in-right">
                    <div class="card-header">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-area me-2"></i>
                            Analytics Dashboard
                        </h5>
                        <ul class="nav nav-tabs card-header-tabs" id="chart-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="candlestick-tab" data-bs-toggle="tab" data-bs-target="#candlestick-pane" data-group="candlestick" type="button" role="tab">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Candlestick Chart
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="gex-tab" data-bs-toggle="tab" data-bs-target="#gex-pane" data-group="gamma" type="button" role="tab">
                                    <i class="fas fa-chart-bar me-1"></i>
                                    Gamma (GEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="vgex-tab" data-bs-toggle="tab" data-bs-target="#vgex-pane" data-group="gamma" type="button" role="tab">
                                    <i class="fas fa-chart-area me-1"></i>
                                    Vol Gamma (VGEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="dex-tab" data-bs-toggle="tab" data-bs-target="#dex-pane" data-group="basic" type="button" role="tab">
                                    <i class="fas fa-chart-line me-1"></i>
                                    Delta (DEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="dgex-tab" data-bs-toggle="tab" data-bs-target="#dgex-pane" data-group="gamma" type="button" role="tab">
                                    <i class="fas fa-chart-scatter me-1"></i>
                                    Delta-Gamma (D_GEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tex-tab" data-bs-toggle="tab" data-bs-target="#tex-pane" data-group="basic" type="button" role="tab">
                                    <i class="fas fa-clock me-1"></i>
                                    Theta (TEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="vegx-tab" data-bs-toggle="tab" data-bs-target="#vegx-pane" data-group="basic" type="button" role="tab">
                                    <i class="fas fa-wave-square me-1"></i>
                                    Vega (VEGX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="vex-tab" data-bs-toggle="tab" data-bs-target="#vex-pane" data-group="advanced" type="button" role="tab">
                                    <i class="fas fa-project-diagram me-1"></i>
                                    Vanna (VEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="cex-tab" data-bs-toggle="tab" data-bs-target="#cex-pane" data-group="advanced" type="button" role="tab">
                                    <i class="fas fa-magic me-1"></i>
                                    Charm (CEX)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="oi-tab" data-bs-toggle="tab" data-bs-target="#oi-pane" data-group="oi" type="button" role="tab">
                                    <i class="fas fa-layer-group me-1"></i>
                                    Open Interest
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="exposure-surface-tab" data-bs-toggle="tab" data-bs-target="#exposure-surface-pane" data-group="exposure-surface" type="button" role="tab">
                                    <i class="fas fa-fire me-1"></i>
                                    Exposure Surface
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="gex-visualizer-tab" data-bs-toggle="tab" data-bs-target="#gex-visualizer-pane" data-group="visualizer" type="button" role="tab">
                                    <i class="fas fa-chart-line me-1"></i>
                                    GEX Visualizer
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="volatility-tab" data-bs-toggle="tab" data-bs-target="#volatility-pane" data-group="volatility" type="button" role="tab">
                                    <i class="fas fa-mountain me-1"></i>
                                    Volatility Surface & Skew
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="greeks-surface-tab" data-bs-toggle="tab" data-bs-target="#greeks-surface-pane" data-group="greeks-surface" type="button" role="tab">
                                    <i class="fas fa-cube me-1"></i>
                                    Greeks Surface 3D
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="model-performance-tab" data-bs-toggle="tab" data-bs-target="#model-performance-pane" data-group="model-performance" type="button" role="tab">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    Model Performance
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="top-player-positioning-tab" data-bs-toggle="tab" data-bs-target="#top-player-positioning-pane" data-group="positioning" type="button" role="tab">
                                    <i class="fas fa-users me-1"></i>
                                    Top Player Positioning
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="signals-tab" data-bs-toggle="tab" data-bs-target="#signals-pane" data-group="signals" type="button" role="tab">
                                    <i class="fas fa-bullseye me-1"></i>
                                    Entry Signals
                                </button>
                            </li>


                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="chart-tab-content">
                            <!-- Candlestick Chart Tab -->
                            <div class="tab-pane fade show active" id="candlestick-pane" role="tabpanel">
                                <!-- Chart Controls -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="chart-settings-panel">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0">Chart Settings</h6>
                                                <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="candlestick" title="Chart Help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </div>
                                            <div class="row g-3 align-items-center">
                                                    <div class="col-md-3">
                                                        <label for="chart-period" class="form-label">Time Period</label>
                                                        <select class="form-select form-select-sm" id="chart-period">
                                                            <option value="1d">1 Day</option>
                                                            <option value="5d" selected>5 Days</option>
                                                            <option value="1mo">1 Month</option>
                                                            <option value="3mo">3 Months</option>
                                                            <option value="6mo">6 Months</option>
                                                            <option value="1y">1 Year</option>
                                                            <option value="2y">2 Years</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label for="chart-interval" class="form-label">Interval</label>
                                                        <select class="form-select form-select-sm" id="chart-interval">
                                                            <option value="1m" selected>1 Minute</option>
                                                            <option value="2m">2 Minutes</option>
                                                            <option value="5m">5 Minutes</option>
                                                            <option value="15m">15 Minutes</option>
                                                            <option value="30m">30 Minutes</option>
                                                            <option value="1h">1 Hour</option>
                                                            <option value="1d">1 Day</option>
                                                            <option value="5d">5 Days</option>
                                                            <option value="1wk">1 Week</option>
                                                            <option value="1mo">1 Month</option>
                                                        </select>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <label class="form-label">Quick Timeframes</label>
                                                        <div class="btn-group w-100" role="group">
                                                            <button type="button" class="btn btn-outline-light btn-sm" data-period="1d" data-interval="5m">Intraday</button>
                                                            <button type="button" class="btn btn-outline-light btn-sm" data-period="5d" data-interval="1m">5D/1M</button>
                                                            <button type="button" class="btn btn-outline-light btn-sm" data-period="1mo" data-interval="1d">Monthly</button>
                                                            <button type="button" class="btn btn-outline-light btn-sm" data-period="1y" data-interval="1wk">Yearly</button>
                                                        </div>
                                                    </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gamma Zone Controls -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="chart-settings-panel">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0">Gamma Zone Settings</h6>
                                                <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="gamma-zones" title="Chart Help">
                                                    <i class="fas fa-question-circle"></i>
                                                </button>
                                            </div>
                                            <div class="row g-3 align-items-center">
                                                <div class="col-md-3">
                                                    <label class="form-label">Display Type</label>
                                                    <div class="form-control form-control-sm bg-dark text-light border-secondary" style="height: auto; padding: 0.25rem 0.5rem;">
                                                        Gamma Zones
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="show-gamma-zones" checked>
                                                        <label class="form-check-label" for="show-gamma-zones">Show Gamma Zones</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="show-zero-gex" checked>
                                                        <label class="form-check-label" for="show-zero-gex">Show Zero GEX Line</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <button class="btn btn-outline-light btn-sm w-100" id="export-tradingview-btn">
                                                        <i class="fas fa-download me-1"></i>
                                                        Export to TradingView
                                                    </button>
                                                </div>
                                                <div class="col-md-2">
                                                    <small class="text-muted d-flex align-items-center">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Requires expiry selection
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chart Display -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="candlestick-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="candlestick-chart" style="height: 1000px; background-color: #1e1e1e; border-radius: 8px;"></div>
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <!-- Gamma Exposure Tab -->
                            <div class="tab-pane fade" id="gex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Gamma Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="gex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <!-- First Row: Basic Chart Settings -->
                                    <div class="row g-2 mb-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Mode</label>
                                            <select class="form-select form-select-sm" id="gex-chart-mode">
                                                <option value="single">Single Model</option>
                                                <option value="comparison">Model Comparison</option>
                                                <option value="overlay">Model Overlay</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="gex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="gex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="gex-show-calls">
                                                <label class="form-check-label" for="gex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="gex-show-puts">
                                                <label class="form-check-label" for="gex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="gex-show-net" checked>
                                                <label class="form-check-label" for="gex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Second Row: Model Comparison Settings (initially hidden) -->
                                    <div class="row g-2" id="gex-model-comparison-settings" style="display: none;">
                                        <div class="col-md-12">
                                            <div class="card bg-dark border-secondary">
                                                <div class="card-header py-2">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-layer-group me-2"></i>
                                                        Model Comparison Settings
                                                    </h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="gex-show-black-scholes" checked>
                                                                <label class="form-check-label" for="gex-show-black-scholes">
                                                                    <span class="badge bg-primary">Black-Scholes</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="gex-show-sabr" checked>
                                                                <label class="form-check-label" for="gex-show-sabr">
                                                                    <span class="badge bg-success">SABR</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="gex-show-dupire">
                                                                <label class="form-check-label" for="gex-show-dupire">
                                                                    <span class="badge bg-info">Dupire</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="gex-show-heston">
                                                                <label class="form-check-label" for="gex-show-heston">
                                                                    <span class="badge bg-warning">Heston</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="gex-show-hybrid">
                                                                <label class="form-check-label" for="gex-show-hybrid">
                                                                    <span class="badge bg-secondary">Hybrid</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button class="btn btn-outline-light btn-sm w-100" id="gex-refresh-comparison">
                                                                <i class="fas fa-sync-alt me-1"></i>
                                                                Compare Models
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="gex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="gex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Volume Gamma Tab -->
                            <div class="tab-pane fade" id="vgex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Volume Gamma Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="vgex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="vgex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="vgex-strike-range" value="20" min="5" max="100">
                                        </div>

                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vgex-show-calls">
                                                <label class="form-check-label" for="vgex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vgex-show-puts">
                                                <label class="form-check-label" for="vgex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vgex-show-net" checked>
                                                <label class="form-check-label" for="vgex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="vgex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="vgex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Delta Exposure Tab -->
                            <div class="tab-pane fade" id="dex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Delta Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="dex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <!-- First Row: Basic Chart Settings -->
                                    <div class="row g-2 mb-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Mode</label>
                                            <select class="form-select form-select-sm" id="dex-chart-mode">
                                                <option value="standard">Standard DEX</option>
                                                <option value="volatility-surface">Vol Surface</option>
                                                <option value="model-comparison">Model Comparison</option>
                                                <option value="sensitivity">Sensitivity Analysis</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="dex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                                <option value="surface">3D Surface</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="dex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dex-show-calls">
                                                <label class="form-check-label" for="dex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dex-show-puts">
                                                <label class="form-check-label" for="dex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dex-show-net" checked>
                                                <label class="form-check-label" for="dex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Second Row: Advanced Settings (initially hidden) -->
                                    <div class="row g-2" id="dex-advanced-settings" style="display: none;">
                                        <div class="col-md-12">
                                            <div class="card bg-dark border-secondary">
                                                <div class="card-header py-2">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-cogs me-2"></i>
                                                        Advanced Delta Analysis Settings
                                                    </h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <!-- Volatility Surface Settings -->
                                                        <div class="col-md-3" id="dex-vol-surface-settings" style="display: none;">
                                                            <label class="form-label">Vol Surface Type</label>
                                                            <select class="form-select form-select-sm" id="dex-vol-surface-type">
                                                                <option value="implied">Implied Vol</option>
                                                                <option value="local">Local Vol (Dupire)</option>
                                                                <option value="stochastic">Stochastic Vol (Heston)</option>
                                                            </select>
                                                        </div>

                                                        <!-- Model Comparison Settings -->
                                                        <div class="col-md-9" id="dex-model-comparison-settings" style="display: none;">
                                                            <div class="row g-2">
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-black-scholes" checked>
                                                                        <label class="form-check-label" for="dex-show-black-scholes">
                                                                            <span class="badge bg-primary">BS</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-sabr" checked>
                                                                        <label class="form-check-label" for="dex-show-sabr">
                                                                            <span class="badge bg-success">SABR</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-dupire">
                                                                        <label class="form-check-label" for="dex-show-dupire">
                                                                            <span class="badge bg-info">Dupire</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-heston">
                                                                        <label class="form-check-label" for="dex-show-heston">
                                                                            <span class="badge bg-warning">Heston</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-hybrid">
                                                                        <label class="form-check-label" for="dex-show-hybrid">
                                                                            <span class="badge bg-secondary">Hybrid</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <button class="btn btn-outline-light btn-sm w-100" id="dex-refresh-comparison">
                                                                        <i class="fas fa-sync-alt me-1"></i>
                                                                        Compare
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Sensitivity Analysis Settings -->
                                                        <div class="col-md-12" id="dex-sensitivity-settings" style="display: none;">
                                                            <div class="row g-2">
                                                                <div class="col-md-2">
                                                                    <label class="form-label">Spot Range (%)</label>
                                                                    <input type="number" class="form-control form-control-sm" id="dex-spot-range" value="10" min="1" max="50">
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <label class="form-label">Vol Range (%)</label>
                                                                    <input type="number" class="form-control form-control-sm" id="dex-vol-range" value="5" min="1" max="20">
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <label class="form-label">Time Steps</label>
                                                                    <input type="number" class="form-control form-control-sm" id="dex-time-steps" value="10" min="5" max="30">
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm mt-4">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-greeks-surface">
                                                                        <label class="form-check-label" for="dex-show-greeks-surface">Greeks Surface</label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <div class="form-check form-check-sm mt-4">
                                                                        <input class="form-check-input" type="checkbox" id="dex-show-pnl-attribution">
                                                                        <label class="form-check-label" for="dex-show-pnl-attribution">P&L Attribution</label>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-2">
                                                                    <button class="btn btn-outline-warning btn-sm w-100 mt-4" id="dex-run-sensitivity">
                                                                        <i class="fas fa-calculator me-1"></i>
                                                                        Run Analysis
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="dex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="dex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Delta-Gamma Exposure Tab -->
                            <div class="tab-pane fade" id="dgex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Delta-Gamma Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="dgex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="dgex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="dgex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dgex-show-calls">
                                                <label class="form-check-label" for="dgex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dgex-show-puts">
                                                <label class="form-check-label" for="dgex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="dgex-show-net" checked>
                                                <label class="form-check-label" for="dgex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="dgex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="dgex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Theta Exposure Tab -->
                            <div class="tab-pane fade" id="tex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Theta Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="tex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="tex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="tex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="tex-show-calls">
                                                <label class="form-check-label" for="tex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="tex-show-puts">
                                                <label class="form-check-label" for="tex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="tex-show-net" checked>
                                                <label class="form-check-label" for="tex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="tex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="tex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Vega Exposure Tab -->
                            <div class="tab-pane fade" id="vegx-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Vega Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="vegx" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="vegx-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="vegx-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vegx-show-calls">
                                                <label class="form-check-label" for="vegx-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vegx-show-puts">
                                                <label class="form-check-label" for="vegx-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vegx-show-net" checked>
                                                <label class="form-check-label" for="vegx-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="vegx-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="vegx-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Vanna Exposure Tab -->
                            <div class="tab-pane fade" id="vex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Vanna Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="vex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="vex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="vex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vex-show-calls">
                                                <label class="form-check-label" for="vex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vex-show-puts">
                                                <label class="form-check-label" for="vex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="vex-show-net" checked>
                                                <label class="form-check-label" for="vex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="vex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="vex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Charm Exposure Tab -->
                            <div class="tab-pane fade" id="cex-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Charm Exposure Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="cex" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="cex-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="cex-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="cex-show-calls">
                                                <label class="form-check-label" for="cex-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="cex-show-puts">
                                                <label class="form-check-label" for="cex-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="cex-show-net" checked>
                                                <label class="form-check-label" for="cex-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="cex-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="cex-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Open Interest Tab -->
                            <div class="tab-pane fade" id="oi-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Open Interest Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="oi" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="oi-chart-type">
                                                <option value="bar">Bar</option>
                                                <option value="line">Line</option>
                                                <option value="scatter">Scatter</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <input type="number" class="form-control form-control-sm" id="oi-strike-range" value="20" min="5" max="100">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Calls</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="oi-show-calls">
                                                <label class="form-check-label" for="oi-show-calls">Show Calls</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Puts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="oi-show-puts">
                                                <label class="form-check-label" for="oi-show-puts">Show Puts</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Net</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="oi-show-net" checked>
                                                <label class="form-check-label" for="oi-show-net">Show Net</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="oi-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="oi-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Exposure Surface Tab -->
                            <div class="tab-pane fade" id="exposure-surface-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Exposure Surface Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="exposure-surface" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="alert alert-info py-2 mb-3">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            <strong>Configure your settings below, then click "Calculate" to generate the exposure surface heatmap.</strong>
                                            Calculation time: 30 seconds to 2 minutes depending on settings.
                                        </small>
                                    </div>
                                    <div class="alert alert-warning py-2 mb-3" style="font-size: 0.85em;">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <strong>Performance Tip:</strong> Large calculations may cause server issues. For best results, use ±10% price range, $2 price steps, and 3-hour time range.
                                    </div>
                                    <div class="row g-2">
                                        <!-- NEW EXPOSURE TYPE DROPDOWN ADDED HERE v2.0 -->
                                        <div class="col-md-2">
                                            <label class="form-label">Exposure Type</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-gex-type">
                                                <option value="normal" selected>Normal GEX (OI)</option>
                                                <option value="volume">Volume GEX</option>
                                                <option value="delta">Delta Exposure</option>
                                                <option value="vanna">Vanna Exposure</option>
                                                <option value="charm">Charm Exposure</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">View Type</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-view-type">
                                                <option value="heatmap" selected>2D Heatmap</option>
                                                <option value="surface">3D Surface</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Show Exposure</label>
                                            <div class="d-flex flex-column gap-1">
                                                <div class="form-check form-check-sm">
                                                    <input class="form-check-input" type="checkbox" id="exposure-surface-show-calls">
                                                    <label class="form-check-label small" for="exposure-surface-show-calls">
                                                        Calls
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-sm">
                                                    <input class="form-check-input" type="checkbox" id="exposure-surface-show-puts">
                                                    <label class="form-check-label small" for="exposure-surface-show-puts">
                                                        Puts
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-sm">
                                                    <input class="form-check-input" type="checkbox" id="exposure-surface-show-net" checked>
                                                    <label class="form-check-label small" for="exposure-surface-show-net">
                                                        Net
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Price Range</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-price-range">
                                                <option value="5">±5% from spot</option>
                                                <option value="10" selected>±10% from spot</option>
                                                <option value="15">±15% from spot</option>
                                                <option value="20">±20% from spot</option>
                                                <option value="25">±25% from spot</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">Time Range</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-time-range">
                                                <option value="60">1 Hour</option>
                                                <option value="180" selected>3 Hours</option>
                                                <option value="405">6.75 Hours (Full Day)</option>
                                                <option value="1440">24 Hours</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">Price Step</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-price-step">
                                                <option value="0.25">$0.25</option>
                                                <option value="0.5">$0.50</option>
                                                <option value="1">$1.00</option>
                                                <option value="2" selected>$2.00</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <label class="form-label">Time Step</label>
                                            <select class="form-select form-select-sm" id="exposure-surface-time-step">
                                                <option value="1">1 Minute</option>
                                                <option value="3" selected>3 Minutes</option>
                                                <option value="5">5 Minutes</option>
                                                <option value="15">15 Minutes</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <button class="btn btn-primary btn-sm w-100" id="exposure-surface-refresh-btn">
                                                <i class="fas fa-play me-1"></i>
                                                Calculate
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Status</label>
                                            <div class="text-muted small" id="exposure-surface-status">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Ready
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Session Price Data Info -->
                                    <div class="row g-2 mt-2 pt-2 border-top">
                                        <div class="col-md-4">
                                            <label class="form-label">Session Price Data</label>
                                            <div class="text-muted small" id="exposure-session-status">
                                                <i class="fas fa-chart-line me-1"></i>
                                                Shows price movement from market open
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Session Data Points</label>
                                            <div class="text-muted small" id="exposure-session-count">
                                                <i class="fas fa-database me-1"></i>
                                                Updated on refresh
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Current Price</label>
                                            <div class="text-muted small" id="exposure-session-price">
                                                <i class="fas fa-dollar-sign me-1"></i>
                                                --
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exposure Surface Chart -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="exposure-surface-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="exposure-surface-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exposure Surface Statistics Panel -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="chart-settings-panel">
                                            <h6 class="mb-3">
                                                <i class="fas fa-chart-area me-2"></i>
                                                Exposure Surface Statistics
                                            </h6>
                                            <div class="row g-3" id="exposure-surface-stats">
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Max Positive GEX</div>
                                                        <div class="fw-bold text-success" id="max-positive-gex">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Max Negative GEX</div>
                                                        <div class="fw-bold text-danger" id="max-negative-gex">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Zero GEX Level</div>
                                                        <div class="fw-bold text-warning" id="zero-gex-level">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Current Price</div>
                                                        <div class="fw-bold text-info" id="exposure-surface-current-price">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Risk-Free Rate
                                                            <button class="btn btn-sm btn-outline-light ms-1" onclick="dashboard.updateRiskFreeRate()" title="Refresh risk-free rate">
                                                                <i class="fas fa-sync-alt"></i>
                                                            </button>
                                                        </div>
                                                        <div class="fw-bold text-success" id="risk-free-rate-display">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Data Points</div>
                                                        <div class="fw-bold" id="exposure-surface-data-points">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Status</div>
                                                        <div class="fw-bold text-info" id="exposure-surface-status-display">Ready</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- GEX Visualizer Tab -->
                            <div class="tab-pane fade" id="gex-visualizer-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">GEX Visualizer Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="gex-visualizer" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-3">
                                            <label class="form-label">GEX Type</label>
                                            <select class="form-select form-select-sm" id="gex-visualizer-type">
                                                <option value="Open Interest">Open Interest</option>
                                                <option value="Volume">Volume</option>
                                                <option value="Both">Both</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <button class="btn btn-outline-light btn-sm w-100" id="gex-visualizer-refresh">
                                                <i class="fas fa-sync-alt me-1"></i>
                                                Refresh
                                            </button>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <button class="btn btn-outline-light btn-sm w-100" id="gex-visualizer-clear">
                                                <i class="fas fa-trash me-1"></i>
                                                Clear History
                                            </button>
                                        </div>
                                        <div class="col-md-5">
                                            <label class="form-label">Status</label>
                                            <div class="text-muted small" id="gex-visualizer-status">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Select ticker and expiry to start tracking GEX levels
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="gex-visualizer-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="gex-visualizer-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Volatility Surface & Skew Tab -->
                            <div class="tab-pane fade" id="volatility-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Volatility Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="volatility" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <!-- First Row: Analysis Type and Model Selection -->
                                    <div class="row g-2 mb-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Analysis Type</label>
                                            <select class="form-select form-select-sm" id="volatility-analysis-type">
                                                <option value="implied_surface">Implied Vol Surface</option>
                                                <option value="local_surface">Local Vol Surface (Dupire)</option>
                                                <option value="stochastic_paths">Stochastic Vol Paths (Heston)</option>
                                                <option value="model_comparison">Model Comparison</option>
                                                <option value="term_structure">Term Structure</option>
                                                <option value="skew_analysis">Skew Analysis</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="volatility-chart-type">
                                                <option value="surface">3D Surface</option>
                                                <option value="contour">Contour Plot</option>
                                                <option value="heatmap">Heatmap</option>
                                                <option value="line">Line Chart</option>
                                                <option value="scatter">Scatter Plot</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Time Horizon</label>
                                            <select class="form-select form-select-sm" id="volatility-time-horizon">
                                                <option value="current">Current Expiry</option>
                                                <option value="multi_expiry">Multi-Expiry</option>
                                                <option value="term_structure">Full Term Structure</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Strike Range</label>
                                            <select class="form-select form-select-sm" id="volatility-strike-range">
                                                <option value="otm">OTM Only</option>
                                                <option value="all" selected>All Strikes</option>
                                                <option value="liquid">Liquid Only</option>
                                                <option value="custom">Custom Range</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Smoothing</label>
                                            <select class="form-select form-select-sm" id="volatility-smoothing">
                                                <option value="none">None</option>
                                                <option value="spline" selected>Spline</option>
                                                <option value="kernel">Kernel</option>
                                                <option value="polynomial">Polynomial</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <button class="btn btn-outline-success btn-sm w-100" id="volatility-analyze-btn">
                                                <i class="fas fa-chart-area me-1"></i>
                                                Analyze
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Second Row: Model-Specific Settings -->
                                    <div class="row g-2" id="volatility-model-settings">
                                        <div class="col-md-12">
                                            <div class="card bg-dark border-secondary">
                                                <div class="card-header py-2">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-cogs me-2"></i>
                                                        Advanced Volatility Analysis Settings
                                                    </h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <!-- Dupire Local Vol Settings -->
                                                        <div class="col-md-3" id="dupire-vol-settings" style="display: none;">
                                                            <h6 class="text-info mb-2">Dupire Local Vol</h6>
                                                            <div class="mb-2">
                                                                <label class="form-label form-label-sm">Calibration Method</label>
                                                                <select class="form-select form-select-sm" id="dupire-calibration-method">
                                                                    <option value="finite_difference">Finite Difference</option>
                                                                    <option value="spline_interpolation">Spline Interpolation</option>
                                                                    <option value="neural_network">Neural Network</option>
                                                                </select>
                                                            </div>
                                                            <div class="mb-2">
                                                                <label class="form-label form-label-sm">Grid Resolution</label>
                                                                <input type="number" class="form-control form-control-sm" id="dupire-grid-resolution" value="50" min="20" max="100">
                                                            </div>
                                                        </div>

                                                        <!-- Heston Stochastic Vol Settings -->
                                                        <div class="col-md-3" id="heston-vol-settings" style="display: none;">
                                                            <h6 class="text-warning mb-2">Heston Stochastic Vol</h6>
                                                            <div class="mb-2">
                                                                <label class="form-label form-label-sm">Simulation Paths</label>
                                                                <input type="number" class="form-control form-control-sm" id="heston-simulation-paths" value="10000" min="1000" max="100000" step="1000">
                                                            </div>
                                                            <div class="mb-2">
                                                                <label class="form-label form-label-sm">Time Steps</label>
                                                                <input type="number" class="form-control form-control-sm" id="heston-time-steps" value="252" min="50" max="1000">
                                                            </div>
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="heston-show-confidence-bands" checked>
                                                                <label class="form-check-label" for="heston-show-confidence-bands">Show Confidence Bands</label>
                                                            </div>
                                                        </div>

                                                        <!-- Model Comparison Settings -->
                                                        <div class="col-md-3" id="model-comparison-vol-settings" style="display: none;">
                                                            <h6 class="text-success mb-2">Model Comparison</h6>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="vol-compare-black-scholes" checked>
                                                                <label class="form-check-label" for="vol-compare-black-scholes">Black-Scholes</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="vol-compare-sabr" checked>
                                                                <label class="form-check-label" for="vol-compare-sabr">SABR</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="vol-compare-dupire">
                                                                <label class="form-check-label" for="vol-compare-dupire">Dupire</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="vol-compare-heston">
                                                                <label class="form-check-label" for="vol-compare-heston">Heston</label>
                                                            </div>
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="vol-compare-hybrid">
                                                                <label class="form-check-label" for="vol-compare-hybrid">Hybrid</label>
                                                            </div>
                                                        </div>

                                                        <!-- Advanced Options -->
                                                        <div class="col-md-3">
                                                            <h6 class="text-secondary mb-2">Display Options</h6>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="volatility-show-calls" checked>
                                                                <label class="form-check-label" for="volatility-show-calls">Show Calls</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="volatility-show-puts" checked>
                                                                <label class="form-check-label" for="volatility-show-puts">Show Puts</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-1">
                                                                <input class="form-check-input" type="checkbox" id="volatility-show-atm-line" checked>
                                                                <label class="form-check-label" for="volatility-show-atm-line">Show ATM Line</label>
                                                            </div>
                                                            <div class="form-check form-check-sm mb-2">
                                                                <input class="form-check-input" type="checkbox" id="volatility-show-statistics" checked>
                                                                <label class="form-check-label" for="volatility-show-statistics">Show Statistics</label>
                                                            </div>
                                                            <button class="btn btn-outline-warning btn-sm w-100" id="volatility-export-data">
                                                                <i class="fas fa-download me-1"></i>
                                                                Export Data
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced Volatility Charts Container -->
                                <div class="row" id="volatility-charts-container">
                                    <!-- Main Volatility Analysis Chart -->
                                    <div class="col-md-8">
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-area me-2"></i>
                                                    <span id="volatility-chart-title">Volatility Analysis</span>
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="chart-wrapper position-relative">
                                                    <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="volatility-main-chart" title="Fullscreen">
                                                        <i class="fas fa-expand"></i>
                                                    </button>
                                                    <div id="volatility-main-chart" style="height: 600px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Volatility Metrics and Secondary Charts -->
                                    <div class="col-md-4">
                                        <!-- Real-Time Volatility Metrics -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-tachometer-alt me-2"></i>
                                                    Live Volatility Metrics
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <div class="text-muted small">ATM IV</div>
                                                            <div class="fw-bold text-primary" id="live-atm-iv">--</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <div class="text-muted small">IV Rank</div>
                                                            <div class="fw-bold text-success" id="live-iv-rank">--</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <div class="text-muted small">Skew</div>
                                                            <div class="fw-bold text-warning" id="live-iv-skew">--</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-center">
                                                            <div class="text-muted small">Term Structure</div>
                                                            <div class="fw-bold text-info" id="live-term-structure">--</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Model-Specific Volatility Chart -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-layer-group me-2"></i>
                                                    Model Volatility Comparison
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="volatility-model-comparison-chart" style="height: 250px;"></div>
                                            </div>
                                        </div>

                                        <!-- Volatility Statistics -->
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-bar me-2"></i>
                                                    Statistical Analysis
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-2 text-center">
                                                    <div class="col-6">
                                                        <div class="text-muted small">Vol of Vol</div>
                                                        <div class="fw-bold" id="vol-of-vol">--</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-muted small">Mean Reversion</div>
                                                        <div class="fw-bold" id="mean-reversion">--</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-muted small">Correlation</div>
                                                        <div class="fw-bold" id="vol-correlation">--</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-muted small">Regime</div>
                                                        <div class="fw-bold" id="vol-regime">--</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Secondary Analysis Charts (for multi-chart views) -->
                                <div class="row mt-3" id="volatility-secondary-charts" style="display: none;">
                                    <div class="col-md-6">
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-line me-2"></i>
                                                    Volatility Term Structure
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="volatility-term-structure-chart" style="height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-scatter me-2"></i>
                                                    Volatility Smile Analysis
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="volatility-smile-chart" style="height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Volatility Statistics Panel -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="chart-settings-panel">
                                            <h6 class="mb-3">
                                                <i class="fas fa-chart-bar me-2"></i>
                                                Volatility Statistics
                                            </h6>
                                            <div class="row g-3" id="volatility-stats">
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">ATM IV</div>
                                                        <div class="fw-bold" id="atm-iv">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">IV Skew</div>
                                                        <div class="fw-bold" id="iv-skew">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Put/Call IV Ratio</div>
                                                        <div class="fw-bold" id="pc-iv-ratio">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">IV Range</div>
                                                        <div class="fw-bold" id="iv-range">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Term Structure</div>
                                                        <div class="fw-bold" id="term-structure">--</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center">
                                                        <div class="text-muted small">Status</div>
                                                        <div class="fw-bold text-info" id="volatility-status">Ready</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Greeks Surface 3D Tab -->
                            <div class="tab-pane fade" id="greeks-surface-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Greeks Surface 3D Settings</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="greeks-surface" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>

                                    <!-- First Row: Greek Selection and Chart Mode -->
                                    <div class="row g-2 mb-2">
                                        <div class="col-md-2">
                                            <label class="form-label">Greek Type</label>
                                            <select class="form-select form-select-sm" id="greeks-surface-type">
                                                <option value="gamma">Gamma</option>
                                                <option value="delta">Delta</option>
                                                <option value="vega">Vega</option>
                                                <option value="theta">Theta</option>
                                                <option value="vanna">Vanna</option>
                                                <option value="charm">Charm</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Surface Mode</label>
                                            <select class="form-select form-select-sm" id="greeks-surface-mode">
                                                <option value="single">Single Model</option>
                                                <option value="comparison">Model Comparison</option>
                                                <option value="difference">Model Difference</option>
                                                <option value="animation">Model Animation</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Chart Type</label>
                                            <select class="form-select form-select-sm" id="greeks-surface-chart-type">
                                                <option value="surface">3D Surface</option>
                                                <option value="contour">Contour Plot</option>
                                                <option value="heatmap">Heatmap</option>
                                                <option value="wireframe">Wireframe</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Spot Range (%)</label>
                                            <input type="number" class="form-control form-control-sm" id="greeks-surface-spot-range" value="20" min="5" max="50">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Time Range (days)</label>
                                            <input type="number" class="form-control form-control-sm" id="greeks-surface-time-range" value="30" min="1" max="365">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Resolution</label>
                                            <select class="form-select form-select-sm" id="greeks-surface-resolution">
                                                <option value="low">Low (20x20)</option>
                                                <option value="medium" selected>Medium (30x30)</option>
                                                <option value="high">High (50x50)</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Second Row: Model Selection (for comparison mode) -->
                                    <div class="row g-2" id="greeks-surface-model-selection" style="display: none;">
                                        <div class="col-md-12">
                                            <div class="card bg-dark border-secondary">
                                                <div class="card-header py-2">
                                                    <h6 class="card-title mb-0">
                                                        <i class="fas fa-layer-group me-2"></i>
                                                        Model Selection for Comparison
                                                    </h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-black-scholes" checked>
                                                                <label class="form-check-label" for="greeks-surface-show-black-scholes">
                                                                    <span class="badge bg-primary">Black-Scholes</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-sabr" checked>
                                                                <label class="form-check-label" for="greeks-surface-show-sabr">
                                                                    <span class="badge bg-success">SABR</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-dupire">
                                                                <label class="form-check-label" for="greeks-surface-show-dupire">
                                                                    <span class="badge bg-info">Dupire</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-heston">
                                                                <label class="form-check-label" for="greeks-surface-show-heston">
                                                                    <span class="badge bg-warning">Heston</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <div class="form-check form-check-sm">
                                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-hybrid">
                                                                <label class="form-check-label" for="greeks-surface-show-hybrid">
                                                                    <span class="badge bg-secondary">Hybrid</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button class="btn btn-outline-light btn-sm w-100" id="greeks-surface-generate">
                                                                <i class="fas fa-cube me-1"></i>
                                                                Generate Surface
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Third Row: Advanced Options -->
                                    <div class="row g-2 mt-2">
                                        <div class="col-md-2">
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-current-price" checked>
                                                <label class="form-check-label" for="greeks-surface-show-current-price">Show Current Price</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-atm-line" checked>
                                                <label class="form-check-label" for="greeks-surface-show-atm-line">Show ATM Line</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="greeks-surface-show-zero-plane">
                                                <label class="form-check-label" for="greeks-surface-show-zero-plane">Show Zero Plane</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="greeks-surface-smooth-surface" checked>
                                                <label class="form-check-label" for="greeks-surface-smooth-surface">Smooth Surface</label>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Color Scale</label>
                                            <select class="form-select form-select-sm" id="greeks-surface-colorscale">
                                                <option value="RdYlBu">Red-Yellow-Blue</option>
                                                <option value="Viridis">Viridis</option>
                                                <option value="Plasma">Plasma</option>
                                                <option value="Jet">Jet</option>
                                                <option value="Hot">Hot</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <button class="btn btn-outline-warning btn-sm w-100" id="greeks-surface-export">
                                                <i class="fas fa-download me-1"></i>
                                                Export Data
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chart Container -->
                                <div class="chart-container">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="card bg-dark border-secondary">
                                                <div class="card-body">
                                                    <div id="greeks-surface-chart" style="height: 600px;"></div>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <small class="text-muted">
                                                                <i class="fas fa-info-circle me-1"></i>
                                                                Interactive 3D surface showing Greeks across spot price and time to expiry
                                                            </small>
                                                        </div>
                                                        <div class="col-md-6 text-end">
                                                            <div class="fw-bold text-info" id="greeks-surface-status">Ready</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- Model Performance Dashboard Tab -->
                            <div class="tab-pane fade" id="model-performance-pane" role="tabpanel">
                                <!-- Dashboard Header -->
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">
                                                <i class="fas fa-tachometer-alt me-2"></i>
                                                Real-Time Model Performance Dashboard
                                            </h5>
                                            <div class="d-flex gap-2">
                                                <button class="btn btn-outline-success btn-sm" id="model-performance-start-monitoring">
                                                    <i class="fas fa-play me-1"></i>
                                                    Start Monitoring
                                                </button>
                                                <button class="btn btn-outline-warning btn-sm" id="model-performance-stop-monitoring">
                                                    <i class="fas fa-pause me-1"></i>
                                                    Stop Monitoring
                                                </button>
                                                <button class="btn btn-outline-info btn-sm" id="model-performance-export-report">
                                                    <i class="fas fa-file-export me-1"></i>
                                                    Export Report
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Performance Metrics Cards -->
                                <div class="row mb-3">
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-primary">
                                            <div class="card-body text-center py-2">
                                                <div class="text-primary">
                                                    <i class="fas fa-clock fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Update Frequency</h6>
                                                <h4 class="text-primary mb-0" id="performance-update-frequency">5s</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-success">
                                            <div class="card-body text-center py-2">
                                                <div class="text-success">
                                                    <i class="fas fa-chart-line fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Best Model</h6>
                                                <h6 class="text-success mb-0" id="performance-best-model">SABR</h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-warning">
                                            <div class="card-body text-center py-2">
                                                <div class="text-warning">
                                                    <i class="fas fa-percentage fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Accuracy</h6>
                                                <h5 class="text-warning mb-0" id="performance-accuracy">94.2%</h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-info">
                                            <div class="card-body text-center py-2">
                                                <div class="text-info">
                                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Total P&L</h6>
                                                <h5 class="text-info mb-0" id="performance-total-pnl">+$12.5K</h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-danger">
                                            <div class="card-body text-center py-2">
                                                <div class="text-danger">
                                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Risk Level</h6>
                                                <h6 class="text-danger mb-0" id="performance-risk-level">Medium</h6>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-body text-center py-2">
                                                <div class="text-secondary">
                                                    <i class="fas fa-server fa-2x"></i>
                                                </div>
                                                <h6 class="card-title mt-2 mb-1">Status</h6>
                                                <h6 class="text-success mb-0" id="performance-status">Active</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Dashboard Content -->
                                <div class="row">
                                    <!-- Left Column: Model Comparison Charts -->
                                    <div class="col-md-8">
                                        <!-- Model Accuracy Comparison -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-bullseye me-2"></i>
                                                    Model Accuracy Comparison
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="model-accuracy-chart" style="height: 300px;"></div>
                                            </div>
                                        </div>

                                        <!-- P&L Attribution by Model -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-pie me-2"></i>
                                                    P&L Attribution by Model
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="pnl-attribution-chart" style="height: 300px;"></div>
                                            </div>
                                        </div>

                                        <!-- Real-Time Performance Metrics -->
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-chart-area me-2"></i>
                                                    Real-Time Performance Metrics
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="realtime-performance-chart" style="height: 300px;"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Column: Model Details and Controls -->
                                    <div class="col-md-4">
                                        <!-- Model Selection and Settings -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-cogs me-2"></i>
                                                    Monitoring Settings
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">Update Interval</label>
                                                    <select class="form-select form-select-sm" id="performance-update-interval">
                                                        <option value="1">1 second</option>
                                                        <option value="5" selected>5 seconds</option>
                                                        <option value="10">10 seconds</option>
                                                        <option value="30">30 seconds</option>
                                                        <option value="60">1 minute</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Models to Monitor</label>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="monitor-black-scholes" checked>
                                                        <label class="form-check-label" for="monitor-black-scholes">Black-Scholes</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="monitor-sabr" checked>
                                                        <label class="form-check-label" for="monitor-sabr">SABR</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="monitor-dupire">
                                                        <label class="form-check-label" for="monitor-dupire">Dupire</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="monitor-heston">
                                                        <label class="form-check-label" for="monitor-heston">Heston</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="monitor-hybrid">
                                                        <label class="form-check-label" for="monitor-hybrid">Hybrid</label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Performance Metrics</label>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="metric-accuracy" checked>
                                                        <label class="form-check-label" for="metric-accuracy">Accuracy</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="metric-pnl" checked>
                                                        <label class="form-check-label" for="metric-pnl">P&L Attribution</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="metric-risk" checked>
                                                        <label class="form-check-label" for="metric-risk">Risk Metrics</label>
                                                    </div>
                                                    <div class="form-check form-check-sm">
                                                        <input class="form-check-input" type="checkbox" id="metric-speed">
                                                        <label class="form-check-label" for="metric-speed">Calculation Speed</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Model Rankings -->
                                        <div class="card bg-dark border-secondary mb-3">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-trophy me-2"></i>
                                                    Model Rankings
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="model-rankings-list">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span><i class="fas fa-medal text-warning me-2"></i>1. SABR</span>
                                                        <span class="badge bg-success">94.2%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span><i class="fas fa-medal text-secondary me-2"></i>2. Black-Scholes</span>
                                                        <span class="badge bg-primary">91.8%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span><i class="fas fa-medal text-warning me-2"></i>3. Hybrid</span>
                                                        <span class="badge bg-secondary">89.5%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <span>4. Dupire</span>
                                                        <span class="badge bg-info">87.1%</span>
                                                    </div>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span>5. Heston</span>
                                                        <span class="badge bg-warning">82.3%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Live Alerts -->
                                        <div class="card bg-dark border-secondary">
                                            <div class="card-header">
                                                <h6 class="card-title mb-0">
                                                    <i class="fas fa-bell me-2"></i>
                                                    Live Alerts
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="live-alerts-list" style="max-height: 200px; overflow-y: auto;">
                                                    <div class="alert alert-warning alert-sm mb-2">
                                                        <small><strong>12:34:56</strong> - Heston model showing high volatility</small>
                                                    </div>
                                                    <div class="alert alert-info alert-sm mb-2">
                                                        <small><strong>12:33:21</strong> - SABR model outperforming by 2.3%</small>
                                                    </div>
                                                    <div class="alert alert-success alert-sm mb-0">
                                                        <small><strong>12:32:45</strong> - All models within normal parameters</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Top Player Positioning Tab -->
                            <div class="tab-pane fade" id="top-player-positioning-pane" role="tabpanel">
                                <!-- Chart Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Top 10 Net Premium Strikes</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="premium" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <label class="form-label">View Mode</label>
                                            <select class="form-select form-select-sm" id="premium-view-mode">
                                                <option value="top10">Top 10 Strikes</option>
                                                <option value="all">All Strikes</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Show Net Premium</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="premium-show-net" checked>
                                                <label class="form-check-label" for="premium-show-net">Show Net Premium</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="chart-wrapper position-relative">
                                            <button class="btn btn-sm btn-outline-light chart-fullscreen-btn" data-chart="premium-chart" title="Fullscreen">
                                                <i class="fas fa-expand"></i>
                                            </button>
                                            <div id="premium-chart" style="height: 1000px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Entry Signals Tab -->
                            <div class="tab-pane fade" id="signals-pane" role="tabpanel">
                                <!-- Signals Settings Panel -->
                                <div class="chart-settings-panel mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">Entry Signal Monitor</h6>
                                        <button class="btn btn-outline-light btn-sm chart-help-btn" data-chart="signals" title="Chart Help">
                                            <i class="fas fa-question-circle"></i>
                                        </button>
                                    </div>
                                    <div class="row g-2">
                                        <div class="col-md-3">
                                            <label class="form-label">Auto Refresh</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="signals-auto-refresh" checked>
                                                <label class="form-check-label" for="signals-auto-refresh">Auto Update (15s)</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Sound Alerts</label>
                                            <div class="form-check form-check-sm">
                                                <input class="form-check-input" type="checkbox" id="signals-sound-alerts">
                                                <label class="form-check-label" for="signals-sound-alerts">Enable Sounds</label>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Signal Sensitivity</label>
                                            <select class="form-select form-select-sm" id="signals-sensitivity">
                                                <option value="low">Low (Major signals only)</option>
                                                <option value="medium" selected>Medium (Balanced)</option>
                                                <option value="high">High (All signals)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-primary btn-sm w-100" id="refresh-signals-btn">
                                                <i class="fas fa-sync-alt me-1"></i>
                                                Refresh Signals
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Signals Subtabs -->
                                <div class="signals-subtabs-container">
                                    <ul class="nav nav-pills nav-fill mb-3" id="signals-subtabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="intraday-signals-tab" data-bs-toggle="pill" data-bs-target="#intraday-signals" type="button" role="tab">
                                                <i class="fas fa-chart-line me-1"></i>
                                                Intraday Signals
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="eod-vanna-signals-tab" data-bs-toggle="pill" data-bs-target="#eod-vanna-signals" type="button" role="tab">
                                                <i class="fas fa-moon me-1"></i>
                                                EOD Vanna Signals
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="eod-charm-signals-tab" data-bs-toggle="pill" data-bs-target="#eod-charm-signals" type="button" role="tab">
                                                <i class="fas fa-clock me-1"></i>
                                                EOD Charm Signals
                                            </button>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Signals Subtab Content -->
                                <div class="tab-content" id="signals-subtab-content">
                                    <!-- Intraday Signals Tab -->
                                    <div class="tab-pane fade show active" id="intraday-signals" role="tabpanel">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="signals-container">
                                                    <!-- Current Market Regime -->
                                                    <div class="card bg-dark border-secondary mb-3">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-chart-line me-2"></i>
                                                                Current Market Regime
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="signal-metric">
                                                                        <label>Gamma Regime:</label>
                                                                        <span id="signal-gamma-regime" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="signal-metric">
                                                                        <label>Zero GEX Level:</label>
                                                                        <span id="signal-zero-gex" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="signal-metric">
                                                                        <label>Current Price:</label>
                                                                        <span id="signal-current-price" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Intraday Entry Signals List -->
                                                    <div class="card bg-dark border-secondary">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-bullseye me-2"></i>
                                                                Active Intraday Signals
                                                                <span class="badge bg-primary ms-2" id="active-signals-count">0</span>
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div id="signals-list">
                                                                <!-- Signals will be populated here -->
                                                                <div class="text-center text-muted py-4">
                                                                    <i class="fas fa-search fa-2x mb-2"></i>
                                                                    <p>Select a ticker and expiry to monitor intraday signals</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- EOD Vanna Signals Tab -->
                                    <div class="tab-pane fade" id="eod-vanna-signals" role="tabpanel">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="signals-container">
                                                    <!-- Vanna Analysis Header -->
                                                    <div class="card bg-dark border-secondary mb-3">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-wave-square me-2"></i>
                                                                Vanna Exposure Analysis (EOD)
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Vanna Regime:</label>
                                                                        <span id="vanna-regime" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Vol Sensitivity:</label>
                                                                        <span id="vanna-vol-sensitivity" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Cross-Effect Risk:</label>
                                                                        <span id="vanna-cross-risk" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Overnight Risk:</label>
                                                                        <span id="vanna-overnight-risk" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- EOD Vanna Signals List -->
                                                    <div class="card bg-dark border-secondary">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-moon me-2"></i>
                                                                EOD Vanna Entry Signals
                                                                <span class="badge bg-warning ms-2" id="active-vanna-signals-count">0</span>
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div id="vanna-signals-list">
                                                                <!-- Vanna signals will be populated here -->
                                                                <div class="text-center text-muted py-4">
                                                                    <i class="fas fa-wave-square fa-2x mb-2"></i>
                                                                    <p>Select a ticker and expiry to monitor EOD vanna signals</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- EOD Charm Signals Tab -->
                                    <div class="tab-pane fade" id="eod-charm-signals" role="tabpanel">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="signals-container">
                                                    <!-- Charm Analysis Header -->
                                                    <div class="card bg-dark border-secondary mb-3">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-clock me-2"></i>
                                                                Charm Exposure Analysis (EOD)
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Charm Regime:</label>
                                                                        <span id="charm-regime" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Time Decay Rate:</label>
                                                                        <span id="charm-decay-rate" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>Delta Sensitivity:</label>
                                                                        <span id="charm-delta-sensitivity" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-3">
                                                                    <div class="signal-metric">
                                                                        <label>EOD Risk Level:</label>
                                                                        <span id="charm-eod-risk" class="signal-value">Loading...</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- EOD Charm Signals List -->
                                                    <div class="card bg-dark border-secondary">
                                                        <div class="card-header">
                                                            <h6 class="mb-0">
                                                                <i class="fas fa-clock me-2"></i>
                                                                EOD Charm Entry Signals
                                                                <span class="badge bg-info ms-2" id="active-charm-signals-count">0</span>
                                                            </h6>
                                                        </div>
                                                        <div class="card-body">
                                                            <div id="charm-signals-list">
                                                                <!-- Charm signals will be populated here -->
                                                                <div class="text-center text-muted py-4">
                                                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                                                    <p>Select a ticker and expiry to monitor EOD charm signals</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>




                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h4>Loading Dashboard</h4>
            <p>Fetching market data and initializing charts...</p>
        </div>
    </div>



    <!-- TradingView Export Modal -->
    <div class="modal fade" id="tradingview-export-modal" tabindex="-1" aria-labelledby="tradingview-export-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content bg-dark">
                <div class="modal-header">
                    <h5 class="modal-title text-white" id="tradingview-export-modal-label">
                        <i class="fas fa-download me-2"></i>
                        TradingView Pine Script Export
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>TradingView Pine Script Generated!</strong><br><br>
                        <strong>Instructions:</strong><br>
                        1. Copy the Pine Script code below<br>
                        2. Go to TradingView and open the Pine Editor<br>
                        3. Paste the code and click "Add to Chart"<br>
                        4. The indicator will show gamma exposure levels with zone fills and background coloring
                    </div>
                    <div class="mb-3">
                        <label for="pine-script-code" class="form-label text-white">Pine Script Code:</label>
                        <textarea class="form-control bg-dark text-white" id="pine-script-code" rows="20" readonly style="font-family: 'Courier New', monospace; font-size: 12px;"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success" id="copy-pine-script-btn">
                        <i class="fas fa-copy me-1"></i>
                        Copy to Clipboard
                    </button>
                    <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Help Modal -->
    <div class="modal fade" id="chart-help-modal" tabindex="-1" aria-labelledby="chart-help-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content bg-dark">
                <div class="modal-header">
                    <h5 class="modal-title text-white" id="chart-help-modal-label">
                        <i class="fas fa-question-circle me-2"></i>
                        Chart Help
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-white" id="chart-help-content">
                    <!-- Help content will be dynamically loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Fullscreen Chart Modal -->
    <div id="chart-fullscreen-modal" class="chart-fullscreen-modal">
        <div class="chart-fullscreen-header">
            <h5 id="fullscreen-chart-title">Chart</h5>
            <button class="btn btn-sm btn-outline-light" id="close-fullscreen-btn" title="Exit Fullscreen (ESC)">
                <i class="fas fa-compress"></i>
            </button>
        </div>
        <div class="chart-fullscreen-content">
            <div id="fullscreen-chart-container"></div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Plotly.js for interactive charts - loaded before dashboard.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>

    <!-- Trial system removed - using license system only -->

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}?v=5.2"></script>

    <!-- Copyright Footer -->
    <footer class="copyright-footer">
        <div class="container-fluid">
            <div class="text-center text-muted">
                <small>Copyright © 2025 Greek Terminal. All rights reserved.</small>
            </div>
        </div>
    </footer>

    <style>
        .copyright-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(33, 37, 41, 0.95);
            border-top: 1px solid #495057;
            padding: 8px 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .copyright-footer small {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Add bottom padding to body to prevent content overlap */
        body {
            padding-bottom: 40px;
        }

        /* Signals Tab Styles */
        .signals-container {
            max-height: 800px;
            overflow-y: auto;
        }

        .signal-metric {
            padding: 0.5rem;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.05);
            margin-bottom: 0.5rem;
        }

        .signal-metric label {
            display: block;
            font-size: 0.8rem;
            color: #adb5bd;
            margin-bottom: 0.25rem;
        }

        .signal-value {
            font-weight: bold;
            font-size: 0.9rem;
        }

        .signal-item {
            padding: 1rem;
            margin-bottom: 0.75rem;
            border-radius: 8px;
            border-left: 4px solid;
            background-color: rgba(255, 255, 255, 0.05);
            transition: all 0.3s ease;
        }

        .signal-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .signal-item.bullish {
            border-left-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }

        .signal-item.bearish {
            border-left-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }

        .signal-item.neutral {
            border-left-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }

        .signal-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .signal-title {
            font-weight: bold;
            font-size: 1rem;
            margin: 0;
        }

        .signal-status {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .signal-status.active {
            background-color: #28a745;
            color: white;
        }

        .signal-status.triggered {
            background-color: #ffc107;
            color: #212529;
        }

        .signal-status.inactive {
            background-color: #6c757d;
            color: white;
        }

        .signal-description {
            color: #adb5bd;
            font-size: 0.85rem;
            margin-bottom: 0.5rem;
        }

        .signal-details {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .signal-detail {
            font-size: 0.8rem;
        }

        .signal-detail strong {
            color: #fff;
        }

        .signal-timestamp {
            font-size: 0.75rem;
            color: #6c757d;
            text-align: right;
            margin-top: 0.5rem;
        }

        /* Pulse animation for active signals */
        .signal-item.active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        .signal-item.bearish.active {
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-red {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }
    </style>
</body>
</html>
