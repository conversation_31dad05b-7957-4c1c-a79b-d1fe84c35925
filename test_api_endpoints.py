#!/usr/bin/env python3
"""
Test script for API endpoints related to SABR model
This script tests the API endpoints without running the full Flask app.
"""

import sys
import os
import json

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pricing_models import get_pricing_manager, set_pricing_model, set_sabr_parameters


def test_pricing_model_api_logic():
    """Test the logic that would be used in API endpoints"""
    print("=" * 60)
    print("Testing Pricing Model API Logic")
    print("=" * 60)
    
    try:
        # Test set pricing model logic
        print("Testing set pricing model logic...")
        
        # Test valid model
        model_name = 'sabr'
        if model_name not in ['black_scholes', 'sabr']:
            print(f"❌ Invalid model: {model_name}")
            return False
        
        set_pricing_model(model_name)
        print(f"✅ Set pricing model to: {model_name}")
        
        # Test invalid model
        try:
            invalid_model = 'invalid_model'
            if invalid_model not in ['black_scholes', 'sabr']:
                print(f"✅ Correctly rejected invalid model: {invalid_model}")
            else:
                print(f"❌ Should have rejected invalid model: {invalid_model}")
                return False
        except Exception as e:
            print(f"✅ Correctly handled invalid model: {e}")
        
        # Test set SABR parameters logic
        print("\nTesting set SABR parameters logic...")
        
        # Simulate API request data
        api_data = {
            'alpha': 0.25,
            'beta': 0.7,
            'rho': -0.4,
            'nu': 0.5
        }
        
        alpha = api_data.get('alpha')
        beta = api_data.get('beta')
        rho = api_data.get('rho')
        nu = api_data.get('nu')
        
        set_sabr_parameters(alpha, beta, rho, nu)
        
        pricing_manager = get_pricing_manager()
        current_params = pricing_manager.get_sabr_params()
        
        print(f"✅ Set SABR parameters: {current_params}")
        
        # Verify parameters were set correctly
        assert current_params['alpha'] == alpha, f"Alpha mismatch: {current_params['alpha']} != {alpha}"
        assert current_params['beta'] == beta, f"Beta mismatch: {current_params['beta']} != {beta}"
        assert current_params['rho'] == rho, f"Rho mismatch: {current_params['rho']} != {rho}"
        assert current_params['nu'] == nu, f"Nu mismatch: {current_params['nu']} != {nu}"
        
        print("✅ Parameters verified correctly")
        
        # Test get pricing model info logic
        print("\nTesting get pricing model info logic...")
        
        model_info = pricing_manager.get_model_info()
        
        expected_keys = ['current_model', 'available_models']
        for key in expected_keys:
            assert key in model_info, f"Missing key in model_info: {key}"
        
        if model_info['current_model'] == 'sabr':
            assert 'sabr_parameters' in model_info, "Missing sabr_parameters when model is SABR"
        
        print(f"✅ Model info structure correct: {model_info}")
        
        # Test API response format
        print("\nTesting API response format...")
        
        # Simulate successful API responses
        set_model_response = {
            'success': True,
            'model': model_name
        }
        
        set_params_response = {
            'success': True,
            'parameters': current_params
        }
        
        get_info_response = {
            'success': True,
            'model_info': model_info
        }
        
        # Verify responses can be JSON serialized
        try:
            json.dumps(set_model_response)
            json.dumps(set_params_response)
            json.dumps(get_info_response)
            print("✅ All API responses are JSON serializable")
        except Exception as e:
            print(f"❌ JSON serialization failed: {e}")
            return False
        
        print("✅ All API logic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ API logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_switching_integration():
    """Test the complete model switching workflow"""
    print("\n" + "=" * 60)
    print("Testing Model Switching Integration")
    print("=" * 60)
    
    try:
        pricing_manager = get_pricing_manager()
        
        # Test parameters for Greeks calculation
        S = 100.0
        K = 105.0
        T = 0.25
        r = 0.05
        sigma = 0.2
        
        # Test Black-Scholes mode
        print("Testing Black-Scholes mode...")
        set_pricing_model('black_scholes')
        
        bs_delta = pricing_manager.calculate_delta('c', S, K, T, r, sigma)
        bs_gamma = pricing_manager.calculate_gamma('c', S, K, T, r, sigma)
        bs_vega = pricing_manager.calculate_vega('c', S, K, T, r, sigma)
        bs_theta = pricing_manager.calculate_theta('c', S, K, T, r, sigma)
        
        print(f"   Black-Scholes Greeks: Δ={bs_delta:.4f}, Γ={bs_gamma:.6f}, ν={bs_vega:.4f}, Θ={bs_theta:.4f}")
        
        # Test SABR mode
        print("Testing SABR mode...")
        set_pricing_model('sabr')
        set_sabr_parameters(alpha=0.2, beta=0.5, rho=-0.3, nu=0.4)
        
        sabr_delta = pricing_manager.calculate_delta('c', S, K, T, r, sigma)
        sabr_gamma = pricing_manager.calculate_gamma('c', S, K, T, r, sigma)
        sabr_vega = pricing_manager.calculate_vega('c', S, K, T, r, sigma)
        sabr_theta = pricing_manager.calculate_theta('c', S, K, T, r, sigma)
        
        print(f"   SABR Greeks: Δ={sabr_delta:.4f}, Γ={sabr_gamma:.6f}, ν={sabr_vega:.4f}, Θ={sabr_theta:.4f}")
        
        # Verify that results are different (they should be with different models)
        delta_diff = abs(sabr_delta - bs_delta)
        gamma_diff = abs(sabr_gamma - bs_gamma)
        
        print(f"   Differences: Δ diff={delta_diff:.6f}, Γ diff={gamma_diff:.6f}")
        
        # The models should produce different results for most cases
        if delta_diff > 0.001 or gamma_diff > 0.001:
            print("✅ Models produce different results as expected")
        else:
            print("⚠️  Models produce very similar results (this may be normal for some parameters)")
        
        # Test switching back and forth
        print("Testing rapid model switching...")
        for i in range(3):
            set_pricing_model('black_scholes')
            bs_test = pricing_manager.calculate_delta('c', S, K, T, r, sigma)
            
            set_pricing_model('sabr')
            sabr_test = pricing_manager.calculate_delta('c', S, K, T, r, sigma)
            
            print(f"   Switch {i+1}: BS={bs_test:.4f}, SABR={sabr_test:.4f}")
        
        print("✅ Model switching integration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model switching integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_validation():
    """Test parameter validation for SABR model"""
    print("\n" + "=" * 60)
    print("Testing Parameter Validation")
    print("=" * 60)
    
    try:
        pricing_manager = get_pricing_manager()
        set_pricing_model('sabr')
        
        # Test valid parameters
        valid_params = [
            {'alpha': 0.1, 'beta': 0.0, 'rho': -1.0, 'nu': 0.0},  # Boundary values
            {'alpha': 0.5, 'beta': 1.0, 'rho': 1.0, 'nu': 1.0},   # Boundary values
            {'alpha': 0.2, 'beta': 0.5, 'rho': -0.3, 'nu': 0.4},  # Typical values
        ]
        
        for i, params in enumerate(valid_params):
            try:
                set_sabr_parameters(**params)
                current = pricing_manager.get_sabr_params()
                print(f"✅ Valid params {i+1}: {params} -> {current}")
            except Exception as e:
                print(f"❌ Valid params {i+1} rejected: {params} -> {e}")
                return False
        
        # Test that calculations work with various parameter sets
        print("Testing calculations with different parameter sets...")
        
        test_cases = [
            {'alpha': 0.1, 'beta': 0.0, 'rho': 0.0, 'nu': 0.1},    # Low vol, normal model
            {'alpha': 0.3, 'beta': 1.0, 'rho': -0.5, 'nu': 0.8},   # High vol, lognormal model
            {'alpha': 0.2, 'beta': 0.5, 'rho': 0.3, 'nu': 0.4},    # Positive correlation
        ]
        
        for i, params in enumerate(test_cases):
            set_sabr_parameters(**params)
            
            # Test basic calculation
            delta = pricing_manager.calculate_delta('c', 100, 100, 0.25, 0.05, 0.2)
            gamma = pricing_manager.calculate_gamma('c', 100, 100, 0.25, 0.05, 0.2)
            
            # Basic sanity checks
            assert 0 <= delta <= 1, f"Delta out of range: {delta}"
            assert gamma >= 0, f"Gamma should be non-negative: {gamma}"
            
            print(f"✅ Test case {i+1}: {params} -> Δ={delta:.4f}, Γ={gamma:.6f}")
        
        print("✅ All parameter validation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_api_tests():
    """Run all API-related tests"""
    print("SABR Model API Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_pricing_model_api_logic,
        test_model_switching_integration,
        test_parameter_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"API Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All API tests passed! SABR integration is working correctly.")
        return True
    else:
        print("❌ Some API tests failed. Please review the implementation.")
        return False


if __name__ == '__main__':
    success = run_all_api_tests()
    sys.exit(0 if success else 1)
