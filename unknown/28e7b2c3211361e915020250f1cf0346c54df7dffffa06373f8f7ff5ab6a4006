#!/usr/bin/env python3
"""
GitHub-based License Management System for Greek Terminal
Handles Discord-based license authentication and validation
"""

import os
import json
import hashlib
import hmac
import base64
import requests
import tempfile
import platform
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import P<PERSON><PERSON>DF2HMAC
from github import Github
import re

class LicenseConfig:
    """Configuration for license system"""
    def __init__(self):
        # GitHub configuration - try environment first, then fallback to bundled config
        self.GITHUB_TOKEN = os.environ.get('GITHUB_TOKEN', '')
        self.GITHUB_REPO = os.environ.get('GITHUB_REPO', '')  # format: "username/repo"

        # If running as executable and no env vars, use bundled config
        if not self.GITHUB_TOKEN or not self.GITHUB_REPO:
            self._load_bundled_config()

        self.LICENSE_FILE_PATH = 'licenses/license_data.enc'
        
        # License format: DEV-XXXXX-XXXX|discord_id|username|expiry_date (5 digits)
        self.LICENSE_PATTERN = r'^DEV-\d{5}-[A-F0-9]{4}\|\d+\|[a-zA-Z0-9_]+\|\d{4}/\d{2}/\d{2}$'
        
        # Security settings
        self.ENCRYPTION_ITERATIONS = 100000
        self.SALT_LENGTH = 32
        self.KEY_LENGTH = 32
        
        # Cache settings
        self.CACHE_DURATION_HOURS = 1  # Cache license data for 1 hour
        self.OFFLINE_GRACE_PERIOD_HOURS = 24  # Allow offline validation for 24 hours
        
        # Local storage for caching
        user_data_dir = os.path.expanduser('~')
        self.CACHE_LOCATIONS = [
            os.path.join(user_data_dir, '.greek_terminal', 'license_cache.enc'),
            os.path.join(tempfile.gettempdir(), '.gt_license_cache.dat'),
        ]

class LicenseManager:
    """GitHub-based license management with Discord integration"""
    
    def __init__(self):
        self.config = LicenseConfig()
        self._master_key = self._generate_master_key()
        self._github = None
        self._repo = None
        self._init_github()
    
    def _generate_master_key(self):
        """Generate encryption key for license data"""
        # Use application-specific data for key generation
        app_data = "GreekTerminalLicense2024"
        system_data = f"{platform.system()}{platform.machine()}"
        
        # Generate key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=self.config.KEY_LENGTH,
            salt=b"GreekTerminalLicenseKey",
            iterations=self.config.ENCRYPTION_ITERATIONS,
        )
        key = base64.urlsafe_b64encode(kdf.derive((app_data + system_data).encode()))
        return key
    
    def _init_github(self):
        """Initialize GitHub connection"""
        try:
            if self.config.GITHUB_TOKEN and self.config.GITHUB_REPO:
                self._github = Github(self.config.GITHUB_TOKEN)
                self._repo = self._github.get_repo(self.config.GITHUB_REPO)
                print("✅ GitHub connection initialized")
            else:
                print("⚠️ GitHub credentials not configured")
        except Exception as e:
            print(f"❌ GitHub initialization failed: {e}")
            self._github = None
            self._repo = None
    
    def _encrypt_data(self, data):
        """Encrypt license data"""
        try:
            fernet = Fernet(self._master_key)
            # Convert datetime objects to strings for JSON serialization
            json_data = json.dumps(data, sort_keys=True, default=str)
            encrypted = fernet.encrypt(json_data.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            print(f"Encryption error: {e}")
            return None
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt license data"""
        try:
            fernet = Fernet(self._master_key)
            decoded = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = fernet.decrypt(decoded)
            return json.loads(decrypted.decode())
        except Exception as e:
            print(f"Decryption error: {e}")
            return None
    
    def _parse_license_line(self, line):
        """Parse a license line into components"""
        try:
            line = line.strip()
            if not re.match(self.config.LICENSE_PATTERN, line):
                return None
            
            parts = line.split('|')
            if len(parts) != 4:
                return None
            
            license_key, discord_id, username, expiry_str = parts
            
            # Parse expiry date
            expiry_date = datetime.strptime(expiry_str, '%Y/%m/%d')
            
            return {
                'license_key': license_key,
                'discord_id': discord_id,
                'username': username,
                'expiry_date': expiry_date,
                'expiry_str': expiry_str,
                'raw_line': line
            }
        except Exception as e:
            print(f"Error parsing license line '{line}': {e}")
            return None
    
    def _load_licenses_from_github(self):
        """Load license data from GitHub repository"""
        try:
            if not self._repo:
                return None
            
            # Get the license file from GitHub
            file_content = self._repo.get_contents(self.config.LICENSE_FILE_PATH)
            encrypted_data = file_content.decoded_content.decode('utf-8')
            
            # Decrypt the data
            decrypted_data = self._decrypt_data(encrypted_data)
            if not decrypted_data:
                return None
            
            # Parse license lines
            licenses = {}
            license_lines = decrypted_data.get('licenses', [])
            
            for line in license_lines:
                license_info = self._parse_license_line(line)
                if license_info:
                    licenses[license_info['license_key']] = license_info
            
            return {
                'licenses': licenses,
                'last_updated': datetime.now().isoformat(),
                'source': 'github'
            }
            
        except Exception as e:
            print(f"Error loading licenses from GitHub: {e}")
            return None
    
    def _save_license_cache(self, license_data):
        """Save license data to local cache"""
        try:
            encrypted_data = self._encrypt_data(license_data)
            if not encrypted_data:
                return False
            
            saved_count = 0
            for location in self.config.CACHE_LOCATIONS:
                try:
                    os.makedirs(os.path.dirname(location), exist_ok=True)
                    with open(location, 'w') as f:
                        f.write(encrypted_data)
                    saved_count += 1
                except Exception:
                    continue
            
            return saved_count > 0
        except Exception as e:
            print(f"Error saving license cache: {e}")
            return False
    
    def _load_license_cache(self):
        """Load license data from local cache"""
        for location in self.config.CACHE_LOCATIONS:
            try:
                if os.path.exists(location):
                    with open(location, 'r') as f:
                        encrypted_data = f.read().strip()
                    
                    data = self._decrypt_data(encrypted_data)
                    if data:
                        # Check if cache is still valid
                        last_updated = datetime.fromisoformat(data['last_updated'])
                        cache_age = datetime.now() - last_updated
                        
                        if cache_age.total_seconds() < (self.config.CACHE_DURATION_HOURS * 3600):
                            data['source'] = 'cache'
                            return data
            except Exception:
                continue
        
        return None
    
    def get_licenses(self, force_refresh=False):
        """Get license data with caching"""
        # Try to load from cache first (unless force refresh)
        if not force_refresh:
            cached_data = self._load_license_cache()
            if cached_data:
                return cached_data
        
        # Load from GitHub
        github_data = self._load_licenses_from_github()
        if github_data:
            # Save to cache
            self._save_license_cache(github_data)
            return github_data
        
        # Fallback to cache if GitHub fails
        cached_data = self._load_license_cache()
        if cached_data:
            # Check if within offline grace period
            last_updated = datetime.fromisoformat(cached_data['last_updated'])
            offline_duration = datetime.now() - last_updated
            
            if offline_duration.total_seconds() < (self.config.OFFLINE_GRACE_PERIOD_HOURS * 3600):
                cached_data['source'] = 'cache_offline'
                return cached_data
        
        return None
    
    def validate_license(self, license_key):
        """Validate a license key"""
        try:
            # Get license data
            license_data = self.get_licenses()
            if not license_data:
                # Provide more detailed error message
                if not self.config.GITHUB_TOKEN or not self.config.GITHUB_REPO:
                    return {
                        'valid': False,
                        'error': 'License system not configured. Missing GitHub credentials.',
                        'source': 'error',
                        'details': 'Contact support for assistance with license validation.'
                    }
                else:
                    return {
                        'valid': False,
                        'error': 'Unable to load license data. Check internet connection.',
                        'source': 'error',
                        'details': 'License validation requires internet access or cached license data.'
                    }
            
            licenses = license_data['licenses']
            
            # Check if license exists
            if license_key not in licenses:
                return {
                    'valid': False,
                    'error': 'License key not found',
                    'source': license_data['source']
                }
            
            license_info = licenses[license_key]
            
            # Check if license is expired
            now = datetime.now()

            # Ensure expiry_date is a datetime object (might be string from JSON)
            expiry_date = license_info['expiry_date']
            if isinstance(expiry_date, str):
                try:
                    expiry_date = datetime.strptime(expiry_date, '%Y/%m/%d')
                except:
                    # Try ISO format if that fails
                    expiry_date = datetime.fromisoformat(expiry_date.replace('Z', '+00:00'))

            if expiry_date < now:
                return {
                    'valid': False,
                    'error': 'License has expired',
                    'expiry_date': license_info['expiry_str'],
                    'source': license_data['source']
                }
            
            # License is valid
            days_remaining = (expiry_date - now).days
            
            return {
                'valid': True,
                'license_info': license_info,
                'days_remaining': days_remaining,
                'source': license_data['source']
            }
            
        except Exception as e:
            print(f"Error validating license: {e}")
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}',
                'source': 'error'
            }

# Global license manager instance
_license_manager = None

def get_license_manager():
    """Get the global license manager instance"""
    global _license_manager
    if _license_manager is None:
        _license_manager = LicenseManager()
    return _license_manager

def validate_license_key(license_key):
    """Simple function to validate a license key"""
    manager = get_license_manager()
    return manager.validate_license(license_key)

def check_license_access(license_key):
    """Check if a license key provides access"""
    result = validate_license_key(license_key)
    return result.get('valid', False)

def invalidate_license_cache():
    """Invalidate the license cache to force refresh"""
    manager = get_license_manager()
    try:
        # Remove all cache files
        for location in manager.config.CACHE_LOCATIONS:
            if os.path.exists(location):
                os.remove(location)
                print(f"🗑️ Removed cache file: {location}")
        print(f"✅ License cache invalidated")
        return True
    except Exception as e:
        print(f"❌ Error invalidating cache: {e}")
        return False

def refresh_license_cache():
    """Force refresh the license cache"""
    invalidate_license_cache()
    manager = get_license_manager()
    return manager.get_licenses()
