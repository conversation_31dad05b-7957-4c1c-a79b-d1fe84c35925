#!/usr/bin/env python3
"""
Heston Stochastic Volatility Model Implementation
This module provides Heston model for option pricing and Greeks using characteristic functions.
"""

import numpy as np
import math
import cmath
from scipy.stats import norm
from scipy.integrate import quad
from scipy.optimize import minimize_scalar
import warnings


class HestonModel:
    """
    Heston Stochastic Volatility Model implementation.
    
    The Heston model is defined by the stochastic differential equations:
    dS_t = rS_t dt + √v_t S_t dW_1(t)
    dv_t = κ(θ - v_t)dt + σ√v_t dW_2(t)
    
    Where:
    - S_t is the asset price
    - v_t is the instantaneous variance
    - κ is the mean reversion speed
    - θ is the long-term variance
    - σ is the volatility of volatility
    - ρ is the correlation between the two Brownian motions
    """
    
    def __init__(self, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04):
        """
        Initialize Heston model with parameters.

        Args:
            kappa (float): Mean reversion speed
            theta (float): Long-term variance
            sigma (float): Volatility of volatility
            rho (float): Correlation between asset and volatility
            v0 (float): Initial variance
        """
        self.kappa = kappa
        self.theta_param = theta  # Renamed to avoid conflict with theta() method
        self.sigma = sigma
        self.rho = rho
        self.v0 = v0

        # Validate parameters
        self._validate_parameters()
    
    def _validate_parameters(self):
        """Validate Heston model parameters."""
        if self.kappa <= 0:
            raise ValueError(f"Kappa must be positive, got {self.kappa}")
        if self.theta_param <= 0:
            raise ValueError(f"Theta must be positive, got {self.theta_param}")
        if self.sigma <= 0:
            raise ValueError(f"Sigma must be positive, got {self.sigma}")
        if not (-1 <= self.rho <= 1):
            raise ValueError(f"Rho must be between -1 and 1, got {self.rho}")
        if self.v0 <= 0:
            raise ValueError(f"Initial variance must be positive, got {self.v0}")

        # Feller condition: 2*kappa*theta > sigma^2
        if 2 * self.kappa * self.theta_param <= self.sigma**2:
            warnings.warn(f"Feller condition not satisfied: 2κθ = {2*self.kappa*self.theta_param:.4f} <= σ² = {self.sigma**2:.4f}")
    
    def characteristic_function(self, u, S0, r, T, q=0.0):
        """
        Calculate the characteristic function of log(S_T) under Heston model.
        
        Args:
            u (complex): Argument of characteristic function
            S0 (float): Initial stock price
            r (float): Risk-free rate
            T (float): Time to maturity
            q (float): Dividend yield
            
        Returns:
            complex: Characteristic function value
        """
        if T <= 0:
            return complex(1.0, 0.0)
        
        # Parameters
        kappa, theta, sigma, rho, v0 = self.kappa, self.theta_param, self.sigma, self.rho, self.v0
        
        # Calculate d and g
        d = cmath.sqrt((rho * sigma * u * 1j - kappa)**2 - sigma**2 * (-u * 1j - u**2))
        g = (kappa - rho * sigma * u * 1j - d) / (kappa - rho * sigma * u * 1j + d)
        
        # Calculate C and D
        exp_dT = cmath.exp(-d * T)
        
        C = (r - q) * u * 1j * T + (kappa * theta / sigma**2) * (
            (kappa - rho * sigma * u * 1j - d) * T - 2 * cmath.log((1 - g * exp_dT) / (1 - g))
        )
        
        D = ((kappa - rho * sigma * u * 1j - d) / sigma**2) * (
            (1 - exp_dT) / (1 - g * exp_dT)
        )
        
        # Characteristic function
        cf = cmath.exp(C + D * v0 + u * 1j * math.log(S0))
        
        return cf
    
    def option_price_cf(self, S0, K, T, r, option_type='call', q=0.0):
        """
        Calculate option price using characteristic function and Fourier inversion.
        
        Args:
            S0 (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            option_type (str): 'call' or 'put'
            q (float): Dividend yield
            
        Returns:
            float: Option price
        """
        if T <= 0:
            if option_type.lower() == 'call':
                return max(S0 - K, 0)
            else:
                return max(K - S0, 0)
        
        try:
            # Use Carr-Madan formula for option pricing
            alpha = 1.25  # Damping parameter

            # Optimization 24: Pre-calculate constants outside integrand
            exp_neg_rT = math.exp(-r * T)
            alpha_plus_1 = alpha + 1
            alpha_plus_1_j = alpha_plus_1 * 1j

            def integrand(u):
                """Integrand for Fourier inversion."""
                try:
                    cf = self.characteristic_function(u - alpha_plus_1_j, S0, r, T, q)
                    numerator = exp_neg_rT * cf
                    denominator = (alpha + u * 1j) * (alpha_plus_1 + u * 1j)
                    return (numerator / denominator).real
                except:
                    return 0.0

            # Numerical integration
            integral, _ = quad(integrand, 0, 100, limit=50)
            
            call_price = S0 - K * math.exp(-r * T) * (0.5 + integral / math.pi)
            
            if option_type.lower() == 'call':
                return max(call_price, 0)
            else:
                # Put-call parity
                put_price = call_price - S0 * math.exp(-q * T) + K * math.exp(-r * T)
                return max(put_price, 0)
                
        except Exception as e:
            warnings.warn(f"Heston CF pricing failed: {e}, using approximation")
            return self._approximate_price(S0, K, T, r, option_type, q)
    
    def _approximate_price(self, S0, K, T, r, option_type, q=0.0):
        """Approximate option price using Black-Scholes with average volatility."""
        avg_vol = math.sqrt(self.v0)  # Simple approximation
        return self._black_scholes_price(S0, K, T, r, avg_vol, option_type, q)
    
    def _black_scholes_price(self, S, K, T, r, sigma, option_type, q=0.0):
        """Calculate Black-Scholes price with given volatility."""
        if T <= 0 or sigma <= 0:
            if option_type.lower() == 'call':
                return max(S - K, 0)
            else:
                return max(K - S, 0)
        
        d1 = (math.log(S / K) + (r - q + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        if option_type.lower() == 'call':
            price = S * math.exp(-q * T) * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * math.exp(-q * T) * norm.cdf(-d1)
        
        return max(price, 0)
    
    def option_price(self, S, K, T, r, option_type='call', q=0.0):
        """
        Calculate option price using Heston model.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration
            r (float): Risk-free rate
            option_type (str): 'call' or 'put'
            q (float): Dividend yield
            
        Returns:
            float: Option price
        """
        return self.option_price_cf(S, K, T, r, option_type, q)
    
    def delta(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate delta using finite differences."""
        if T <= 0:
            if option_type.lower() == 'call':
                return 1.0 if S > K else 0.0
            else:
                return -1.0 if S < K else 0.0
        
        h = 0.01 * S  # 1% bump
        price_up = self.option_price(S + h, K, T, r, option_type, q)
        price_down = self.option_price(S - h, K, T, r, option_type, q)
        
        return (price_up - price_down) / (2 * h)
    
    def gamma(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate gamma using finite differences."""
        if T <= 0 or S <= 0:
            return 0.0
        
        h = 0.01 * S  # 1% bump
        price_up = self.option_price(S + h, K, T, r, option_type, q)
        price_center = self.option_price(S, K, T, r, option_type, q)
        price_down = self.option_price(S - h, K, T, r, option_type, q)
        
        return (price_up - 2 * price_center + price_down) / (h**2)
    
    def vega(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate vega with respect to initial volatility."""
        if T <= 0 or S <= 0:
            return 0.0
        
        # Bump initial variance
        h = 0.01 * self.v0  # 1% bump in variance
        
        original_v0 = self.v0
        
        self.v0 = original_v0 + h
        price_up = self.option_price(S, K, T, r, option_type, q)
        
        self.v0 = original_v0 - h
        price_down = self.option_price(S, K, T, r, option_type, q)
        
        self.v0 = original_v0  # Restore original value
        
        # Convert from variance sensitivity to volatility sensitivity
        vol_sensitivity = (price_up - price_down) / (2 * h)
        return vol_sensitivity * 2 * math.sqrt(self.v0) / 100  # Convert to percentage point change
    
    def theta(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate theta using finite differences."""
        if T <= 0:
            return 0.0
        
        h = 1.0 / 365.0  # 1 day
        if T <= h:
            h = T / 2.0
        
        price_now = self.option_price(S, K, T, r, option_type, q)
        price_later = self.option_price(S, K, T - h, r, option_type, q)
        
        return (price_later - price_now) / h / 365  # Daily theta
    
    def vanna(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate vanna using finite differences."""
        if T <= 0 or S <= 0:
            return 0.0
        
        h_s = 0.01 * S  # 1% bump in spot
        h_v = 0.01 * self.v0  # 1% bump in variance
        
        original_v0 = self.v0
        
        # Calculate cross derivative
        self.v0 = original_v0 + h_v
        delta_up = self.delta(S, K, T, r, option_type, q)
        
        self.v0 = original_v0 - h_v
        delta_down = self.delta(S, K, T, r, option_type, q)
        
        self.v0 = original_v0  # Restore original value
        
        vanna = (delta_up - delta_down) / (2 * h_v)
        return vanna * 2 * math.sqrt(self.v0) / 100  # Convert to vol sensitivity
    
    def charm(self, S, K, T, r, option_type='call', q=0.0):
        """Calculate charm using finite differences."""
        if T <= 0 or S <= 0:
            return 0.0
        
        h = 1.0 / 365.0  # 1 day
        if T <= h:
            h = T / 2.0
        
        delta_now = self.delta(S, K, T, r, option_type, q)
        delta_later = self.delta(S, K, T - h, r, option_type, q)
        
        return (delta_later - delta_now) / h / 365  # Daily charm


# Default Heston model instance with reasonable parameters
default_heston = HestonModel(kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04)


def heston_option_price(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston option pricing."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.option_price(S, K, T, r, option_type, q)


def heston_delta(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston delta calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.delta(S, K, T, r, option_type, q)


def heston_gamma(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston gamma calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.gamma(S, K, T, r, option_type, q)


def heston_vega(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston vega calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.vega(S, K, T, r, option_type, q)


def heston_theta(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston theta calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.theta(S, K, T, r, option_type, q)


def heston_vanna(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston vanna calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.vanna(S, K, T, r, option_type, q)


def heston_charm(flag, S, K, T, r, kappa=2.0, theta=0.04, sigma=0.3, rho=-0.7, v0=0.04, q=0.0):
    """Convenience function for Heston charm calculation."""
    heston = HestonModel(kappa=kappa, theta=theta, sigma=sigma, rho=rho, v0=v0)
    option_type = 'call' if flag.lower() == 'c' else 'put'
    return heston.charm(S, K, T, r, option_type, q)
