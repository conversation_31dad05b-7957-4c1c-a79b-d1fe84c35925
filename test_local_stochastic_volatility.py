#!/usr/bin/env python3
"""
Test script for Local Stochastic Volatility 3D Surface features
Tests the new L function-based volatility modeling with log moneyness and time to maturity
"""

import requests
import json
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_TICKER = "SPY"
TEST_EXPIRY = "2024-12-20"  # Adjust as needed

def test_local_stochastic_volatility_surface():
    """Test the new local stochastic volatility 3D surface endpoint"""
    print("🔍 Testing Local Stochastic Volatility 3D Surface...")
    
    url = f"{BASE_URL}/api/local-stochastic-volatility-surface/{TEST_TICKER}/{TEST_EXPIRY}"
    
    # Test different L function types
    l_function_types = ['polynomial', 'exponential', 'spline']
    results = {}
    
    for l_func_type in l_function_types:
        print(f"   Testing L Function: {l_func_type}")
        
        params = {
            'l_function_type': l_func_type,
            'grid_resolution': 50,
            'time_grid_points': 15,
            'moneyness_grid_points': 25,
            'include_calibration': 'true'
        }
        
        try:
            response = requests.get(url, params=params, timeout=60)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    surface_data = data.get('surface_data', {})
                    calibration_metrics = surface_data.get('calibration_metrics', {})
                    
                    results[l_func_type] = {
                        'success': True,
                        'grid_points': surface_data.get('grid_info', {}).get('total_points', 0),
                        'calibration_rmse': calibration_metrics.get('rmse', 'N/A'),
                        'quality_score': calibration_metrics.get('quality_score', 'N/A'),
                        'l_function_type': surface_data.get('l_function_type', 'Unknown')
                    }
                    
                    print(f"   ✅ {l_func_type}: {results[l_func_type]['grid_points']} points, "
                          f"RMSE: {results[l_func_type]['calibration_rmse']}")
                else:
                    results[l_func_type] = {'success': False, 'error': data.get('error', 'Unknown')}
                    print(f"   ❌ {l_func_type}: {results[l_func_type]['error']}")
            else:
                results[l_func_type] = {'success': False, 'error': f'HTTP {response.status_code}'}
                print(f"   ❌ {l_func_type}: HTTP {response.status_code}")
                
        except Exception as e:
            results[l_func_type] = {'success': False, 'error': str(e)}
            print(f"   ❌ {l_func_type}: {e}")
    
    # Summary
    successful_tests = sum(1 for r in results.values() if r.get('success', False))
    print(f"✅ Local Stochastic Volatility Surface: {successful_tests}/{len(l_function_types)} L functions working")
    
    return successful_tests == len(l_function_types), results

def test_enhanced_skew_analysis():
    """Test the enhanced volatility skew analysis with stochastic features"""
    print("🔍 Testing Enhanced Volatility Skew Analysis...")
    
    url = f"{BASE_URL}/api/advanced-volatility-analysis/{TEST_TICKER}/{TEST_EXPIRY}"
    
    params = {
        'analysis_type': 'enhanced_skew_analysis',
        'include_stochastic': 'true',
        'skew_model_type': 'local_stochastic'
    }
    
    try:
        response = requests.get(url, params=params, timeout=45)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                enhanced_analysis = data.get('enhanced_skew_analysis', {})
                
                # Check key components
                components = [
                    'traditional_skew',
                    'log_moneyness_analysis', 
                    'time_dependent_skew',
                    'l_function_skew',
                    'advanced_metrics',
                    'stochastic_adjustments'
                ]
                
                available_components = [comp for comp in components if comp in enhanced_analysis]
                
                print(f"   ✅ Enhanced Skew Analysis: {len(available_components)}/{len(components)} components")
                print(f"   Available: {', '.join(available_components)}")
                
                # Check specific metrics
                traditional = enhanced_analysis.get('traditional_skew', {})
                if 'total_skew' in traditional:
                    print(f"   Total Skew: {traditional['total_skew']:.4f}")
                
                log_analysis = enhanced_analysis.get('log_moneyness_analysis', {})
                if 'skew_slope' in log_analysis:
                    print(f"   Log Moneyness Skew Slope: {log_analysis['skew_slope']:.4f}")
                
                return True
            else:
                print(f"   ❌ API Error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Request Error: {e}")
        return False

def test_advanced_volatility_analysis_integration():
    """Test integration of local stochastic surface with advanced analysis"""
    print("🔍 Testing Advanced Volatility Analysis Integration...")
    
    url = f"{BASE_URL}/api/advanced-volatility-analysis/{TEST_TICKER}/{TEST_EXPIRY}"
    
    analysis_types = [
        'implied_surface',
        'local_surface', 
        'local_stochastic_surface',
        'enhanced_skew_analysis'
    ]
    
    results = {}
    
    for analysis_type in analysis_types:
        print(f"   Testing: {analysis_type}")
        
        params = {'analysis_type': analysis_type}
        
        # Add specific parameters for local stochastic surface
        if analysis_type == 'local_stochastic_surface':
            params.update({
                'l_function_type': 'polynomial',
                'grid_resolution': 30,
                'time_grid_points': 10,
                'moneyness_grid_points': 15
            })
        elif analysis_type == 'enhanced_skew_analysis':
            params.update({
                'include_stochastic': 'true',
                'skew_model_type': 'local_stochastic'
            })
        
        try:
            response = requests.get(url, params=params, timeout=45)
            
            if response.status_code == 200:
                data = response.json()
                success = data.get('success', False)
                results[analysis_type] = success
                
                if success:
                    print(f"   ✅ {analysis_type}: Working")
                else:
                    print(f"   ❌ {analysis_type}: {data.get('error', 'Unknown error')}")
            else:
                results[analysis_type] = False
                print(f"   ❌ {analysis_type}: HTTP {response.status_code}")
                
        except Exception as e:
            results[analysis_type] = False
            print(f"   ❌ {analysis_type}: {e}")
    
    successful_tests = sum(results.values())
    print(f"✅ Advanced Analysis Integration: {successful_tests}/{len(analysis_types)} types working")
    
    return successful_tests == len(analysis_types)

def test_l_function_performance():
    """Test performance of different L function types"""
    print("🔍 Testing L Function Performance...")
    
    url = f"{BASE_URL}/api/local-stochastic-volatility-surface/{TEST_TICKER}/{TEST_EXPIRY}"
    
    performance_results = {}
    
    for l_func_type in ['polynomial', 'exponential', 'spline']:
        params = {
            'l_function_type': l_func_type,
            'grid_resolution': 40,
            'time_grid_points': 12,
            'moneyness_grid_points': 20
        }
        
        try:
            start_time = time.time()
            response = requests.get(url, params=params, timeout=60)
            end_time = time.time()
            
            calculation_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    surface_data = data.get('surface_data', {})
                    grid_info = surface_data.get('grid_info', {})
                    
                    performance_results[l_func_type] = {
                        'calculation_time': calculation_time,
                        'total_points': grid_info.get('total_points', 0),
                        'points_per_second': grid_info.get('total_points', 0) / calculation_time if calculation_time > 0 else 0
                    }
                    
                    print(f"   ✅ {l_func_type}: {calculation_time:.2f}s, "
                          f"{performance_results[l_func_type]['points_per_second']:.0f} points/sec")
                else:
                    print(f"   ❌ {l_func_type}: Failed - {data.get('error', 'Unknown')}")
            else:
                print(f"   ❌ {l_func_type}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {l_func_type}: {e}")
    
    if performance_results:
        fastest = min(performance_results.items(), key=lambda x: x[1]['calculation_time'])
        print(f"   🏆 Fastest L Function: {fastest[0]} ({fastest[1]['calculation_time']:.2f}s)")
        return True
    else:
        print("   ❌ No performance data collected")
        return False

def main():
    """Run all local stochastic volatility tests"""
    print("🧪 Testing Local Stochastic Volatility 3D Surface Features")
    print("=" * 70)
    
    tests = [
        ("Local Stochastic Volatility 3D Surface", test_local_stochastic_volatility_surface),
        ("Enhanced Skew Analysis", test_enhanced_skew_analysis),
        ("Advanced Analysis Integration", test_advanced_volatility_analysis_integration),
        ("L Function Performance", test_l_function_performance)
    ]
    
    results = []
    detailed_results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_name == "Local Stochastic Volatility 3D Surface":
                result, details = test_func()
                detailed_results[test_name] = details
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All local stochastic volatility features are working!")
        print("✅ L function-based 3D surfaces with log moneyness and time to maturity")
        print("✅ Enhanced skew analysis with stochastic adjustments")
        print("✅ Advanced volatility modeling capabilities")
    else:
        print("⚠️ Some features need attention - check the errors above")
    
    # Show detailed results for L function tests
    if "Local Stochastic Volatility 3D Surface" in detailed_results:
        print("\n📈 L Function Performance Details:")
        for l_func, details in detailed_results["Local Stochastic Volatility 3D Surface"].items():
            if details.get('success'):
                print(f"   {l_func}: Quality Score {details.get('quality_score', 'N/A')}")

if __name__ == "__main__":
    main()
