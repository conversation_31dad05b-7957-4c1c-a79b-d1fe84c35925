#!/usr/bin/env python3
"""
Discord Webhook Integration for Greek Terminal License System
Sends notifications to Discord when licenses are created, modified, or expire
"""

import os
import json
import requests
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional

class DiscordWebhook:
    """Discord webhook integration for license notifications"""
    
    def __init__(self):
        # Default webhook URL - can be overridden by environment variable
        self.webhook_url = os.environ.get(
            'DISCORD_WEBHOOK_URL',
            'https://discord.com/api/webhooks/1386306236408135700/30d4dmoZMt1i32qo_-CkRPw3Hcrki0LsZ484fuSWvXmZ0vZ6sC11IslpFZ-J0oK0JNJe'
        )
        
        # Enable/disable webhook notifications
        self.enabled = os.environ.get('DISCORD_WEBHOOK_ENABLED', 'true').lower() == 'true'
        
        # Webhook settings
        self.timeout = 10  # seconds
        self.max_retries = 3
    
    def _create_embed(self, title: str, description: str, color: int, fields: list = None) -> Dict[str, Any]:
        """Create a Discord embed object"""
        embed = {
            "title": title,
            "description": description,
            "color": color,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "footer": {
                "text": "Greek Terminal License System",
                "icon_url": "https://cdn.discordapp.com/emojis/📊.png"
            },
            "thumbnail": {
                "url": "https://cdn.discordapp.com/emojis/🔑.png"
            }
        }
        
        if fields:
            embed["fields"] = fields
        
        return embed
    
    def _send_webhook(self, payload: Dict[str, Any]) -> bool:
        """Send webhook payload to Discord"""
        if not self.enabled:
            print("🔕 Discord webhook disabled")
            return True
        
        if not self.webhook_url:
            print("❌ Discord webhook URL not configured")
            return False
        
        try:
            for attempt in range(self.max_retries):
                response = requests.post(
                    self.webhook_url,
                    json=payload,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 204:
                    print("✅ Discord notification sent successfully")
                    return True
                elif response.status_code == 429:
                    # Rate limited, wait and retry
                    retry_after = response.json().get('retry_after', 1)
                    print(f"⏳ Discord rate limited, waiting {retry_after}s...")
                    import time
                    time.sleep(retry_after)
                    continue
                else:
                    print(f"⚠️ Discord webhook failed: {response.status_code} - {response.text}")
                    if attempt == self.max_retries - 1:
                        return False
                    continue
            
            return False
            
        except requests.exceptions.Timeout:
            print("⏰ Discord webhook timeout")
            return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Discord webhook error: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected Discord webhook error: {e}")
            return False
    
    def notify_license_created(self, license_key: str, discord_id: str, username: str, days_valid: int, expiry_date: str) -> bool:
        """Send notification when a new license is created"""
        
        # Calculate expiry timestamp for Discord
        try:
            expiry_dt = datetime.strptime(expiry_date, '%Y/%m/%d')
            expiry_timestamp = int(expiry_dt.timestamp())
        except:
            expiry_timestamp = None
        
        fields = [
            {
                "name": "🔑 License Key",
                "value": f"`{license_key}`",
                "inline": True
            },
            {
                "name": "👤 Username",
                "value": f"`{username}`",
                "inline": True
            },
            {
                "name": "🆔 Discord ID",
                "value": f"`{discord_id}`",
                "inline": True
            },
            {
                "name": "⏰ Duration",
                "value": f"`{days_valid} days`",
                "inline": True
            },
            {
                "name": "📅 Expires",
                "value": f"`{expiry_date}`" + (f" (<t:{expiry_timestamp}:R>)" if expiry_timestamp else ""),
                "inline": True
            },
            {
                "name": "📊 Status",
                "value": "🟢 **Active**",
                "inline": True
            }
        ]
        
        embed = self._create_embed(
            title="🆕 New License Created",
            description=f"A new license has been issued for **{username}**",
            color=0xC0C0C0,  # Silver color
            fields=fields
        )
        
        payload = {
            "username": "Greek Terminal",
            "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
            "embeds": [embed]
        }
        
        return self._send_webhook(payload)
    
    def notify_license_extended(self, license_key: str, username: str, additional_days: int, new_expiry: str) -> bool:
        """Send notification when a license is extended"""
        
        fields = [
            {
                "name": "🔑 License Key",
                "value": f"`{license_key}`",
                "inline": True
            },
            {
                "name": "👤 Username",
                "value": f"`{username}`",
                "inline": True
            },
            {
                "name": "➕ Extended By",
                "value": f"`{additional_days} days`",
                "inline": True
            },
            {
                "name": "📅 New Expiry",
                "value": f"`{new_expiry}`",
                "inline": True
            }
        ]
        
        embed = self._create_embed(
            title="⏰ License Extended",
            description=f"License for **{username}** has been extended",
            color=0x00FF00,  # Green for positive action
            fields=fields
        )
        
        payload = {
            "username": "Greek Terminal",
            "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
            "embeds": [embed]
        }
        
        return self._send_webhook(payload)
    
    def notify_license_removed(self, license_key: str, username: str) -> bool:
        """Send notification when a license is removed"""
        
        fields = [
            {
                "name": "🔑 License Key",
                "value": f"`{license_key}`",
                "inline": True
            },
            {
                "name": "👤 Username",
                "value": f"`{username}`",
                "inline": True
            },
            {
                "name": "📊 Status",
                "value": "🔴 **Revoked**",
                "inline": True
            }
        ]
        
        embed = self._create_embed(
            title="🗑️ License Removed",
            description=f"License for **{username}** has been revoked",
            color=0xFF0000,  # Red for removal
            fields=fields
        )
        
        payload = {
            "username": "Greek Terminal",
            "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
            "embeds": [embed]
        }
        
        return self._send_webhook(payload)
    
    def notify_license_login(self, license_key: str, username: str, discord_id: str, days_remaining: int) -> bool:
        """Send notification when someone logs in"""

        # Check if login notifications are enabled
        login_notifications = os.environ.get('DISCORD_NOTIFY_LOGINS', 'false').lower() == 'true'
        if not login_notifications:
            print("🔕 Login notifications disabled")
            return True

        # Get current timestamp
        login_time = datetime.now(timezone.utc)
        login_timestamp = int(login_time.timestamp())

        status_emoji = "🟢" if days_remaining > 7 else "🟡" if days_remaining > 0 else "🔴"
        status_text = "Active" if days_remaining > 7 else "Expiring Soon" if days_remaining > 0 else "Expired"

        fields = [
            {
                "name": "👤 Username",
                "value": f"`{username}`",
                "inline": True
            },
            {
                "name": "🆔 Discord ID",
                "value": f"`{discord_id}`",
                "inline": True
            },
            {
                "name": "🔑 License Key",
                "value": f"`{license_key}`",
                "inline": True
            },
            {
                "name": "🕐 Login Time",
                "value": f"<t:{login_timestamp}:F>\n<t:{login_timestamp}:R>",
                "inline": True
            },
            {
                "name": "⏰ Days Remaining",
                "value": f"`{days_remaining} days`",
                "inline": True
            },
            {
                "name": "📊 License Status",
                "value": f"{status_emoji} **{status_text}**",
                "inline": True
            }
        ]

        embed = self._create_embed(
            title="🔐 User Login Event",
            description=f"**{username}** has logged into Greek Terminal",
            color=0xC0C0C0,  # Silver color
            fields=fields
        )

        payload = {
            "username": "Greek Terminal",
            "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
            "embeds": [embed]
        }

        print(f"📢 Sending login notification for {username}")
        return self._send_webhook(payload)
    
    def notify_license_expiring(self, licenses_expiring: list) -> bool:
        """Send notification for licenses expiring soon"""
        
        if not licenses_expiring:
            return True
        
        description = f"**{len(licenses_expiring)}** license(s) expiring within 7 days"
        
        # Optimization 31: Use list comprehension for field creation
        fields = [
            {
                "name": f"🔑 {license_info['license_key']}",
                "value": f"👤 `{license_info['username']}` - 📅 `{license_info['expiry_date']}` ({license_info['days_remaining']} days)",
                "inline": False
            }
            for license_info in licenses_expiring[:10]  # Limit to 10 to avoid embed limits
        ]
        
        if len(licenses_expiring) > 10:
            fields.append({
                "name": "➕ More",
                "value": f"... and {len(licenses_expiring) - 10} more licenses",
                "inline": False
            })
        
        embed = self._create_embed(
            title="⚠️ Licenses Expiring Soon",
            description=description,
            color=0xFFA500,  # Orange for warning
            fields=fields
        )
        
        payload = {
            "username": "Greek Terminal",
            "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
            "embeds": [embed]
        }
        
        return self._send_webhook(payload)

# Global webhook instance
_discord_webhook = None

def get_discord_webhook():
    """Get the global Discord webhook instance"""
    global _discord_webhook
    if _discord_webhook is None:
        _discord_webhook = DiscordWebhook()
    return _discord_webhook

def test_webhook():
    """Test the Discord webhook connection"""
    webhook = get_discord_webhook()
    
    embed = webhook._create_embed(
        title="🧪 Webhook Test",
        description="Greek Terminal license system webhook is working correctly!",
        color=0xC0C0C0,
        fields=[
            {
                "name": "📊 System Status",
                "value": "✅ **Online**",
                "inline": True
            },
            {
                "name": "🕐 Test Time",
                "value": f"`{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}`",
                "inline": True
            }
        ]
    )
    
    payload = {
        "username": "Greek Terminal",
        "avatar_url": "https://cdn.discordapp.com/emojis/📈.png",
        "embeds": [embed]
    }
    
    return webhook._send_webhook(payload)
