#!/usr/bin/env python3
"""
Standalone launcher for Greek Terminal
This script is designed to be compiled into an .exe file
"""

import sys
import os
import webbrowser
import threading
import time
import json
import tempfile
import subprocess
from datetime import datetime, timedelta

# Hide console window on Windows
if sys.platform == "win32":
    import ctypes
    from ctypes import wintypes

# Add the current directory to Python path for imports
if hasattr(sys, '_MEIPASS'):
    # Running as PyInstaller bundle
    bundle_dir = sys._MEIPASS
else:
    # Running as script
    bundle_dir = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, bundle_dir)

# Load environment variables from .env file
def load_environment():
    """Load environment variables from .env file"""
    try:
        from dotenv import load_dotenv

        # Check if running as executable
        if hasattr(sys, '_MEIPASS'):
            # Running as PyInstaller bundle - look for .env in bundle
            env_path = os.path.join(sys._MEIPASS, '.env')
        else:
            # Running as script - look for .env in current directory
            env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')

        if os.path.exists(env_path):
            load_dotenv(env_path)
            print(f"✅ Environment loaded from {env_path}")
            # Debug: Check if GitHub credentials are loaded
            github_token = os.environ.get('GITHUB_TOKEN', '')
            github_repo = os.environ.get('GITHUB_REPO', '')
            print(f"🔍 GitHub Token loaded: {'Yes' if github_token else 'No'}")
            print(f"🔍 GitHub Repo loaded: {github_repo if github_repo else 'No'}")
        else:
            print(f"⚠️ .env file not found at {env_path}")
            # List files in bundle directory for debugging
            if hasattr(sys, '_MEIPASS'):
                print(f"📁 Files in bundle directory:")
                try:
                    # Optimization 30: Use list comprehension with filter
                    env_files = [file for file in os.listdir(sys._MEIPASS)
                                if file.startswith('.env') or 'env' in file.lower()]
                    for file in env_files:
                        print(f"   - {file}")
                except Exception as e:
                    print(f"   Error listing files: {e}")

    except ImportError:
        print("⚠️ python-dotenv not installed, using system environment variables")
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")

# Load environment early
load_environment()

# Trial system removed - using license system only

# Expiration Configuration
class ExpirationConfig:
    """Configuration for expiration functionality"""
    # Default expiration period (30 days)
    EXPIRATION_DAYS = int(os.environ.get('GT_EXPIRATION_DAYS', '30'))
    EXPIRATION_MINUTES = int(os.environ.get('GT_EXPIRATION_MINUTES', '0'))  # For testing
    WARNING_DAYS = int(os.environ.get('GT_WARNING_DAYS', '7'))  # Show warning 7 days before expiration

    @classmethod
    def get_expiration_timedelta(cls):
        """Get the expiration timedelta based on configuration"""
        if cls.EXPIRATION_MINUTES > 0:
            return timedelta(minutes=cls.EXPIRATION_MINUTES)
        else:
            return timedelta(days=cls.EXPIRATION_DAYS)

    @classmethod
    def get_warning_timedelta(cls):
        """Get the warning timedelta based on configuration"""
        if cls.EXPIRATION_MINUTES > 0:
            # For minute-based testing, warn at 75% of expiration time
            warning_minutes = max(1, int(cls.EXPIRATION_MINUTES * 0.75))
            return timedelta(minutes=warning_minutes)
        else:
            return timedelta(days=cls.WARNING_DAYS)

def get_install_data_file():
    """Get the path to the installation data file"""
    temp_dir = tempfile.gettempdir()
    return os.path.join(temp_dir, '.gt_install_data')

def record_first_run():
    """Record the first run timestamp"""
    data_file = get_install_data_file()

    if not os.path.exists(data_file):
        install_data = {
            'first_run': datetime.now().isoformat(),
            'executable_path': sys.executable if hasattr(sys, '_MEIPASS') else os.path.abspath(__file__)
        }

        try:
            with open(data_file, 'w') as f:
                json.dump(install_data, f)
        except Exception:
            pass  # Silently fail if we can't write the file

def get_first_run_time():
    """Get the first run timestamp"""
    data_file = get_install_data_file()

    try:
        if os.path.exists(data_file):
            with open(data_file, 'r') as f:
                data = json.load(f)
                return datetime.fromisoformat(data['first_run'])
    except Exception:
        pass

    return None

def check_expiration():
    """Check if the application has expired"""
    first_run = get_first_run_time()

    if first_run is None:
        # First time running, record the timestamp
        record_first_run()
        return True

    now = datetime.now()
    expiration_time = first_run + ExpirationConfig.get_expiration_timedelta()
    warning_time = expiration_time - ExpirationConfig.get_warning_timedelta()

    # Debug output for testing (only when explicitly enabled)
    if os.environ.get('GT_DEBUG_EXPIRATION'):
        print(f"🔍 Expiration Debug:")
        print(f"   First run: {first_run}")
        print(f"   Current time: {now}")
        print(f"   Expiration time: {expiration_time}")
        print(f"   Warning time: {warning_time}")
        print(f"   Days since first run: {(now - first_run).days}")
        print(f"   Days until expiration: {(expiration_time - now).days}")

    if now >= expiration_time:
        # Application has expired
        show_expiration_message()
        return False
    elif now >= warning_time:
        # Show warning but continue
        show_warning_message(expiration_time)
        return True
    else:
        # Still valid, no warning needed
        return True

def show_warning_message(expiration_time):
    """Show warning message about upcoming expiration"""
    time_left = expiration_time - datetime.now()

    # Calculate more precise time remaining
    if time_left.days > 0:
        time_str = f"{time_left.days} day(s)"
    elif time_left.seconds > 3600:
        hours = time_left.seconds // 3600
        time_str = f"{hours} hour(s)"
    else:
        minutes = time_left.seconds // 60
        time_str = f"{minutes} minute(s)"

    # Always print to console for visibility
    print(f"⚠️  WARNING: Greek Terminal license expires in {time_str}")
    print(f"   Expiration date: {expiration_time.strftime('%Y-%m-%d %H:%M')}")

    if sys.platform == "win32":
        try:
            import ctypes
            message = (
                f"Greek Terminal License Warning\n\n"
                f"Your Greek Terminal license will expire in {time_str}.\n"
                f"Expiration date: {expiration_time.strftime('%Y-%m-%d %H:%M')}\n\n"
                f"Please contact support to renew your subscription."
            )
            ctypes.windll.user32.MessageBoxW(0, message, "Greek Terminal - License Warning", 0x30)
        except Exception:
            pass  # Already printed to console above
    else:
        pass  # Already printed to console above

def show_expiration_message():
    """Show expiration message and handle self-deletion"""
    if sys.platform == "win32":
        try:
            import ctypes
            message = (
                "Greek Terminal License Expired\n\n"
                "Your Greek Terminal license has expired.\n"
                "Please contact support to renew your subscription.\n\n"
                "The application will now close."
            )
            ctypes.windll.user32.MessageBoxW(0, message, "Greek Terminal - License Expired", 0x10)
        except Exception:
            print("Greek Terminal license has expired")
    else:
        print("Greek Terminal license has expired")

    # Attempt self-deletion if running as executable
    if hasattr(sys, '_MEIPASS'):
        try:
            self_delete()
        except Exception:
            pass  # Silently fail if self-deletion doesn't work

def self_delete():
    """Delete the executable file after the process exits"""
    if not hasattr(sys, '_MEIPASS'):
        return  # Only delete if running as executable

    executable_path = sys.executable

    if sys.platform == "win32":
        # Create a batch script to delete the executable after the process exits
        batch_content = f'''@echo off
timeout /t 2 /nobreak >nul
del /f /q "{executable_path}"
del /f /q "%~f0"
'''

        try:
            temp_dir = tempfile.gettempdir()
            batch_file = os.path.join(temp_dir, 'gt_cleanup.bat')

            with open(batch_file, 'w') as f:
                f.write(batch_content)

            # Start the batch file in a separate process
            subprocess.Popen([batch_file], shell=True, creationflags=subprocess.CREATE_NO_WINDOW)
        except Exception:
            pass  # Silently fail if self-deletion doesn't work

def hide_console_window():
    """Hide the console window on Windows"""
    if sys.platform == "win32":
        try:
            # Get console window handle
            kernel32 = ctypes.windll.kernel32
            user32 = ctypes.windll.user32

            # Get console window
            console_window = kernel32.GetConsoleWindow()

            if console_window:
                # Hide the console window
                user32.ShowWindow(console_window, 0)  # 0 = SW_HIDE
        except Exception:
            pass  # Silently fail if hiding doesn't work

def open_browser():
    """Open the dashboard in the default browser after a short delay"""
    time.sleep(3)  # Wait for Flask to start
    try:
        webbrowser.open('http://localhost:5000')
        # Hide console window after browser opens
        time.sleep(2)  # Give browser time to open
        hide_console_window()
    except Exception:
        pass  # Silently fail if browser can't be opened



def load_build_info():
    """Load build information if available"""
    try:
        # Check if running as executable
        if hasattr(sys, '_MEIPASS'):
            build_info_path = os.path.join(sys._MEIPASS, 'build_info.json')
        else:
            build_info_path = 'build_info.json'

        if os.path.exists(build_info_path):
            with open(build_info_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Could not load build info: {e}")

    return None



def main():
    """Main function to run the dashboard"""
    try:
        # Check legacy expiration system first (for compatibility)
        if not check_expiration():
            # Application has expired, exit
            sys.exit(1)



        print("Starting Greek Terminal...")
        print("Opening browser and hiding console...")

        # Start browser in a separate thread
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()

        # Import and run the Flask app
        from app import run_dashboard
        run_dashboard()

    except KeyboardInterrupt:
        print("\nShutting down Greek Terminal...")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting Greek Terminal: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == '__main__':
    main()
