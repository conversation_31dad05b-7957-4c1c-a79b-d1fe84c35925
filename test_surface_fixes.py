#!/usr/bin/env python3
"""
Test script to verify volatility surface and Greek surface fixes
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_TICKER = "SPY"
TEST_EXPIRY = "2024-12-20"  # Adjust as needed

def test_volatility_surface():
    """Test volatility surface endpoint"""
    print("🔍 Testing Volatility Surface...")
    
    url = f"{BASE_URL}/api/volatility-surface/{TEST_TICKER}"
    
    try:
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                surface_data = data.get('surface_data', [])
                print(f"✅ Volatility Surface: {len(surface_data)} data points")
                print(f"   Current Price: ${data.get('current_price', 'N/A')}")
                print(f"   Expiry Dates: {len(data.get('expiry_dates', []))}")
                
                # Check data quality
                if surface_data:
                    sample = surface_data[0]
                    print(f"   Sample Point: Strike=${sample.get('strike')}, IV={sample.get('implied_volatility'):.3f}")
                    return True
                else:
                    print("❌ No surface data returned")
                    return False
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def test_volatility_skew():
    """Test volatility skew endpoint"""
    print("🔍 Testing Volatility Skew...")
    
    url = f"{BASE_URL}/api/volatility-skew/{TEST_TICKER}/{TEST_EXPIRY}"
    
    try:
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                skew_data = data.get('skew_data', [])
                skew_metrics = data.get('skew_metrics', {})
                print(f"✅ Volatility Skew: {len(skew_data)} data points")
                print(f"   Current Price: ${data.get('current_price', 'N/A')}")
                print(f"   ATM IV: {skew_metrics.get('atm_iv', 'N/A')}")
                print(f"   Skew: {skew_metrics.get('skew', 'N/A')}")
                
                # Check data quality
                if skew_data:
                    calls = [d for d in skew_data if d['option_type'] == 'call']
                    puts = [d for d in skew_data if d['option_type'] == 'put']
                    print(f"   Calls: {len(calls)}, Puts: {len(puts)}")
                    return True
                else:
                    print("❌ No skew data returned")
                    return False
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def test_greeks_surface():
    """Test Greeks surface endpoint"""
    print("🔍 Testing Greeks Surface...")
    
    url = f"{BASE_URL}/api/greeks-surface/{TEST_TICKER}/{TEST_EXPIRY}"
    params = {
        'greek_type': 'gamma',
        'models': 'black_scholes,sabr'
    }
    
    try:
        response = requests.get(url, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                surface_data = data.get('surface_data', {})
                print(f"✅ Greeks Surface: Generated successfully")
                print(f"   Current Price: ${data.get('current_price', 'N/A')}")
                print(f"   Models: {list(surface_data.keys())}")
                
                # Check data quality
                for model, model_data in surface_data.items():
                    if 'gamma' in model_data:
                        gamma_data = model_data['gamma']
                        print(f"   {model}: {len(gamma_data)} x {len(gamma_data[0]) if gamma_data else 0} grid")
                
                return True
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def test_gamma_surface():
    """Test gamma surface endpoint"""
    print("🔍 Testing Gamma Surface...")
    
    url = f"{BASE_URL}/api/gamma-surface/{TEST_TICKER}/{TEST_EXPIRY}"
    params = {
        'price_range': 5,
        'time_range': 60,
        'price_step': 2.0,
        'time_step': 5,
        'gex_type': 'normal'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Gamma Surface: Calculation started")
                print(f"   Status: {data.get('status', 'N/A')}")
                print(f"   Settings: {data.get('settings', {})}")
                
                # Check status endpoint
                time.sleep(2)  # Wait a bit
                status_url = f"{BASE_URL}/api/gamma-surface-status/{TEST_TICKER}/{TEST_EXPIRY}"
                status_response = requests.get(status_url, timeout=10)
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"   Progress: {status_data.get('progress', 'N/A')}%")
                    print(f"   Message: {status_data.get('message', 'N/A')}")
                
                return True
            else:
                print(f"❌ API Error: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request Error: {e}")
        return False

def main():
    """Run all surface tests"""
    print("🧪 Testing Surface Generation Fixes")
    print("=" * 50)
    
    results = []
    
    # Test each endpoint
    results.append(("Volatility Surface", test_volatility_surface()))
    results.append(("Volatility Skew", test_volatility_skew()))
    results.append(("Greeks Surface", test_greeks_surface()))
    results.append(("Gamma Surface", test_gamma_surface()))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All surface generation endpoints are working!")
    else:
        print("⚠️ Some endpoints need attention")

if __name__ == "__main__":
    main()
