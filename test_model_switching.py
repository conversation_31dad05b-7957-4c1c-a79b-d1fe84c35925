#!/usr/bin/env python3
"""
Test script to verify that model switching works correctly
and that exposure calculations use the currently selected model.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_model_switching():
    """Test that model switching affects exposure calculations"""
    print("=" * 60)
    print("🧪 Testing Model Switching Functionality")
    print("=" * 60)
    
    # Test parameters
    ticker = "SPY"
    expiry = "2025-07-18"
    
    try:
        # Test 1: Set to Black-Scholes and get GEX
        print("\n1️⃣ Testing Black-Scholes Model")
        response = requests.post(f"{BASE_URL}/api/set-pricing-model", 
                               json={"model": "black_scholes"})
        if response.status_code == 200:
            print("   ✅ Successfully set to Black-Scholes")
        else:
            print(f"   ❌ Failed to set Black-Scholes: {response.text}")
            return False
        
        # Wait a moment for model to be set
        time.sleep(1)
        
        # Get GEX with Black-Scholes
        response = requests.get(f"{BASE_URL}/api/gamma-exposure/{ticker}/{expiry}")
        if response.status_code == 200:
            bs_data = response.json()
            if bs_data.get('success'):
                bs_gex = bs_data.get('gex_data', [])
                print(f"   ✅ Got GEX data with {len(bs_gex)} strikes")
                if bs_gex:
                    print(f"   📊 Sample GEX: Strike {bs_gex[0]['strike']}, Net GEX: {bs_gex[0]['net_gex']:.2f}")
            else:
                print(f"   ❌ GEX request failed: {bs_data.get('error')}")
                return False
        else:
            print(f"   ❌ GEX request failed with status {response.status_code}")
            return False
        
        # Test 2: Set to SABR and get GEX
        print("\n2️⃣ Testing SABR Model")
        response = requests.post(f"{BASE_URL}/api/set-pricing-model", 
                               json={"model": "sabr"})
        if response.status_code == 200:
            print("   ✅ Successfully set to SABR")
        else:
            print(f"   ❌ Failed to set SABR: {response.text}")
            return False
        
        # Wait a moment for model to be set
        time.sleep(1)
        
        # Get GEX with SABR
        response = requests.get(f"{BASE_URL}/api/gamma-exposure/{ticker}/{expiry}")
        if response.status_code == 200:
            sabr_data = response.json()
            if sabr_data.get('success'):
                sabr_gex = sabr_data.get('gex_data', [])
                print(f"   ✅ Got GEX data with {len(sabr_gex)} strikes")
                if sabr_gex:
                    print(f"   📊 Sample GEX: Strike {sabr_gex[0]['strike']}, Net GEX: {sabr_gex[0]['net_gex']:.2f}")
            else:
                print(f"   ❌ GEX request failed: {sabr_data.get('error')}")
                return False
        else:
            print(f"   ❌ GEX request failed with status {response.status_code}")
            return False
        
        # Test 3: Compare results
        print("\n3️⃣ Comparing Results")
        if bs_gex and sabr_gex and len(bs_gex) > 0 and len(sabr_gex) > 0:
            bs_net_gex = bs_gex[0]['net_gex']
            sabr_net_gex = sabr_gex[0]['net_gex']
            
            if abs(bs_net_gex - sabr_net_gex) > 0.001:  # Allow for small numerical differences
                print(f"   ✅ Models produce different results!")
                print(f"   📊 Black-Scholes Net GEX: {bs_net_gex:.6f}")
                print(f"   📊 SABR Net GEX: {sabr_net_gex:.6f}")
                print(f"   📊 Difference: {abs(bs_net_gex - sabr_net_gex):.6f}")
                print("   🎉 Model switching is working correctly!")
                return True
            else:
                print(f"   ⚠️ Models produce similar results:")
                print(f"   📊 Black-Scholes Net GEX: {bs_net_gex:.6f}")
                print(f"   📊 SABR Net GEX: {sabr_net_gex:.6f}")
                print("   🤔 This might indicate the models are not switching properly")
                return False
        else:
            print("   ❌ Could not compare results - insufficient data")
            return False
        
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

def test_model_test_endpoint():
    """Test the dedicated model test endpoint"""
    print("\n" + "=" * 60)
    print("🧪 Testing Model Test Endpoint")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/api/test-pricing-models")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                results = data.get('results', {})
                print("   ✅ Model test endpoint working")
                print("   📊 Results:")
                for model, result in results.items():
                    if 'error' in result:
                        print(f"      {model}: ❌ {result['error']}")
                    else:
                        print(f"      {model}: Delta={result['delta']:.6f}, Gamma={result['gamma']:.8f}")
                
                # Check if models produce different results
                deltas = [r['delta'] for r in results.values() if 'delta' in r]
                if len(set(round(d, 6) for d in deltas)) > 1:
                    print("   ✅ Models produce different deltas - switching works!")
                    return True
                else:
                    print("   ⚠️ All models produce same delta - might be an issue")
                    return False
            else:
                print(f"   ❌ Test endpoint failed: {data.get('error')}")
                return False
        else:
            print(f"   ❌ Test endpoint request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Model Switching Tests")
    
    # Test 1: Model switching with exposure calculations
    test1_passed = test_model_switching()
    
    # Test 2: Dedicated model test endpoint
    test2_passed = test_model_test_endpoint()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary")
    print("=" * 60)
    print(f"Model Switching Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Model Test Endpoint: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! Model switching is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Model switching may need attention.")
