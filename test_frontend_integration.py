#!/usr/bin/env python3
"""
Test script to verify frontend integration of Local Stochastic Volatility features
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options
import sys

def test_frontend_elements():
    """Test that the new frontend elements are present"""
    print("🔍 Testing Frontend Elements...")
    
    # Setup Chrome options for headless testing
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://localhost:5000")
        
        # Wait for page to load
        time.sleep(3)
        
        # Check if volatility analysis dropdown exists
        try:
            analysis_dropdown = driver.find_element(By.ID, "volatility-analysis-type")
            options = [option.get_attribute("value") for option in analysis_dropdown.find_elements(By.TAG_NAME, "option")]
            
            expected_options = [
                "implied_surface",
                "local_surface", 
                "local_stochastic_surface",
                "stochastic_paths",
                "model_comparison",
                "term_structure",
                "skew_analysis",
                "enhanced_skew_analysis"
            ]
            
            missing_options = [opt for opt in expected_options if opt not in options]
            
            if not missing_options:
                print("   ✅ All volatility analysis options present")
                print(f"   Available options: {', '.join(options)}")
                
                # Test selecting local stochastic surface
                select = Select(analysis_dropdown)
                select.select_by_value("local_stochastic_surface")
                time.sleep(1)
                
                # Check if local stochastic settings appear
                try:
                    local_stoch_settings = driver.find_element(By.ID, "local-stochastic-vol-settings")
                    if local_stoch_settings.is_displayed():
                        print("   ✅ Local Stochastic Volatility settings panel appears")
                    else:
                        print("   ❌ Local Stochastic Volatility settings panel not visible")
                        return False
                except:
                    print("   ❌ Local Stochastic Volatility settings panel not found")
                    return False
                
                # Test enhanced skew analysis
                select.select_by_value("enhanced_skew_analysis")
                time.sleep(1)
                
                try:
                    enhanced_skew_settings = driver.find_element(By.ID, "enhanced-skew-settings")
                    if enhanced_skew_settings.is_displayed():
                        print("   ✅ Enhanced Skew Analysis settings panel appears")
                    else:
                        print("   ❌ Enhanced Skew Analysis settings panel not visible")
                        return False
                except:
                    print("   ❌ Enhanced Skew Analysis settings panel not found")
                    return False
                
                return True
            else:
                print(f"   ❌ Missing options: {', '.join(missing_options)}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error finding volatility analysis dropdown: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error setting up browser: {e}")
        print("   ℹ️ Note: This test requires Chrome and chromedriver to be installed")
        return False
    finally:
        try:
            driver.quit()
        except:
            pass

def test_api_endpoints_availability():
    """Test that the new API endpoints are available"""
    print("🔍 Testing API Endpoints Availability...")
    
    base_url = "http://localhost:5000"
    test_ticker = "SPY"
    test_expiry = "2024-12-20"
    
    endpoints_to_test = [
        {
            'name': 'Local Stochastic Volatility Surface',
            'url': f"{base_url}/api/local-stochastic-volatility-surface/{test_ticker}/{test_expiry}",
            'params': {
                'l_function_type': 'polynomial',
                'grid_resolution': 30,
                'time_grid_points': 10,
                'moneyness_grid_points': 15
            }
        },
        {
            'name': 'Enhanced Skew Analysis',
            'url': f"{base_url}/api/advanced-volatility-analysis/{test_ticker}/{test_expiry}",
            'params': {
                'analysis_type': 'enhanced_skew_analysis',
                'include_stochastic': 'true',
                'skew_model_type': 'local_stochastic'
            }
        }
    ]
    
    results = []
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(endpoint['url'], params=endpoint['params'], timeout=30)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success', False):
                        print(f"   ✅ {endpoint['name']}: Working")
                        results.append(True)
                    else:
                        print(f"   ⚠️ {endpoint['name']}: API returned error - {data.get('error', 'Unknown')}")
                        results.append(False)
                except:
                    print(f"   ❌ {endpoint['name']}: Invalid JSON response")
                    results.append(False)
            else:
                print(f"   ❌ {endpoint['name']}: HTTP {response.status_code}")
                results.append(False)
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ {endpoint['name']}: Server not running")
            results.append(False)
        except Exception as e:
            print(f"   ❌ {endpoint['name']}: {e}")
            results.append(False)
    
    return all(results)

def test_settings_integration():
    """Test that settings are properly integrated"""
    print("🔍 Testing Settings Integration...")
    
    # Test that the HTML contains the new settings
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        required_elements = [
            'local-stochastic-vol-settings',
            'enhanced-skew-settings',
            'l-function-type',
            'time-grid-points',
            'moneyness-grid-points',
            'skew-model-type',
            'include-stochastic'
        ]
        
        missing_elements = [elem for elem in required_elements if elem not in html_content]
        
        if not missing_elements:
            print("   ✅ All required HTML elements present")
            
            # Check for the new options in the dropdown
            if 'local_stochastic_surface' in html_content and 'enhanced_skew_analysis' in html_content:
                print("   ✅ New analysis options in dropdown")
                return True
            else:
                print("   ❌ New analysis options missing from dropdown")
                return False
        else:
            print(f"   ❌ Missing HTML elements: {', '.join(missing_elements)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error reading HTML template: {e}")
        return False

def test_javascript_integration():
    """Test that JavaScript changes are present"""
    print("🔍 Testing JavaScript Integration...")
    
    try:
        with open('static/js/dashboard.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        required_js_elements = [
            'local-stochastic-vol-settings',
            'enhanced-skew-settings',
            'local_stochastic_surface',
            'enhanced_skew_analysis',
            'l-function-type',
            'skew-model-type'
        ]
        
        missing_js_elements = [elem for elem in required_js_elements if elem not in js_content]
        
        if not missing_js_elements:
            print("   ✅ All required JavaScript elements present")
            
            # Check for the new API endpoint handling
            if 'local-stochastic-volatility-surface' in js_content:
                print("   ✅ New API endpoint handling present")
                return True
            else:
                print("   ❌ New API endpoint handling missing")
                return False
        else:
            print(f"   ❌ Missing JavaScript elements: {', '.join(missing_js_elements)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error reading JavaScript file: {e}")
        return False

def main():
    """Run all frontend integration tests"""
    print("🧪 Testing Frontend Integration for Local Stochastic Volatility")
    print("=" * 70)
    
    tests = [
        ("Settings Integration", test_settings_integration),
        ("JavaScript Integration", test_javascript_integration),
        ("API Endpoints Availability", test_api_endpoints_availability),
        ("Frontend Elements", test_frontend_elements)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All frontend integration tests passed!")
        print("✅ Local Stochastic Volatility features are available in the UI")
        print("✅ Enhanced Skew Analysis options are integrated")
        print("✅ Settings panels and controls are working")
    else:
        print("⚠️ Some integration issues found - check the errors above")
        if passed >= len(results) - 1:
            print("ℹ️ Most tests passed - minor issues may be due to server not running")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
