# Greek Terminal Signal System Documentation

## Overview

The Greek Terminal Signal System is a comprehensive options flow analysis engine that generates trading signals based on gamma exposure (GEX), vanna exposure, and charm exposure calculations. The system analyzes options positioning to identify market bias, price targets, and optimal entry/exit points.

## Signal Types

### 1. Intraday Gamma Signals
**Purpose**: Generate simple BUY/SELL/HOLD signals based on gamma positioning and market bias.

**Key Components**:
- Simple signal action (BUY/SELL/HOLD)
- Market bias calculation (BULLISH/BEARISH/NEUTRAL)
- Confidence levels and supporting factors
- Signal strength assessment
- Clear reasoning for each signal

### 2. EOD Vanna Signals
**Purpose**: Capture end-of-day volatility plays based on vanna exposure.

**Key Components**:
- Volatility regime analysis
- Delta sensitivity calculations
- Time-based entry signals
- Risk assessment

### 3. EOD Charm Signals
**Purpose**: Generate simple BUY/SELL/HOLD signals based on charm exposure and time decay effects.

**Key Components**:
- Simple signal action (BUY/SELL/HOLD)
- Charm-based market bias calculation
- Time decay rate analysis
- Confidence levels and supporting factors
- Signal strength assessment
- Clear reasoning for each signal

## Core Decision Factors

### Gamma Exposure Analysis

#### Zero GEX Level
- **Definition**: The closest strike to current price where volume-based gamma exposure flips sign
- **Calculation**: Finds the nearest strike to current price that represents a gamma sign change (positive to negative or vice versa)
- **Note**: Uses volume-based gamma exposure (VGEX) instead of OI-based gamma exposure (GEX)
- **Method**: No interpolation - uses actual strike prices where gamma flips occur
- **Significance**:
  - Above Zero GEX = Positive Gamma Regime (market makers dampen volatility)
  - Below Zero GEX = Negative Gamma Regime (market makers amplify moves)

#### Support and Resistance Levels
- **Positive Gamma Below Price** = Support (market makers buy dips)
- **Positive Gamma Above Price** = Resistance (market makers sell rallies)
- **Negative Gamma** = Opposite effect (amplifies moves)

#### Market Regime Classification
1. **STRONG_POSITIVE_GAMMA**: Net GEX > $1M (strong dampening effect)
2. **POSITIVE_GAMMA**: Net GEX > 0 (moderate dampening)
3. **NEGATIVE_GAMMA**: Net GEX < 0 (moderate amplification)
4. **STRONG_NEGATIVE_GAMMA**: Net GEX < -$1M (strong amplification)

### Bias Calculation Factors

#### Factor 1: Position Relative to Zero GEX (Weight: ±1.0)
- Above Zero GEX: +1.0 (bullish bias)
- Below Zero GEX: -1.0 (bearish bias)

#### Factor 2: Call vs Put Open Interest (Weight: ±0.5)
- Call OI > Put OI * 1.2: +0.5 (bullish)
- Put OI > Call OI * 1.2: -0.5 (bearish)

#### Factor 3: Volume Analysis (Weight: ±0.3)
- Call Volume > Put Volume * 1.3: +0.3 (bullish)
- Put Volume > Call Volume * 1.3: -0.3 (bearish)

#### Factor 4: Distance from Gamma Levels (Weight: ±0.3)
- Near Support: +0.3 (bullish)
- Near Resistance: -0.3 (bearish)

### Confidence Scoring

#### Bias Strength (Max 30 points)
- Strong bias (>70%): +30 points
- Moderate bias (>40%): +20 points

#### Volume Confirmation (Max 25 points)
- High volume (>1000): +25 points
- Moderate volume (>500): +15 points

#### Gamma Magnitude (Max 25 points)
- High GEX (>$1M): +25 points
- Moderate GEX (>$500K): +15 points

#### Supporting Factors (Max 20 points)
- Multiple factors (≥3): +20 points
- Some factors (≥2): +10 points

**Confidence Levels**:
- HIGH: ≥70 points
- MEDIUM: 40-69 points
- LOW: <40 points

## Simple Signal Generation

### Signal Logic
The gamma signal system generates simple BUY/SELL/HOLD signals based on:

#### BUY Signal Conditions
- **Strong BUY**: Bullish bias ≥50% + Confidence ≥40%
- **Moderate BUY**: Bullish bias ≥30% (regardless of confidence)

#### SELL Signal Conditions
- **Strong SELL**: Bearish bias ≥50% + Confidence ≥40%
- **Moderate SELL**: Bearish bias ≥30% (regardless of confidence)

#### HOLD Signal Conditions
- Neutral bias or weak signals (<30% strength)
- Low confidence with weak bias
- Any error or invalid data conditions

### Signal Output
Each signal includes:
- **Action**: BUY, SELL, or HOLD
- **Reason**: Clear explanation of the signal logic
- **Confidence Level**: HIGH, MEDIUM, or LOW
- **Bias Strength**: Percentage strength of the underlying bias
- **Current Price**: Reference price for the signal

## Vanna Signal Analysis

### Volatility Regime Assessment
- **High Vanna**: Significant delta sensitivity to volatility changes
- **Low Vanna**: Stable delta regardless of volatility
- **Positive Vanna**: Delta increases with volatility
- **Negative Vanna**: Delta decreases with volatility

### Entry Signal Types
1. **VOLATILITY_EXPANSION**: High positive vanna suggests delta acceleration
2. **VOLATILITY_CONTRACTION**: High negative vanna suggests delta deceleration
3. **VANNA_REVERSAL**: Extreme vanna concentration suggests reversal

## Charm Signal Analysis

### Time Decay Effects
- **Positive Charm**: Delta increases as time passes (benefits longs)
- **Negative Charm**: Delta decreases as time passes (benefits shorts)
- **Decay Acceleration Zone**: Highest charm concentration near current price

### Charm Bias Calculation Factors

#### Factor 1: Charm Regime (Weight: ±1.0)
- Positive Charm Regime: +1.0 (bullish bias)
- Negative Charm Regime: -1.0 (bearish bias)

#### Factor 2: Time Decay Rate (Weight: ±0.6)
- High decay rate (>70%) with positive charm: +0.6 (bullish)
- High decay rate (>70%) with negative charm: -0.6 (bearish)
- Moderate decay rate (>40%): ±0.3 based on charm regime

#### Factor 3: Charm Concentration Near Price (Weight: ±0.4)
- High charm concentration (>50K) near current price: ±0.4 based on sign

### Signal Generation
- **BUY**: Strong bullish charm bias (≥50%) with medium/high confidence (≥40%)
- **SELL**: Strong bearish charm bias (≥50%) with medium/high confidence (≥40%)
- **HOLD**: Weak bias, low confidence, or neutral charm positioning

## Signal Strength Calculation

### Components (0-100 scale)
- **Gamma Exposure Strength**: Max 50 points (normalized by $1M threshold)
- **Volume Strength**: Max 50 points (normalized by 2000 volume threshold)

### Formula
```
Signal Strength = min(GEX_Strength + Volume_Strength, 100)
```

## Usage Guidelines

### Signal Interpretation
1. **BUY Signal + High Confidence**: Strong bullish opportunity - consider full position
2. **BUY Signal + Medium/Low Confidence**: Moderate bullish bias - consider smaller position
3. **SELL Signal + High Confidence**: Strong bearish opportunity - consider full position
4. **SELL Signal + Medium/Low Confidence**: Moderate bearish bias - consider smaller position
5. **HOLD Signal**: No clear directional bias - avoid new positions or wait for better setup

### Risk Management
- Position size based on confidence level (High = full size, Medium = 50%, Low = 25%)
- Monitor gamma regime changes throughout the day
- Use your own stop loss and profit target strategies
- Be aware of expiration effects and time decay

### Best Practices
1. **BUY signals work best**: Above zero GEX level in positive gamma regime
2. **SELL signals work best**: Below zero GEX level in negative gamma regime
3. Combine with volume confirmation for higher probability
4. Monitor zero GEX level for regime changes
5. Use EOD vanna/charm signals for additional confirmation
6. Adjust position sizing based on signal confidence

## Technical Implementation Details

### Data Flow Architecture
1. **Options Data Ingestion**: Real-time options chain data from market feeds
2. **Exposure Calculations**: Gamma, vanna, and charm exposure per strike
3. **Signal Generation**: Multi-factor analysis using volume-based gamma exposure (VGEX) for intraday signals
4. **Frontend Display**: Real-time signal updates with auto-refresh capability

### Gamma Exposure Types
- **GEX (Gamma Exposure)**: Uses Open Interest for calculations - used for general analysis
- **VGEX (Volume Gamma Exposure)**: Uses Volume for calculations - used for intraday signals
- **Intraday signals specifically use VGEX** to capture real-time trading activity rather than static positioning

### Key Algorithms

#### Zero GEX Strike Selection
```python
# Find closest strike to current price where gamma flips sign
if current_gex <= 0 <= next_gex or next_gex <= 0 <= current_gex:
    distance_current = abs(current_strike - current_price)
    distance_next = abs(next_strike - current_price)

    # Choose the strike closest to current price
    zero_strike = current_strike if distance_current <= distance_next else next_strike
```

#### Bias Score Calculation
```python
bias_score = (
    zero_gex_factor +           # ±1.0
    oi_dominance_factor +       # ±0.5
    volume_factor +             # ±0.3
    gamma_proximity_factor      # ±0.3
)

# Final bias determination
if bias_score > 0.5: direction = 'BULLISH'
elif bias_score < -0.5: direction = 'BEARISH'
else: direction = 'NEUTRAL'
```

### API Endpoints

#### Primary Signal Endpoints
- `GET /api/signals/{ticker}/{expiry}` - Intraday gamma signals
- `GET /api/eod-vanna-signals/{ticker}/{expiry}` - EOD vanna signals
- `GET /api/eod-charm-signals/{ticker}/{expiry}` - EOD charm signals

#### Response Format
```json
{
    "success": true,
    "signals": {
        "signal": {
            "action": "BUY|SELL|HOLD",
            "reason": "Strong bullish bias (75%) with high confidence",
            "confidence_level": "HIGH|MEDIUM|LOW",
            "bias_strength": 75,
            "confidence_score": 80
        },
        "bias": {
            "direction": "BULLISH|BEARISH|NEUTRAL",
            "strength": 0-100,
            "factors": ["factor1", "factor2"],
            "reason": "Score: X.XX based on N factors"
        },
        "confidence": {
            "score": 0-100,
            "level": "HIGH|MEDIUM|LOW",
            "factors": ["factor1", "factor2"]
        },
        "gamma_levels": {
            "zero_gex": 121.75,
            "max_positive_gex": {"strike": 125, "value": 1500000},
            "max_negative_gex": {"strike": 115, "value": -800000},
            "nearest_support": {"strike": 120, "gamma_exposure": 500000},
            "nearest_resistance": {"strike": 125, "gamma_exposure": 750000}
        },
        "market_regime": "POSITIVE_GAMMA|NEGATIVE_GAMMA|STRONG_POSITIVE_GAMMA|STRONG_NEGATIVE_GAMMA",
        "signal_strength": 0-100
    },
    "current_price": 122.50,
    "ticker": "SPY",
    "expiry_date": "2024-01-19",
    "timestamp": "2024-01-15T14:30:00"
}
```

## Error Handling and Data Validation

### Backend Safeguards
- Input validation for all data structures
- Safe mathematical operations with epsilon checks
- Graceful degradation for missing data fields
- Comprehensive exception handling with logging

### Frontend Resilience
- Data structure validation before rendering
- Error state management with retry mechanisms
- Auto-refresh error tracking and circuit breaking
- XSS protection for error message display

### Common Error Scenarios
1. **Empty Data Sets**: Returns neutral signals with low confidence
2. **Network Failures**: Displays retry button and stops auto-refresh
3. **Invalid Calculations**: Uses fallback values and logs warnings
4. **Malformed Responses**: Shows user-friendly error messages

## Performance Considerations

### Optimization Strategies
- Efficient data filtering and validation
- Minimal DOM manipulation in frontend
- Cached calculations where appropriate
- Progressive error handling to prevent cascading failures

### Monitoring and Debugging
- Comprehensive console logging for development
- Error tracking with context information
- Performance metrics for calculation times
- User-friendly error reporting
