<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Greek Terminal - Advanced Admin Control Center</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Pro -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- DataTables CSS with Extensions -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>

    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='dashboard.css') }}">

    <style>
        /* State-of-the-Art Greek Terminal Admin Theme */
        :root {
            --admin-primary: #0a0a0a;
            --admin-secondary: #1a1a1a;
            --admin-tertiary: #2a2a2a;
            --admin-quaternary: #3a3a3a;

            --admin-accent: #C0C0C0;
            --admin-accent-bright: #E8E8E8;
            --admin-accent-dim: #808080;

            --admin-success: #00C851;
            --admin-warning: #FF8800;
            --admin-danger: #FF4444;
            --admin-info: #33B5E5;

            --admin-glass: rgba(255, 255, 255, 0.05);
            --admin-glass-border: rgba(255, 255, 255, 0.1);
            --admin-glass-hover: rgba(255, 255, 255, 0.08);

            --admin-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --admin-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
            --admin-shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
            --admin-shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.6);

            --admin-glow: 0 0 20px rgba(192, 192, 192, 0.3);
            --admin-glow-strong: 0 0 30px rgba(192, 192, 192, 0.5);

            --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --admin-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            background:
                radial-gradient(circle at 20% 80%, rgba(192, 192, 192, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(192, 192, 192, 0.03) 0%, transparent 50%),
                linear-gradient(135deg, var(--admin-primary) 0%, #0f0f0f 25%, var(--admin-secondary) 50%, #0f0f0f 75%, var(--admin-primary) 100%);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(192, 192, 192, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(192, 192, 192, 0.02) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
        }

        /* Advanced Header Design */
        .admin-header {
            background: linear-gradient(135deg, var(--admin-secondary) 0%, var(--admin-tertiary) 50%, var(--admin-secondary) 100%);
            border-bottom: 1px solid var(--admin-glass-border);
            padding: 3rem 0 2rem;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--admin-accent), transparent);
        }

        .admin-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(192, 192, 192, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(192, 192, 192, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .admin-title-container {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .admin-logo {
            font-size: 4rem;
            color: var(--admin-accent);
            margin-bottom: 1rem;
            text-shadow: var(--admin-glow);
            animation: logoGlow 3s ease-in-out infinite;
        }

        @keyframes logoGlow {
            0%, 100% { text-shadow: var(--admin-glow); }
            50% { text-shadow: var(--admin-glow-strong); }
        }

        .admin-title {
            color: var(--text-primary);
            font-size: 3rem;
            font-weight: 900;
            margin: 0;
            background: linear-gradient(135deg, var(--admin-accent-bright), var(--admin-accent), var(--admin-accent-dim));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -1px;
        }

        .admin-subtitle {
            color: var(--admin-accent-dim);
            margin-top: 0.5rem;
            font-size: 1.2rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 3px;
            opacity: 0.9;
        }

        .admin-user-info {
            background: var(--admin-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--admin-glass-border);
            margin-top: 1rem;
            padding: 1rem 2rem;
            border-radius: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .user-info-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--admin-accent), var(--admin-accent-bright));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--admin-primary);
            font-size: 1.5rem;
            box-shadow: var(--admin-glow);
        }

        .user-details {
            text-align: left;
        }

        .user-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--admin-accent-bright);
            margin-bottom: 0.2rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: var(--admin-accent-dim);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .user-permissions {
            font-size: 0.75rem;
            color: var(--admin-accent);
            font-style: italic;
            margin-top: 0.2rem;
        }

        .admin-stats-bar {
            background: var(--admin-glass);
            backdrop-filter: blur(20px);
            border: 1px solid var(--admin-glass-border);
            margin-top: 1rem;
            padding: 1rem 2rem;
            border-radius: 0 0 24px 24px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .admin-stat-item {
            text-align: center;
            padding: 0.5rem 1rem;
        }

        .admin-stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--admin-accent);
            font-family: 'JetBrains Mono', monospace;
        }

        .admin-stat-label {
            font-size: 0.8rem;
            color: var(--admin-accent-dim);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 0.2rem;
        }

        /* Advanced Navigation */
        .admin-nav-container {
            background: var(--admin-glass);
            backdrop-filter: blur(30px);
            border: 1px solid var(--admin-glass-border);
            border-radius: 20px;
            margin: 2rem 0;
            padding: 1rem;
            box-shadow: var(--admin-shadow-lg);
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .admin-nav {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .admin-nav .nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .admin-nav .nav-link {
            color: var(--admin-accent-dim);
            font-weight: 600;
            padding: 1rem 2rem;
            border-radius: 16px;
            transition: var(--admin-transition);
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .admin-nav .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(192, 192, 192, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .admin-nav .nav-link:hover {
            color: var(--admin-accent);
            background: var(--admin-glass-hover);
            border-color: var(--admin-accent);
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-md);
        }

        .admin-nav .nav-link:hover::before {
            left: 100%;
        }

        .admin-nav .nav-link.active {
            color: var(--admin-accent-bright);
            background: linear-gradient(135deg, var(--admin-glass-hover), var(--admin-glass));
            border-color: var(--admin-accent);
            box-shadow: var(--admin-glow);
        }

        .admin-nav .nav-link i {
            font-size: 1.1rem;
        }

        /* Advanced Card System */
        .admin-card {
            background: var(--admin-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--admin-glass-border);
            border-radius: 24px;
            box-shadow: var(--admin-shadow-lg);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: var(--admin-transition);
            position: relative;
        }

        .admin-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--admin-accent), transparent);
        }

        .admin-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--admin-shadow-xl);
            border-color: var(--admin-accent);
        }

        .admin-card-header {
            background: linear-gradient(135deg, var(--admin-glass-hover), var(--admin-glass));
            border-bottom: 1px solid var(--admin-glass-border);
            padding: 2rem;
            position: relative;
        }

        .admin-card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--admin-accent), transparent);
        }

        .admin-card-title {
            margin: 0;
            font-weight: 700;
            font-size: 1.4rem;
            color: var(--admin-accent-bright);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .admin-card-title i {
            font-size: 1.2rem;
            color: var(--admin-accent);
        }

        .admin-card-body {
            padding: 2rem;
        }

        /* Legacy card support */
        .card {
            background: var(--admin-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--admin-glass-border);
            border-radius: 24px;
            box-shadow: var(--admin-shadow-lg);
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(135deg, var(--admin-glass-hover), var(--admin-glass));
            border-bottom: 1px solid var(--admin-glass-border);
            padding: 2rem;
            border-radius: 24px 24px 0 0;
            color: var(--admin-accent-bright);
        }

        .card-header h5 {
            margin: 0;
            font-weight: 700;
            color: var(--admin-accent-bright);
            font-size: 1.4rem;
        }

        .card-body {
            padding: 2rem;
        }

        /* Advanced Button System */
        .btn-admin {
            background: linear-gradient(135deg, var(--admin-glass), var(--admin-glass-hover));
            border: 2px solid var(--admin-accent);
            color: var(--admin-accent);
            font-weight: 700;
            padding: 0.875rem 2rem;
            border-radius: 16px;
            transition: var(--admin-transition);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .btn-admin::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(192, 192, 192, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-admin:hover {
            background: linear-gradient(135deg, var(--admin-glass-hover), var(--admin-glass));
            border-color: var(--admin-accent-bright);
            color: var(--admin-accent-bright);
            transform: translateY(-3px);
            box-shadow: var(--admin-glow);
        }

        .btn-admin:hover::before {
            left: 100%;
        }

        .btn-admin:active {
            transform: translateY(-1px);
        }

        .btn-success-admin {
            border-color: var(--admin-success);
            color: var(--admin-success);
        }

        .btn-success-admin:hover {
            border-color: var(--admin-success);
            color: var(--admin-success);
            box-shadow: 0 0 20px rgba(0, 200, 81, 0.4);
        }

        .btn-danger-admin {
            border-color: var(--admin-danger);
            color: var(--admin-danger);
        }

        .btn-danger-admin:hover {
            border-color: var(--admin-danger);
            color: var(--admin-danger);
            box-shadow: 0 0 20px rgba(255, 68, 68, 0.4);
        }

        .btn-warning-admin {
            border-color: var(--admin-warning);
            color: var(--admin-warning);
        }

        .btn-warning-admin:hover {
            border-color: var(--admin-warning);
            color: var(--admin-warning);
            box-shadow: 0 0 20px rgba(255, 136, 0, 0.4);
        }

        .btn-danger-admin {
            border-color: var(--admin-danger);
            color: var(--admin-danger);
        }

        .btn-danger-admin:hover {
            border-color: var(--admin-danger);
            color: var(--admin-danger);
            box-shadow: 0 0 20px rgba(255, 68, 68, 0.4);
        }

        .btn-info-admin {
            border-color: var(--admin-info);
            color: var(--admin-info);
        }

        .btn-info-admin:hover {
            border-color: var(--admin-info);
            color: var(--admin-info);
            box-shadow: 0 0 20px rgba(51, 181, 229, 0.4);
        }

        /* Advanced Form Controls */
        .form-control {
            background: var(--admin-glass);
            border: 2px solid var(--admin-glass-border);
            color: var(--text-primary);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            font-weight: 500;
            transition: var(--admin-transition);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            background: var(--admin-glass-hover);
            border-color: var(--admin-accent);
            color: var(--text-primary);
            box-shadow: 0 0 0 4px rgba(192, 192, 192, 0.15);
            transform: translateY(-2px);
        }

        .form-control::placeholder {
            color: var(--admin-accent-dim);
            font-style: italic;
        }

        .form-label {
            color: var(--admin-accent);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            margin-bottom: 0.75rem;
        }

        /* Advanced Table Design */
        .admin-table-container {
            background: var(--admin-glass);
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid var(--admin-glass-border);
            box-shadow: var(--admin-shadow-md);
        }

        .table {
            background: transparent;
            color: var(--text-primary);
            margin: 0;
            font-family: 'Inter', sans-serif;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-tertiary));
            color: var(--admin-accent-bright);
            border: none;
            border-bottom: 2px solid var(--admin-accent);
            padding: 1.5rem 1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            position: relative;
        }

        .table thead th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--admin-accent), transparent);
        }

        .table tbody td {
            background: var(--admin-glass);
            border: none;
            border-bottom: 1px solid var(--admin-glass-border);
            padding: 1.5rem 1rem;
            vertical-align: middle;
            transition: var(--admin-transition-fast);
            font-weight: 500;
        }

        .table tbody tr {
            transition: var(--admin-transition-fast);
        }

        .table tbody tr:hover {
            background: var(--admin-glass-hover);
            transform: scale(1.01);
            box-shadow: var(--admin-shadow-sm);
        }

        .table tbody tr:hover td {
            background: transparent;
        }

        /* DataTables enhancements */
        .dataTables_wrapper {
            font-family: 'Inter', sans-serif;
        }

        .dataTables_filter input {
            background: var(--admin-glass);
            border: 1px solid var(--admin-glass-border);
            border-radius: 12px;
            color: var(--text-primary);
            padding: 0.75rem 1rem;
        }

        .dataTables_length select {
            background: var(--admin-glass);
            border: 1px solid var(--admin-glass-border);
            border-radius: 8px;
            color: var(--text-primary);
            padding: 0.5rem;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Advanced Badge System */
        .admin-badge {
            padding: 0.5rem 1rem;
            border-radius: 12px;
            font-weight: 700;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 2px solid;
            backdrop-filter: blur(10px);
            transition: var(--admin-transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .admin-badge:hover {
            transform: scale(1.05);
        }

        .status-badge-active {
            background: rgba(0, 200, 81, 0.15) !important;
            color: var(--admin-success) !important;
            border-color: var(--admin-success) !important;
            box-shadow: 0 0 10px rgba(0, 200, 81, 0.3);
        }

        .status-badge-expired {
            background: rgba(255, 68, 68, 0.15) !important;
            color: var(--admin-danger) !important;
            border-color: var(--admin-danger) !important;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
        }

        .status-badge-warning {
            background: rgba(255, 136, 0, 0.15) !important;
            color: var(--admin-warning) !important;
            border-color: var(--admin-warning) !important;
            box-shadow: 0 0 10px rgba(255, 136, 0, 0.3);
        }

        .status-badge-info {
            background: rgba(51, 181, 229, 0.15) !important;
            color: var(--admin-info) !important;
            border-color: var(--admin-info) !important;
            box-shadow: 0 0 10px rgba(51, 181, 229, 0.3);
        }

        /* Legacy badge support */
        .badge {
            background: var(--admin-glass) !important;
            color: var(--admin-accent) !important;
            border: 1px solid var(--admin-glass-border) !important;
            padding: 0.5rem 1rem;
            border-radius: 12px;
            font-weight: 600;
        }

        .badge-success {
            background: rgba(0, 200, 81, 0.15) !important;
            color: var(--admin-success) !important;
            border-color: var(--admin-success) !important;
        }

        .badge-danger {
            background: rgba(255, 68, 68, 0.15) !important;
            color: var(--admin-danger) !important;
            border-color: var(--admin-danger) !important;
        }

        .badge-warning {
            background: rgba(255, 136, 0, 0.15) !important;
            color: var(--admin-warning) !important;
            border-color: var(--admin-warning) !important;
        }

        .dev-badge {
            background: linear-gradient(135deg, var(--accent-success), var(--accent-primary));
            color: #000000;
            padding: 0.3rem 0.8rem;
            border-radius: 6px;
            font-weight: 700;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stats-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all var(--transition-normal);
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--accent-success);
            margin: 0;
        }

        .stats-label {
            color: var(--text-tertiary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
        }

        .modal-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0;
            color: var(--text-primary);
        }

        .modal-body {
            color: var(--text-primary);
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Advanced License Key Display */
        .license-key-display {
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--admin-accent-bright);
            background: linear-gradient(135deg, var(--admin-secondary), var(--admin-tertiary));
            padding: 0.75rem 1.25rem;
            border-radius: 12px;
            border: 1px solid var(--admin-accent);
            letter-spacing: 1px;
            white-space: nowrap;
            display: inline-block;
            position: relative;
            overflow: hidden;
            transition: var(--admin-transition);
            box-shadow: var(--admin-shadow-sm);
        }

        .license-key-display::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(192, 192, 192, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .license-key-display:hover {
            transform: scale(1.05);
            box-shadow: var(--admin-glow);
            border-color: var(--admin-accent-bright);
        }

        .license-key-display:hover::before {
            left: 100%;
        }

        /* Developer Badge */
        .dev-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            font-size: 0.7rem;
            font-weight: 800;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
            animation: devGlow 2s ease-in-out infinite;
        }

        @keyframes devGlow {
            0%, 100% { box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3); }
            50% { box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6); }
        }

        .alert-admin {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            color: var(--text-primary);
        }

        .alert-success-admin {
            border-left: 4px solid var(--accent-success);
            background: rgba(192, 192, 192, 0.1);
        }

        .alert-danger-admin {
            border-left: 4px solid var(--accent-danger);
            background: rgba(255, 0, 0, 0.1);
        }

        /* Advanced Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: var(--admin-glass);
            border: 1px solid var(--admin-glass-border);
            border-radius: 20px;
            padding: 2.5rem;
            text-align: center;
            transition: var(--admin-transition);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(20px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--admin-accent), transparent);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--admin-shadow-xl);
            border-color: var(--admin-accent);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--admin-accent);
            margin-bottom: 1rem;
            font-family: 'JetBrains Mono', monospace;
            text-shadow: var(--admin-glow);
            animation: numberPulse 3s ease-in-out infinite;
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .stat-label {
            color: var(--admin-accent-dim);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.85rem;
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--admin-accent);
            margin-bottom: 1rem;
            opacity: 0.7;
        }

        /* Tab Content Animations */
        .tab-pane {
            transition: var(--admin-transition);
        }

        .tab-pane.active {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate__fadeIn {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-title {
                font-size: 2rem;
            }

            .admin-nav .nav {
                flex-direction: column;
                gap: 0.5rem;
            }

            .admin-nav .nav-link {
                padding: 0.75rem 1.5rem;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card {
                padding: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .admin-card-header,
            .admin-card-body {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .admin-header {
                padding: 2rem 0 1rem;
            }

            .admin-title {
                font-size: 1.5rem;
            }

            .admin-subtitle {
                font-size: 1rem;
                letter-spacing: 1px;
            }

            .admin-nav .nav-link {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Advanced Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="admin-title-container">
                <div class="admin-logo">
                    <i class="fas fa-crown"></i>
                </div>
                <h1 class="admin-title">
                    ADMIN CONTROL CENTER
                </h1>
                <p class="admin-subtitle">Greek Terminal License Management System</p>
            </div>

            <!-- User Info Bar -->
            <div class="admin-user-info">
                <div class="user-info-container">
                    <div class="user-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">{{ current_user_info.username }}</div>
                        <div class="user-role">{{ user_role }}</div>
                        {% if user_role == "Developer" %}
                            <div class="user-permissions">Full System Access</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Real-time Stats Bar -->
            <div class="admin-stats-bar">
                <div class="admin-stat-item">
                    <div class="admin-stat-number">{{ stats.total_licenses }}</div>
                    <div class="admin-stat-label">Total Licenses</div>
                </div>
                <div class="admin-stat-item">
                    <div class="admin-stat-number">{{ stats.active_licenses }}</div>
                    <div class="admin-stat-label">Active</div>
                </div>
                <div class="admin-stat-item">
                    <div class="admin-stat-number">{{ stats.expired_licenses }}</div>
                    <div class="admin-stat-label">Expired</div>
                </div>
                <div class="admin-stat-item">
                    <div class="admin-stat-number">{{ developers|length }}</div>
                    <div class="admin-stat-label">Developers</div>
                </div>
            </div>

            <!-- Developer Permissions Info -->
            <div class="alert alert-admin alert-success-admin mt-3">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <h6><i class="fas fa-crown me-2"></i>Developer Privileges (Highest Level)</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0 text-start">
                                    <li>✅ Full license management (add, edit, remove, extend)</li>
                                    <li>✅ Complete developer management (add, remove, protect)</li>
                                    <li>✅ System configuration (webhooks, cache)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0 text-start">
                                    <li>✅ Advanced system tools and cleanup</li>
                                    <li>✅ View all statistics and information</li>
                                    <li>✅ Complete administrative control</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Advanced Navigation -->
        <div class="admin-nav-container">
            <div class="admin-nav">
                <ul class="nav nav-pills justify-content-center" id="adminTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link active" id="dashboard-tab" data-bs-toggle="pill" data-bs-target="#dashboard" type="button" role="tab" aria-controls="dashboard" aria-selected="true">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="licenses-tab" data-bs-toggle="pill" data-bs-target="#licenses" type="button" role="tab" aria-controls="licenses" aria-selected="false">
                            <i class="fas fa-key"></i>
                            <span>Licenses</span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="developers-tab" data-bs-toggle="pill" data-bs-target="#developers" type="button" role="tab" aria-controls="developers" aria-selected="false">
                            <i class="fas fa-code"></i>
                            <span>Developers</span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link" id="settings-tab" data-bs-toggle="pill" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="false">
                            <i class="fas fa-cogs"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                    <li class="nav-item ms-auto" role="presentation">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-admin alert-{{ 'success' if category == 'success' else 'danger' }}-admin alert-dismissible fade show">
                        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Tab Content -->
        <div class="tab-content" id="adminTabContent">
            <!-- Dashboard Tab -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab" tabindex="0">
                <!-- Advanced Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="stat-number" id="totalLicenses">{{ stats.total_licenses }}</div>
                        <div class="stat-label">Total Licenses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number" id="activeLicenses">{{ stats.active_licenses }}</div>
                        <div class="stat-label">Active Licenses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number" id="expiredLicenses">{{ stats.expired_licenses }}</div>
                        <div class="stat-label">Expired Licenses</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number" id="expiringSoon">{{ stats.expiring_soon }}</div>
                        <div class="stat-label">Expiring Soon</div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h5 class="admin-card-title">
                            <i class="fas fa-chart-line"></i>
                            Recent Activity
                        </h5>
                    </div>
                    <div class="admin-card-body">
                        <div class="admin-table-container">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Action</th>
                                            <th>License</th>
                                            <th>User</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for activity in recent_activity %}
                                        <tr>
                                            <td>
                                                <i class="fas fa-{{ activity.icon }} me-2"></i>
                                                {{ activity.action }}
                                            </td>
                                            <td><code class="license-key-display">{{ activity.license_key }}</code></td>
                                            <td>{{ activity.username }}</td>
                                            <td>{{ activity.date }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Licenses Tab -->
            <div class="tab-pane fade" id="licenses" role="tabpanel" aria-labelledby="licenses-tab" tabindex="0">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="admin-card-title">
                                <i class="fas fa-key"></i>
                                License Management
                            </h5>
                            <div>
                                <button class="btn btn-admin btn-success-admin" data-bs-toggle="modal" data-bs-target="#addLicenseModal">
                                    <i class="fas fa-plus me-2"></i>Add License
                                </button>
                                <button class="btn btn-admin btn-info-admin" onclick="refreshLicenses()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh
                                </button>
                                <button class="btn btn-admin btn-warning-admin" onclick="refreshCache()">
                                    <i class="fas fa-database me-2"></i>Refresh Cache
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="admin-card-body">
                        <div class="admin-table-container">
                            <div class="table-responsive">
                                <table id="licensesTable" class="table">
                                    <thead>
                                        <tr>
                                            <th>License Key</th>
                                            <th>Discord ID</th>
                                            <th>Username</th>
                                            <th>Expiry Date</th>
                                            <th>Status</th>
                                            <th>Days Remaining</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for license in licenses %}
                                        <tr>
                                            <td>
                                                <span class="license-key-display">{{ license.license_key }}</span>
                                            </td>
                                            <td>
                                                <span class="admin-badge status-badge-info">{{ license.discord_id }}</span>
                                            </td>
                                            <td>
                                                <strong>{{ license.username }}</strong>
                                                {% if license.username in developers %}
                                                    <span class="dev-badge ms-2">DEV</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ license.expiry_date }}</td>
                                            <td>
                                                {% if license.is_expired %}
                                                    <span class="admin-badge status-badge-expired">
                                                        <i class="fas fa-times-circle"></i>
                                                        Expired
                                                    </span>
                                                {% elif license.days_remaining <= 7 %}
                                                    <span class="admin-badge status-badge-warning">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        Expiring Soon
                                                    </span>
                                                {% else %}
                                                    <span class="admin-badge status-badge-active">
                                                        <i class="fas fa-check-circle"></i>
                                                        Active
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if license.is_expired %}
                                                    <span style="color: var(--admin-danger); font-weight: 600;">Expired</span>
                                                {% else %}
                                                    <span style="color: var(--admin-accent); font-weight: 600;">{{ license.days_remaining }} days</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-admin btn-warning-admin" onclick="extendLicense('{{ license.license_key }}', '{{ license.username }}')">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                    <button class="btn btn-admin btn-info-admin" onclick="editLicense('{{ license.license_key }}', '{{ license.username }}', '{{ license.discord_id }}')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-admin btn-danger-admin" onclick="removeLicense('{{ license.license_key }}', '{{ license.username }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Developers Tab -->
            <div class="tab-pane fade" id="developers" role="tabpanel" aria-labelledby="developers-tab" tabindex="0">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-code me-2"></i>Developer Management</h5>
                            <div>
                                <button class="btn btn-admin btn-success-admin" data-bs-toggle="modal" data-bs-target="#addDeveloperModal">
                                    <i class="fas fa-user-plus me-2"></i>Add Developer
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-admin alert-info-admin">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Developer Note:</strong> You have full system access including developer management and protection settings. Developers are the highest level of access.
                        </div>

                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>License Key</th>
                                        <th>Discord ID</th>
                                        <th>Status</th>
                                        <th>Added Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dev_info in developers_info %}
                                    {% set dev_license = licenses | selectattr('username', 'equalto', dev_info.username) | first %}
                                    <tr>
                                        <td>
                                            <strong>{{ dev_info.username }}</strong>
                                            <span class="dev-badge ms-2">DEV</span>
                                            {% if dev_info.protected %}
                                                <span class="badge" style="background: rgba(255, 215, 0, 0.2); color: #FFD700; border: 1px solid #FFD700; font-size: 0.7rem;">
                                                    <i class="fas fa-shield-alt me-1"></i>PROTECTED
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dev_license %}
                                                <span class="badge" style="background: var(--bg-secondary); color: var(--accent-success); font-family: monospace; font-size: 0.9rem; padding: 0.5rem 0.8rem;">{{ dev_license.license_key }}</span>
                                            {% else %}
                                                <span style="color: var(--text-muted);">No License</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dev_license %}
                                                {{ dev_license.discord_id }}
                                            {% else %}
                                                <span style="color: var(--text-muted);">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="status-badge-active">Active Developer</span>
                                        </td>
                                        <td>{{ dev_info.added_date }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                {% if dev_license %}
                                                    <button class="btn btn-admin btn-warning-admin btn-sm" onclick="extendLicense('{{ dev_license.license_key }}', '{{ dev_info.username }}')">
                                                        <i class="fas fa-clock me-1"></i>Extend
                                                    </button>
                                                {% endif %}
                                                {% if dev_info.protected %}
                                                    <button class="btn btn-admin btn-info-admin btn-sm" onclick="toggleProtection('{{ dev_info.username }}', false)">
                                                        <i class="fas fa-unlock me-1"></i>Unprotect
                                                    </button>
                                                {% else %}
                                                    <button class="btn btn-admin btn-success-admin btn-sm" onclick="toggleProtection('{{ dev_info.username }}', true)">
                                                        <i class="fas fa-shield-alt me-1"></i>Protect
                                                    </button>
                                                {% endif %}
                                                {% if not dev_info.protected %}
                                                    <button class="btn btn-admin btn-danger-admin btn-sm" onclick="removeDeveloper('{{ dev_info.username }}')">
                                                        <i class="fas fa-user-minus me-1"></i>Remove
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel" aria-labelledby="settings-tab" tabindex="0">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog me-2"></i>System Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">GitHub Repository</label>
                                    <input type="text" class="form-control" value="{{ config.github_repo }}" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">License File</label>
                                    <input type="text" class="form-control" value="{{ config.license_file }}" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Admin Users</label>
                                    <textarea class="form-control" rows="3" readonly>{{ config.admin_users | join(', ') }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bell me-2"></i>Discord Webhook</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Webhook Status</label>
                                    <div>
                                        {% if config.webhook_enabled %}
                                            <span class="status-badge-active">Enabled</span>
                                        {% else %}
                                            <span class="status-badge-expired">Disabled</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Login Notifications</label>
                                    <div>
                                        {% if config.login_notifications %}
                                            <span class="status-badge-active">Enabled</span>
                                        {% else %}
                                            <span class="status-badge-warning">Disabled</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <button class="btn btn-admin" onclick="testWebhook()">
                                    <i class="fas fa-test-tube me-2"></i>Test Webhook
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Admin Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-admin w-100 mb-2" onclick="refreshLicenses()">
                                    <i class="fas fa-sync-alt me-2"></i>Refresh Licenses
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-admin w-100 mb-2" onclick="exportLicenses()">
                                    <i class="fas fa-download me-2"></i>Export Licenses
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-admin btn-warning-admin w-100 mb-2" onclick="cleanupExpired()">
                                    <i class="fas fa-broom me-2"></i>Cleanup Expired
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add License Modal -->
    <div class="modal fade" id="addLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Add New License
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addLicenseForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="discordId" class="form-label">Discord ID</label>
                            <input type="text" class="form-control" id="discordId" required
                                   pattern="\d+" title="Discord ID must be numeric">
                        </div>
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" required
                                   pattern="[a-zA-Z0-9_]+" title="Username can only contain letters, numbers, and underscores">
                        </div>
                        <div class="mb-3">
                            <label for="daysValid" class="form-label">License Duration (Days)</label>
                            <select class="form-control" id="daysValid">
                                <option value="7">7 Days (Trial)</option>
                                <option value="30" selected>30 Days (Monthly)</option>
                                <option value="90">90 Days (Quarterly)</option>
                                <option value="365">365 Days (Yearly)</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                        <div class="mb-3" id="customDaysDiv" style="display: none;">
                            <label for="customDays" class="form-label">Custom Days</label>
                            <input type="number" class="form-control" id="customDays" min="1" max="3650">
                        </div>
                        <div id="generatedLicenseKey" style="display: none;">
                            <label class="form-label">Generated License Key:</label>
                            <div class="license-key-display" id="licenseKeyDisplay"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-admin" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-admin btn-success-admin">
                            <i class="fas fa-plus me-2"></i>Add License
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Developer Modal -->
    <div class="modal fade" id="addDeveloperModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Add Developer
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="addDeveloperForm">
                    <div class="modal-body">
                        <div class="alert alert-admin alert-info-admin">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> Developers have the highest level of system access including full license and developer management capabilities.
                        </div>

                        <div class="mb-3">
                            <label for="devUsername" class="form-label">Select User to Promote</label>
                            <select class="form-control" id="devUsername" required>
                                <option value="">Select a user...</option>
                                {% for license in licenses %}
                                    {% if license.username not in developers %}
                                        <option value="{{ license.username }}" data-license="{{ license.license_key }}" data-discord="{{ license.discord_id }}">
                                            {{ license.username }} ({{ license.license_key }})
                                        </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>

                        <div id="devUserInfo" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">License Key</label>
                                <input type="text" class="form-control" id="devLicenseKey" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Discord ID</label>
                                <input type="text" class="form-control" id="devDiscordId" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="devProtected">
                                <label class="form-check-label" for="devProtected">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    <strong>Mark as Protected</strong>
                                    <small class="text-muted d-block">Protected developers cannot be removed by other administrators</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-admin" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-admin btn-success-admin">
                            <i class="fas fa-user-plus me-2"></i>Promote to Developer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Extend License Modal -->
    <div class="modal fade" id="extendLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-clock me-2"></i>Extend License
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="extendLicenseForm">
                    <div class="modal-body">
                        <p>Extending license for: <strong id="extendUsername"></strong></p>
                        <p>License Key: <code class="license-key-display" id="extendLicenseKey"></code></p>

                        <div class="mb-3">
                            <label for="additionalDays" class="form-label">Additional Days</label>
                            <select class="form-control" id="additionalDays">
                                <option value="7">7 Days</option>
                                <option value="30" selected>30 Days</option>
                                <option value="90">90 Days</option>
                                <option value="365">365 Days</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-admin" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-admin btn-warning-admin">
                            <i class="fas fa-clock me-2"></i>Extend License
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit License Modal -->
    <div class="modal fade" id="editLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Edit License
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="editLicenseForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">License Key</label>
                            <input type="text" class="form-control" id="editLicenseKey" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="editUsername" required
                                   pattern="[a-zA-Z0-9_]+" title="Username can only contain letters, numbers, and underscores">
                        </div>
                        <div class="mb-3">
                            <label for="editDiscordId" class="form-label">Discord ID</label>
                            <input type="text" class="form-control" id="editDiscordId" required
                                   pattern="\d+" title="Discord ID must be numeric">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-admin" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-admin btn-success-admin">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize DataTable and Tab Navigation
        $(document).ready(function() {
            // Initialize DataTable
            $('#licensesTable').DataTable({
                pageLength: 25,
                order: [[3, 'asc']], // Sort by expiry date
                columnDefs: [
                    { orderable: false, targets: [6] } // Disable sorting for actions column
                ]
            });

            // Initialize Bootstrap tabs
            var triggerTabList = [].slice.call(document.querySelectorAll('#adminTabs a[data-bs-toggle="pill"]'));
            triggerTabList.forEach(function (triggerEl) {
                var tabTrigger = new bootstrap.Tab(triggerEl);

                triggerEl.addEventListener('click', function (event) {
                    event.preventDefault();
                    tabTrigger.show();
                });
            });

            // Add visual feedback for tab switching
            $('a[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("data-bs-target");
                console.log('Switched to tab:', target);

                // Add animation to the active tab content
                $(target).addClass('animate__fadeIn');
                setTimeout(function() {
                    $(target).removeClass('animate__fadeIn');
                }, 500);
            });

            // Ensure proper tab activation on page load
            var hash = window.location.hash;
            if (hash) {
                var tabTrigger = document.querySelector('#adminTabs a[data-bs-target="' + hash + '"]');
                if (tabTrigger) {
                    var tab = new bootstrap.Tab(tabTrigger);
                    tab.show();
                }
            }
        });

        // Handle custom days selection
        document.getElementById('daysValid').addEventListener('change', function() {
            const customDiv = document.getElementById('customDaysDiv');
            if (this.value === 'custom') {
                customDiv.style.display = 'block';
            } else {
                customDiv.style.display = 'none';
            }
        });

        // Handle developer selection
        document.getElementById('devUsername').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const infoDiv = document.getElementById('devUserInfo');

            if (this.value) {
                document.getElementById('devLicenseKey').value = selectedOption.dataset.license;
                document.getElementById('devDiscordId').value = selectedOption.dataset.discord;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        });

        // Add license form submission
        document.getElementById('addLicenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const discordId = document.getElementById('discordId').value;
            const username = document.getElementById('username').value;
            const daysValid = document.getElementById('daysValid').value;
            const customDays = document.getElementById('customDays').value;
            
            const days = daysValid === 'custom' ? customDays : daysValid;
            
            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';
            submitBtn.disabled = true;
            
            fetch('/admin/add-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    discord_id: discordId,
                    username: username,
                    days_valid: parseInt(days)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show generated license key
                    document.getElementById('licenseKeyDisplay').textContent = data.license_key;
                    document.getElementById('generatedLicenseKey').style.display = 'block';
                    
                    // Reset form
                    e.target.reset();
                    document.getElementById('customDaysDiv').style.display = 'none';
                    
                    // Refresh page after a delay
                    setTimeout(() => {
                        location.reload();
                    }, 3000);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Add developer form submission
        document.getElementById('addDeveloperForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('devUsername').value;
            const protected = document.getElementById('devProtected').checked;

            if (!username) {
                alert('Please select a user to promote');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Promoting...';
            submitBtn.disabled = true;

            fetch('/admin/add-developer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    protected: protected
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('addDeveloperModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Extend license function
        function extendLicense(licenseKey, username) {
            document.getElementById('extendLicenseKey').textContent = licenseKey;
            document.getElementById('extendUsername').textContent = username;

            const modal = new bootstrap.Modal(document.getElementById('extendLicenseModal'));
            modal.show();
        }

        // Remove developer function
        function removeDeveloper(username) {
            if (confirm(`Are you sure you want to remove ${username} from developer status?\n\nThis action will revoke their developer privileges but they will retain their license access.`)) {
                fetch('/admin/remove-developer', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', `${username} has been removed from developer status`);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert('error', 'Failed to remove developer: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', 'Error removing developer: ' + error);
                });
            }
        }

        // Toggle developer protection function
        function toggleProtection(username, protect) {
            const action = protect ? 'protect' : 'unprotect';
            const actionText = protect ? 'Protected developers cannot be removed by other administrators.' : 'This developer can be removed by administrators.';

            if (confirm(`Are you sure you want to ${action} ${username}?\n\n${actionText}`)) {
                fetch('/admin/toggle-developer-protection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        protected: protect
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const statusText = protect ? 'protected' : 'unprotected';
                        showAlert('success', `${username} has been marked as ${statusText}`);
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert('error', 'Failed to change protection status: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', 'Error changing protection status: ' + error);
                });
            }
        }

        // Edit license function
        function editLicense(licenseKey, username, discordId) {
            document.getElementById('editLicenseKey').value = licenseKey;
            document.getElementById('editUsername').value = username;
            document.getElementById('editDiscordId').value = discordId;

            const modal = new bootstrap.Modal(document.getElementById('editLicenseModal'));
            modal.show();
        }

        // Extend license form submission
        document.getElementById('extendLicenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const licenseKey = document.getElementById('extendLicenseKey').textContent;
            const additionalDays = document.getElementById('additionalDays').value;
            
            fetch('/admin/extend-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    license_key: licenseKey,
                    additional_days: parseInt(additionalDays)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('extendLicenseModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });

        // Edit license form submission
        document.getElementById('editLicenseForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const licenseKey = document.getElementById('editLicenseKey').value;
            const username = document.getElementById('editUsername').value;
            const discordId = document.getElementById('editDiscordId').value;

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitBtn.disabled = true;

            fetch('/admin/edit-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    license_key: licenseKey,
                    username: username,
                    discord_id: discordId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    bootstrap.Modal.getInstance(document.getElementById('editLicenseModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Remove license function
        function removeLicense(licenseKey, username) {
            if (confirm(`Are you sure you want to remove the license for ${username}?\n\nLicense Key: ${licenseKey}\n\nThis action cannot be undone.`)) {
                fetch('/admin/remove-license', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        license_key: licenseKey
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }

        // Utility functions
        function refreshLicenses() {
            location.reload();
        }

        function refreshCache() {
            if (confirm('This will refresh the license cache and may take a moment. Continue?')) {
                fetch('/admin/refresh-cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', 'Cache refreshed successfully! Source: ' + (data.source || 'unknown'));
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert('error', 'Failed to refresh cache: ' + data.error);
                    }
                })
                .catch(error => {
                    showAlert('error', 'Error refreshing cache: ' + error);
                });
            }
        }

        function testWebhook() {
            fetch('/admin/test-webhook', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Webhook test successful! Check your Discord channel.');
                } else {
                    alert('Webhook test failed: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        }

        function exportLicenses() {
            window.open('/admin/export-licenses', '_blank');
        }

        function cleanupExpired() {
            if (confirm('Are you sure you want to remove all expired licenses?\n\nThis action cannot be undone.')) {
                fetch('/admin/cleanup-expired', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Removed ${data.removed_count} expired licenses.`);
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
    </script>

    <!-- Copyright Footer -->
    <footer class="copyright-footer">
        <div class="container-fluid">
            <div class="text-center text-muted">
                <small>Copyright © 2025 Greek Terminal. All rights reserved.</small>
            </div>
        </div>
    </footer>

    <style>
        .copyright-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background-color: rgba(33, 37, 41, 0.95);
            border-top: 1px solid #495057;
            padding: 8px 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .copyright-footer small {
            font-size: 0.75rem;
            color: #6c757d;
        }

        /* Add bottom padding to body to prevent content overlap */
        body {
            padding-bottom: 40px;
        }
    </style>
</body>
</html>
