#!/usr/bin/env python3
"""
License System Setup Script for Greek Terminal
Interactive setup and testing tool
"""

import os
import sys
import secrets
from datetime import datetime, timedelta

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("🔐 Greek Terminal License System Setup")
    print("=" * 60)
    print()

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        ('requests', 'requests'),
        ('github', 'PyGithub'),
        ('cryptography', 'cryptography'),
        ('flask', 'Flask')
    ]
    
    # Optimization 33: Use list comprehension for missing packages detection
    missing_packages = []

    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            print(f"   ✅ {pip_name}")
        except ImportError:
            print(f"   ❌ {pip_name} (missing)")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("   ✅ All dependencies installed")
    return True

def get_user_input(prompt, default=None, required=True):
    """Get user input with optional default"""
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        value = input(full_prompt).strip()
        
        if value:
            return value
        elif default:
            return default
        elif not required:
            return ""
        else:
            print("   This field is required. Please enter a value.")

def setup_github_config():
    """Setup GitHub configuration"""
    print("\n🐙 GitHub Configuration")
    print("You need a GitHub repository to store license data.")
    print()
    
    # Get GitHub token
    print("1. GitHub Personal Access Token:")
    print("   - Go to GitHub Settings → Developer settings → Personal access tokens")
    print("   - Generate token with 'repo' permissions")
    print()
    
    github_token = get_user_input("Enter your GitHub token")
    
    # Get repository
    print("\n2. GitHub Repository:")
    print("   - Create a private repository for license storage")
    print("   - Format: username/repository-name")
    print()
    
    github_repo = get_user_input("Enter repository (username/repo)")
    
    # Validate repository format
    if '/' not in github_repo:
        print("   ⚠️ Repository format should be 'username/repo'")
        github_repo = get_user_input("Enter repository (username/repo)")
    
    return github_token, github_repo

def setup_security_config():
    """Setup security configuration"""
    print("\n🔒 Security Configuration")
    
    # Generate session secret
    session_secret = secrets.token_hex(32)
    print(f"Generated session secret: {session_secret[:16]}...")
    
    # Admin users
    print("\nAdmin users (comma-separated usernames):")
    admin_users = get_user_input("Admin usernames", "admin,administrator")
    
    return session_secret, admin_users

def test_github_connection(github_token, github_repo):
    """Test GitHub connection"""
    print("\n🧪 Testing GitHub connection...")
    
    try:
        from github import Github
        
        g = Github(github_token)
        repo = g.get_repo(github_repo)
        
        print(f"   ✅ Connected to repository: {repo.full_name}")
        print(f"   📊 Repository info: {repo.description or 'No description'}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ GitHub connection failed: {e}")
        return False

def create_env_file(github_token, github_repo, session_secret, admin_users):
    """Create .env file with configuration"""
    print("\n📝 Creating .env file...")
    
    env_content = f"""# Greek Terminal License System Configuration
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# Required Configuration
GITHUB_TOKEN={github_token}
GITHUB_REPO={github_repo}
LICENSE_SESSION_SECRET={session_secret}

# Optional Configuration
LICENSE_FILE_PATH=licenses/license_data.enc
LICENSE_CACHE_HOURS=1
LICENSE_OFFLINE_GRACE_HOURS=24
LICENSE_SESSION_TIMEOUT_HOURS=24
LICENSE_MAX_LOGIN_ATTEMPTS=5
LICENSE_LOCKOUT_MINUTES=15
LICENSE_ADMIN_USERS={admin_users}

# Feature Flags
ENABLE_LICENSE_SYSTEM=true
ENABLE_TRIAL_FALLBACK=true
ENABLE_OFFLINE_MODE=true

# Logging
LOG_LICENSE_ATTEMPTS=true
LOG_ADMIN_ACTIONS=true
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("   ✅ .env file created successfully")
        return True
    except Exception as e:
        print(f"   ❌ Failed to create .env file: {e}")
        return False

def create_first_license():
    """Create the first admin license"""
    print("\n👤 Creating your admin license...")
    
    # Get user info
    discord_id = get_user_input("Enter your Discord ID (numeric)")
    username = get_user_input("Enter your username", "admin")
    
    # Validate Discord ID
    if not discord_id.isdigit():
        print("   ⚠️ Discord ID should be numeric")
        discord_id = get_user_input("Enter your Discord ID (numeric)")
    
    try:
        # Import after setting environment
        from license_admin import get_license_admin
        
        admin = get_license_admin()
        result = admin.add_license(discord_id, username, 365)  # 1 year license
        
        if result['success']:
            print(f"   ✅ License created successfully!")
            print(f"   🔑 License Key: {result['license_key']}")
            print(f"   📋 Full License: {result['license_line']}")
            print()
            print("   💾 Save this license key - you'll need it to login!")
            return result['license_key']
        else:
            print(f"   ❌ Failed to create license: {result['error']}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error creating license: {e}")
        return None

def test_license_system(license_key):
    """Test the license system"""
    print("\n🧪 Testing license system...")
    
    try:
        from license_manager import get_license_manager
        
        manager = get_license_manager()
        
        # Test license validation
        result = manager.validate_license(license_key)
        
        if result['valid']:
            print("   ✅ License validation successful")
            print(f"   👤 Username: {result['license_info']['username']}")
            print(f"   📅 Expires: {result['license_info']['expiry_str']}")
            print(f"   ⏰ Days remaining: {result['days_remaining']}")
            return True
        else:
            print(f"   ❌ License validation failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"   ❌ License system test failed: {e}")
        return False

def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup Complete!")
    print()
    print("Next steps:")
    print("1. Start the application:")
    print("   python app.py")
    print()
    print("2. Open your browser and go to:")
    print("   http://localhost:5000/login")
    print()
    print("3. Login with your license key")
    print()
    print("4. Access the admin dashboard:")
    print("   http://localhost:5000/admin")
    print()
    print("5. Read the full documentation:")
    print("   LICENSE_SYSTEM_SETUP.md")
    print()
    print("🔐 Your license system is ready to use!")

def main():
    """Main setup function"""
    print_header()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first.")
        sys.exit(1)
    
    # Setup GitHub configuration
    github_token, github_repo = setup_github_config()
    
    # Test GitHub connection
    if not test_github_connection(github_token, github_repo):
        print("\n❌ Please check your GitHub configuration and try again.")
        sys.exit(1)
    
    # Setup security configuration
    session_secret, admin_users = setup_security_config()
    
    # Create .env file
    if not create_env_file(github_token, github_repo, session_secret, admin_users):
        print("\n❌ Failed to create configuration file.")
        sys.exit(1)
    
    # Set environment variables for this session
    os.environ['GITHUB_TOKEN'] = github_token
    os.environ['GITHUB_REPO'] = github_repo
    os.environ['LICENSE_SESSION_SECRET'] = session_secret
    os.environ['LICENSE_ADMIN_USERS'] = admin_users
    
    # Create first license
    license_key = create_first_license()
    
    if license_key:
        # Test license system
        test_license_system(license_key)
        
        # Print next steps
        print_next_steps()
    else:
        print("\n⚠️ Setup completed but license creation failed.")
        print("You can create licenses manually using the admin tools.")

if __name__ == '__main__':
    main()
